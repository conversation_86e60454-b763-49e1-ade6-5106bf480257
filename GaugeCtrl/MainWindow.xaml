<Window x:Class="GaugeCtrl.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:hc="https://handyorg.github.io/handycontrol"
        xmlns:local="clr-namespace:GaugeCtrl"
        xmlns:vm="clr-namespace:GaugeCtrl.ViewModels"
        xmlns:views="clr-namespace:GaugeCtrl.Views"
        xmlns:conv="clr-namespace:ValueConverters;assembly=ValueConverters"
        xmlns:converter="clr-namespace:GaugeCtrl.Converter"
        mc:Ignorable="d"
        Title="{Binding WindowTitle}" Height="768" Width="1366">
    <Window.DataContext>
        <vm:MainWindowViewModel />
    </Window.DataContext>

    <Window.Resources>

        <conv:BoolToBrushConverter x:Key="BoolToBrushConverter" TrueValue="Green"  FalseValue="Red"></conv:BoolToBrushConverter>
        <conv:BoolToStringConverter x:Key="BoolToStringConverter" TrueValue="已连接" FalseValue="已断开"></conv:BoolToStringConverter>
        <conv:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter" TrueValue="Visible" FalseValue="Collapsed"></conv:BoolToVisibilityConverter>

        <!-- 字节值转换器 -->
        <local:ByteConverter x:Key="ByteConverter" />

        <!-- 轴类型转换器 -->
        <converter:AxisTypeToStringConverter x:Key="AxisTypeToStringConverter" />

        <!-- 组合转换器：字符串比较 -> 布尔值 -> 可见性 -->
        <conv:ValueConverterGroup x:Key="StringToVisibilityConverter">
            <local:StringToBooleanConverter />
            <conv:BoolToVisibilityConverter TrueValue="Visible" FalseValue="Collapsed" />
        </conv:ValueConverterGroup>
        <!-- 通用按钮模板 -->
        <ControlTemplate x:Key="RoundedButtonTemplate" TargetType="Button">
            <Border Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}"
                    CornerRadius="4"
                    Padding="{TemplateBinding Padding}">
                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
            </Border>
        </ControlTemplate>

        <!-- 导航RadioButton样式 -->
        <Style x:Key="RadioButtonClean" TargetType="RadioButton">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Padding" Value="15,8" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="HorizontalContentAlignment" Value="Center" />
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="Foreground" Value="#666" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="RadioButton">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="Center" />
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsChecked" Value="True">
                    <Setter Property="Background" Value="#2196F3" />
                    <Setter Property="Foreground" Value="White" />
                    <Setter Property="BorderBrush" Value="#2196F3" />
                </Trigger>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E3F2FD" />
                </Trigger>
                <MultiTrigger>
                    <MultiTrigger.Conditions>
                        <Condition Property="IsChecked" Value="True" />
                        <Condition Property="IsMouseOver" Value="True" />
                    </MultiTrigger.Conditions>
                    <Setter Property="Background" Value="#1976D2" />
                </MultiTrigger>
            </Style.Triggers>
        </Style>

        <!-- 默认按钮样式 -->
        <Style x:Key="DefaultButtonStyle" TargetType="Button">
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Padding" Value="15,8" />
            <Setter Property="Margin" Value="0,0,0,10" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Template" Value="{StaticResource RoundedButtonTemplate}" />
        </Style>

        <!-- 主要按钮样式 -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource DefaultButtonStyle}">
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Background" Value="#F5F5F5" />
            <Setter Property="Foreground" Value="#666" />
            <Setter Property="BorderBrush" Value="#E0E0E0" />
            <Setter Property="BorderThickness" Value="1" />
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E3F2FD" />
                    <Setter Property="BorderThickness" Value="1" />
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 闪烁按钮样式 -->
        <Style x:Key="BlinkingButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#FF9800" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderBrush" Value="#FF9800" />
            <Style.Triggers>
                <Trigger Property="IsEnabled" Value="True">
                    <Trigger.EnterActions>
                        <BeginStoryboard>
                            <Storyboard RepeatBehavior="Forever">
                                <ColorAnimation Storyboard.TargetProperty="(Button.Background).(SolidColorBrush.Color)"
                                              From="#FF9800" To="#FFC107" Duration="0:0:0.8" AutoReverse="True" />
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.EnterActions>
                </Trigger>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F57C00" />
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 主要文件按钮样式 -->
        <Style x:Key="PrimaryFileButtonStyle" TargetType="Button" >
            <Setter Property="Background" Value="#2196F3" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="BorderThickness" Value="0" />
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1976D2" />
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 信息显示区域样式 -->
        <Style x:Key="InfoLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Margin" Value="0,0,0,5" />
        </Style>

        <Style x:Key="InfoTextBoxStyle" TargetType="TextBox">
            <Setter Property="IsReadOnly" Value="True" />
            <Setter Property="HorizontalContentAlignment" Value="Center"></Setter>
            <Setter Property="Background" Value="#F8F8F8" />
            <Setter Property="Padding" Value="10,8" />
            <Setter Property="BorderBrush" Value="#E0E0E0" />
        </Style>

        <!-- ToggleButtonSwitch 样式 -->
        <Style x:Key="ToggleButtonSwitch" TargetType="ToggleButton">
            <Setter Property="Width" Value="50" />
            <Setter Property="Height" Value="25" />
            <Setter Property="Background" Value="#CCCCCC" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToggleButton">
                        <Border x:Name="Border" Background="{TemplateBinding Background}" CornerRadius="12.5">
                            <Ellipse x:Name="Thumb" Width="21" Height="21" Fill="White" HorizontalAlignment="Left" Margin="2,2,0,0">
                                <Ellipse.RenderTransform>
                                    <TranslateTransform x:Name="ThumbTransform" X="0" />
                                </Ellipse.RenderTransform>
                            </Ellipse>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#4CAF50" />
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="ThumbTransform"
                                                           Storyboard.TargetProperty="X"
                                                           To="25" Duration="0:0:0.2" />
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="ThumbTransform"
                                                           Storyboard.TargetProperty="X"
                                                           To="0" Duration="0:0:0.2" />
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250" />
            <ColumnDefinition />
        </Grid.ColumnDefinitions>
        


        <!-- 左侧面板 -->
        <Border Grid.Column="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,1,0" Margin="3" >
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!-- Logo区域 -->
                <Image Grid.Row="0" Source="/Images/modular.png" Width="200"
                       Margin="20,10,20,20" HorizontalAlignment="Center" />

                <!-- 可滚动内容区域 -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                    <StackPanel>

                        <!-- 驱动器型号 -->
                        <StackPanel Margin="20,0,20,10">
                            <TextBlock Text="驱动器型号" Style="{StaticResource InfoLabelStyle}" />
                            <TextBox Text="MDLDRV.C40-200L-BR" Style="{StaticResource InfoTextBoxStyle}" />
                        </StackPanel>

                        <!-- 固件版本 -->
                        <StackPanel Margin="20,0,20,10">
                            <TextBlock Text="固件版本" Style="{StaticResource InfoLabelStyle}" />
                            <TextBox Text="1.2.2" Style="{StaticResource InfoTextBoxStyle}" />
                        </StackPanel>

                        <!-- 功能按钮区域 -->
                        <StackPanel Margin="20,20,20,20" VerticalAlignment="Top">
                            <RadioButton Content="🔌 连接设备"
                                        Style="{StaticResource RadioButtonClean}"
                                        IsChecked="{Binding SelectedPage, Converter={x:Static local:StringToBooleanConverter.Instance}, ConverterParameter=ConnectDevice}"
                                        Command="{Binding SwitchViewCommand}"
                                        CommandParameter="ConnectDevice" />
                            <RadioButton Content="🔧 参数设定"
                                        Style="{StaticResource RadioButtonClean}"
                                        IsChecked="{Binding SelectedPage, Converter={x:Static local:StringToBooleanConverter.Instance}, ConverterParameter=ParameterSettings}"
                                        Command="{Binding SwitchViewCommand}"
                                        CommandParameter="ParameterSettings" />
                            <RadioButton Content="🦾 手动调试"
                                        Style="{StaticResource RadioButtonClean}"
                                        IsChecked="{Binding SelectedPage, Converter={x:Static local:StringToBooleanConverter.Instance}, ConverterParameter=MotionSimulation}"
                                        Command="{Binding SwitchViewCommand}"
                                        CommandParameter="MotionSimulation" />
                            <RadioButton Content="📈 状态监控"
                                        Style="{StaticResource RadioButtonClean}"
                                        IsChecked="{Binding SelectedPage, Converter={x:Static local:StringToBooleanConverter.Instance}, ConverterParameter=StatusMonitoring}"
                                        Command="{Binding SwitchViewCommand}"
                                        CommandParameter="StatusMonitoring" />
                            <!-- <RadioButton Content="📦 固件升级" -->
                            <!--             Style="{StaticResource RadioButtonClean}" -->
                            <!--             IsChecked="{Binding SelectedPage, Converter={x:Static local:StringToBooleanConverter.Instance}, ConverterParameter=FirmwareUpgrade}" -->
                            <!--             Command="{Binding SwitchViewCommand}" -->
                            <!--             CommandParameter="FirmwareUpgrade" /> -->
                        </StackPanel>

                        <!-- 从站地址设置 -->
                        <StackPanel Margin="20,10,20,10">
                            <!-- 从站地址下拉框，子节点启用时显示 -->
                            <TextBlock Text="从站地址" Style="{StaticResource InfoLabelStyle}"
                                       Visibility="{Binding IsMasterNodeEnabled, Converter={StaticResource BoolToVisibilityConverter}}" />
                            <ComboBox ItemsSource="{Binding SlaveAddressOptions}"
                                      SelectedItem="{Binding CurrentSlaveId, Converter={StaticResource ByteConverter}}"
                                      Margin="0,5,0,10"
                                      Visibility="{Binding IsMasterNodeEnabled, Converter={StaticResource BoolToVisibilityConverter}}" />

                            <!-- 选择轴下拉框，子节点启用时显示 -->
                            <TextBlock Text="选择轴" Style="{StaticResource InfoLabelStyle}"/>
                            <ComboBox ItemsSource="{Binding AxisOptions}"
                                      SelectedItem="{Binding SelectedAxis}">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Converter={StaticResource AxisTypeToStringConverter}}" />
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </StackPanel>

                        <!-- 参数表操作按钮 -->
                        <StackPanel Margin="20,0,20,0"
                                   Visibility="{Binding SelectedPage, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=ParameterSettings}">
                            <Button Content="写入参数" Command="{Binding UploadParametersCommand}">
                                <Button.Style>
                                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding HasModifiedParameters}" Value="True">
                                                <Setter Property="Background" Value="#FF9800" />
                                                <Setter Property="Foreground" Value="White" />
                                                <Setter Property="BorderBrush" Value="#FF9800" />
                                                <DataTrigger.EnterActions>
                                                    <BeginStoryboard Name="UploadBlinkStoryboard">
                                                        <Storyboard RepeatBehavior="Forever">
                                                            <ColorAnimation Storyboard.TargetProperty="(Button.Background).(SolidColorBrush.Color)"
                                                                          From="#FF9800" To="#FFC107" Duration="0:0:0.8" AutoReverse="True" />
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </DataTrigger.EnterActions>
                                                <DataTrigger.ExitActions>
                                                    <StopStoryboard BeginStoryboardName="UploadBlinkStoryboard" />
                                                </DataTrigger.ExitActions>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                            </Button>
                            <Button Content="读取参数" Command="{Binding DownloadParametersCommand}">
                                <Button.Style>
                                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding ParameterSettingsViewModel.NeedReloadParameters}" Value="True">
                                                <Setter Property="Background" Value="#FF9800" />
                                                <Setter Property="Foreground" Value="White" />
                                                <Setter Property="BorderBrush" Value="#FF9800" />
                                                <DataTrigger.EnterActions>
                                                    <BeginStoryboard Name="DownloadBlinkStoryboard">
                                                        <Storyboard RepeatBehavior="Forever">
                                                            <ColorAnimation Storyboard.TargetProperty="(Button.Background).(SolidColorBrush.Color)"
                                                                          From="#FF9800" To="#FFC107" Duration="0:0:0.8" AutoReverse="True" />
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </DataTrigger.EnterActions>
                                                <DataTrigger.ExitActions>
                                                    <StopStoryboard BeginStoryboardName="DownloadBlinkStoryboard" />
                                                </DataTrigger.ExitActions>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                            </Button>
                            <Button Content="保存参数" Style="{StaticResource PrimaryButtonStyle}" />

                            <Button Margin="0,10,0,0" Padding="10,8" Style="{StaticResource PrimaryButtonStyle}" Click="FileMenuButton_Click">
                                <Button.ContextMenu>
                                    <ContextMenu>
                                        <MenuItem Header="📤 导出参数文件" Command="{Binding ExportParametersCommand}" />
                                        <MenuItem Header="📥 导入参数文件" Command="{Binding ImportParametersCommand}" />
                                        <Separator />
                                        <MenuItem Header="🏭 恢复出厂设置" Command="{Binding ResetToFactoryCommand}" />
                                        <MenuItem Header="🔨 错误复位" Command="{Binding ResetErrorCommand}" />
                                        <MenuItem Header="🔃 软复位" Command="{Binding SoftResetCommand}" />
                                    </ContextMenu>
                                </Button.ContextMenu>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="文件" VerticalAlignment="Center" />
                                    <TextBlock Text="▶" FontSize="10" Margin="5,0,0,0" VerticalAlignment="Center" Foreground="#999" />
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>

                <!-- 连接状态指示器 -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="20,15,20,5">
                    <Ellipse Width="15" Height="15"
                             Fill="{Binding SerialSettingsViewModel.IsConnected, Converter={StaticResource BoolToBrushConverter}}"
                             Margin="0,0,8,0" />
                    <TextBlock Text="{Binding SerialSettingsViewModel.IsConnected, Converter={StaticResource BoolToStringConverter}}"
                               FontSize="12" Foreground="#666" VerticalAlignment="Center" />
                </StackPanel>
            </Grid>
        </Border>

        <!-- 右侧ContentControl -->
        <ContentControl Grid.Column="1" Content="{Binding CurrentView}" Margin="3">
            <ContentControl.Resources>
                <DataTemplate DataType="{x:Type vm:SerialSettingsViewModel}"> 
                    <views:SerialSettingsView />
                </DataTemplate>
                <DataTemplate DataType="{x:Type vm:OscilloscopeViewModel}">
                    <views:OscilloscopeView />
                </DataTemplate>
                <DataTemplate DataType="{x:Type vm:ParameterSettingsViewModel}">
                    <views:ParameterSettingsView />
                </DataTemplate>
                <DataTemplate DataType="{x:Type vm:MotionSimulationViewModel}">
                    <views:MotionSimulationView />
                </DataTemplate>
                <DataTemplate DataType="{x:Type vm:FirmwareUpgradeViewModel}">
                    <views:FirmwareUpgradeView />
                </DataTemplate>
            </ContentControl.Resources>
        </ContentControl>
    </Grid>
</Window>