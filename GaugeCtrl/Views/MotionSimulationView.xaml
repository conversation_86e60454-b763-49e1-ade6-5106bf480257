<UserControl x:Class="GaugeCtrl.Views.MotionSimulationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             xmlns:local="clr-namespace:GaugeCtrl.Views"
             xmlns:converter="clr-namespace:GaugeCtrl.Converter"
             xmlns:conv="clr-namespace:ValueConverters;assembly=ValueConverters"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="900">

    <UserControl.Resources>
        <!-- 转换器 -->
        <conv:BoolToVisibilityConverter x:Key="BooleanToVisibilityConverter" FalseValue="Collapsed" TrueValue="Visible"/>
        <converter:ValidationErrorToForegroundConverter x:Key="ValidationErrorToForegroundConverter" />
        <conv:BoolToFontWeightConverter x:Key="BooleanToFontWeightConverter" FalseValue="Normal" TrueValue="Bold" />


    </UserControl.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="手动调试" FontSize="24" FontWeight="Bold"
                   Foreground="#333" Margin="10,0,0,20" HorizontalAlignment="Left" />

        <!-- TabControl -->
        <Grid Grid.Row="1" Margin="5,0,0,20">
            <TabControl Background="White" BorderBrush="#E0E0E0" BorderThickness="1" HorizontalAlignment="Stretch">
                <TabControl.Style>
                    <Style TargetType="TabControl">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="TabControl">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="*" />
                                        </Grid.RowDefinitions>
                                        <TabPanel Grid.Row="0" IsItemsHost="True" HorizontalAlignment="Center"
                                                  Background="Transparent" />
                                        <Border Grid.Row="1" Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}">
                                            <ContentPresenter ContentSource="SelectedContent" />
                                        </Border>
                                    </Grid>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </TabControl.Style>

                <!-- 点动模式 -->
                <TabItem Header="点动模式" Visibility="Collapsed"  Style="{StaticResource CustomTabItemStyle}" Width="150">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!-- 第一行第一列：操作按钮 -->
                        <Border Grid.Row="0" Grid.Column="0" Background="White" BorderBrush="#E0E0E0"
                                BorderThickness="1" Margin="5">
                            <Grid Margin="15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="300" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <!-- 左侧：控制参数 -->
                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />  <!-- 控制模式 -->
                                            <RowDefinition Height="Auto" />  <!-- 速度 -->
                                            <RowDefinition Height="Auto" />  <!-- 加速度 -->
                                            <RowDefinition Height="Auto" />  <!-- 减速度 -->
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>

                                        <!-- 控制模式 -->
                                        <TextBlock Grid.Row="0" Grid.Column="0"
                                                   Text="控制模式:" FontSize="14" 
                                                   Margin="0,0,10,5" VerticalAlignment="Center" />
                                        <ComboBox Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="2"
                                                  Height="25" SelectedIndex="1"
                                                  Margin="0,0,10,5" VerticalAlignment="Center">
                                            <ComboBoxItem Content="IO+内部旋钮控制" />
                                            <ComboBoxItem Content="完全通讯控制" />
                                            <ComboBoxItem Content="通讯仅控制速度" />
                                            <ComboBoxItem Content="IO+外部模拟量控制" />
                                        </ComboBox>

                                        <!-- 速度 -->
                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="速度:"
                                                   VerticalAlignment="Center" Margin="0,0,10,5" />
                                        <TextBox Grid.Row="1" Grid.Column="1" Text="200"
                                                 Margin="0,0,10,5" VerticalAlignment="Center" />
                                        <TextBlock Grid.Row="1" Grid.Column="2" Text="rpm"
                                                   VerticalAlignment="Center" Margin="0,0,0,5" />

                                        <!-- 加速度 -->
                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="加速度:"
                                                   VerticalAlignment="Center" Margin="0,0,10,5" />
                                        <TextBox Grid.Row="2" Grid.Column="1" Text="500"
                                                 Margin="0,0,10,5" VerticalAlignment="Center" />
                                        <TextBlock Grid.Row="2" Grid.Column="2" Text="ms"
                                                   VerticalAlignment="Center" Margin="0,0,0,5" />

                                        <!-- 减速度 -->
                                        <TextBlock Grid.Row="3" Grid.Column="0" Text="减速度:"
                                                   VerticalAlignment="Center" Margin="0,0,10,5" />
                                        <TextBox Grid.Row="3" Grid.Column="1" Text="500"
                                                 Margin="0,0,10,5" VerticalAlignment="Center" />
                                        <TextBlock Grid.Row="3" Grid.Column="2" Text="ms"
                                                   VerticalAlignment="Center" Margin="0,0,0,5" />
                                    </Grid>


                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                                        <Button Content="◀ 点动" Width="80" Height="35" Margin="5" Background="#2196F3"
                                                Foreground="White" Command="{Binding JogLeftCommand}" />
                                        <Button Content="点动 ▶" Width="80" Height="35" Margin="5" Background="#2196F3"
                                                Foreground="White" Command="{Binding JogRightCommand}" />
                                    </StackPanel>
                                </StackPanel>

                                <!-- 右侧：控制按钮 -->

                                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                    <StackPanel  Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,0,10">
                                        <ToggleButton Style="{StaticResource ToggleButtonSwitch}" />
                                        <TextBlock Text="电机使能" VerticalAlignment="Center" Margin="5,0,0,0" />
                                    </StackPanel>
                                   
                                    <Button Content="定速运行+" Height="35" Margin="0,0,0,10"
                                            HorizontalAlignment="Stretch" Width="Auto" Command="{Binding ConstantSpeedPositiveCommand}" />
                                    <Button Content="定速运行-" Height="35" Margin="0,0,0,10"
                                            HorizontalAlignment="Stretch" Width="Auto" Command="{Binding ConstantSpeedNegativeCommand}" />
                                    <Button Content="停止" Height="35" Margin="0,0,0,10"
                                            HorizontalAlignment="Stretch" Width="Auto"
                                            Background="#F44336" Foreground="White" Command="{Binding StopCommand}" />
                                    <Button Content="报警清除" Height="35" Margin="0,0,0,10"
                                            HorizontalAlignment="Stretch" Width="Auto" Command="{Binding AlarmClearCommand}" />
                                </StackPanel>


                            </Grid>
                        </Border>

                        <!-- 第二行第一列：指令历史 -->

                        <Border Grid.Row="1" Grid.Column="0" Background="White" BorderBrush="#E0E0E0"
                                BorderThickness="1" Margin="5">
                            <Grid Margin="15">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />  <!-- 上部：标题和按钮，高度自适应内容 -->
                                    <RowDefinition Height="*" />    <!-- 下部：文本框，填充剩余高度 -->
                                </Grid.RowDefinitions>

                                <!-- 上部：标题和按钮 -->

                                <DockPanel LastChildFill="False" Margin="0,0,0,10">
                                    <TextBlock Text="指令历史" FontSize="14" 
                                               VerticalAlignment="Center" DockPanel.Dock="Left" />
                                    <Button Content="清除" Padding="8,4" DockPanel.Dock="Right" Command="{Binding ClearHistoryCommand}" />
                                </DockPanel>


                                <!-- 下部：文本框 -->
                                <TextBox Grid.Row="1" IsReadOnly="True" VerticalScrollBarVisibility="Auto"
                                         Background="#F8F8F8" BorderBrush="#E0E0E0" VerticalContentAlignment="Top"
                                         Text="{Binding MotionHistoryText, Mode=OneWay}" TextWrapping="Wrap" />
                            </Grid>
                        </Border>


                        <!-- 第二列合并行：状态监控 -->
                        <Border Grid.Row="0" Grid.Column="1" Grid.RowSpan="2" Background="White" BorderBrush="#E0E0E0"
                                BorderThickness="1" Margin="5">
                            <StackPanel Margin="15">
                                <Border Background="#EEEEEE" CornerRadius="5" Padding="5" Margin="0,0,0,10">
                                    <TextBlock Text="状态监控" FontSize="14"  HorizontalAlignment="Center" />
                                </Border>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>

                                    <!-- Row 0: Titles -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,0,10">
                                        <ToggleButton Style="{StaticResource ToggleButtonSwitch}"  />
                                        <TextBlock Text="启用监控" VerticalAlignment="Center" Margin="5,0,0,0" />
                                    </StackPanel>
                                    <TextBlock  Grid.Row="0" Grid.Column="1"  Text="I/O" FontSize="14"  Margin="20,0,0,10" VerticalAlignment="Center" />

                                    <!-- Row 1: 运行状态 & DI 1 -->
                                    <Grid Grid.Row="1" Grid.Column="0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="运行状态" VerticalAlignment="Center" Margin="0,5,10,5" />
                                        <TextBox Grid.Column="1" Text="匀速" IsReadOnly="True" Margin="0,5,0,5" />
                                    </Grid>
                                    <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,5,0,5">
                                        <Ellipse Width="12" Height="12" Fill="#4CAF50" Margin="0,0,5,0" />
                                        <TextBlock Text="DI 1" VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- Row 2: 给定速度 & DI 2 -->
                                    <Grid Grid.Row="2" Grid.Column="0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="给定速度" VerticalAlignment="Center" Margin="0,5,10,5" />
                                        <TextBox Grid.Column="1" Text="200" IsReadOnly="True" Margin="0,5,0,5" />
                                    </Grid>
                                    <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,5,0,5">
                                        <Ellipse Width="12" Height="12" Fill="#E0E0E0" Margin="0,0,5,0" />
                                        <TextBlock Text="DI 2" VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- Row 3: 实际速度 & DI 3 -->
                                    <Grid Grid.Row="3" Grid.Column="0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="实际速度" VerticalAlignment="Center" Margin="0,5,10,5" />
                                        <TextBox Grid.Column="1" Text="200" IsReadOnly="True" Margin="0,5,0,5" />
                                    </Grid>
                                    <StackPanel Grid.Row="3" Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,5,0,5">
                                        <Ellipse Width="12" Height="12" Fill="#E0E0E0" Margin="0,0,5,0" />
                                        <TextBlock Text="DI 3" VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- Row 4: 母线电流 & DI 4 -->
                                    <Grid Grid.Row="4" Grid.Column="0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="母线电流" VerticalAlignment="Center" Margin="0,5,10,5" />
                                        <TextBox Grid.Column="1" Text="0.16" IsReadOnly="True" Margin="0,5,0,5" />
                                    </Grid>
                                    <StackPanel Grid.Row="4" Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,5,0,5">
                                        <Ellipse Width="12" Height="12" Fill="#E0E0E0" Margin="0,0,5,0" />
                                        <TextBlock Text="DI 4" VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- Row 5: 电机电流 & DI 5 -->
                                    <Grid Grid.Row="5" Grid.Column="0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="电机电流" VerticalAlignment="Center" Margin="0,5,10,5" />
                                        <TextBox Grid.Column="1" Text="0.60" IsReadOnly="True" Margin="0,5,0,5" />
                                    </Grid>
                                    <StackPanel Grid.Row="5" Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,5,0,5">
                                        <Ellipse Width="12" Height="12" Fill="#E0E0E0" Margin="0,0,5,0" />
                                        <TextBlock Text="DI 5" VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- Row 6: 母线电压 -->
                                    <Grid Grid.Row="6" Grid.Column="0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="母线电压" VerticalAlignment="Center" Margin="0,5,10,5" />
                                        <TextBox Grid.Column="1" Text="23.90" IsReadOnly="True" Margin="0,5,0,5" />
                                    </Grid>

                                    <!-- Row 7: 运行方向 -->
                                    <Grid Grid.Row="7" Grid.Column="0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="运行方向" VerticalAlignment="Center" Margin="0,5,10,5" />
                                        <TextBox Grid.Column="1" Text="1" IsReadOnly="True" Margin="0,5,0,5" />
                                    </Grid>
                                </Grid>
                            </StackPanel>
                        </Border>
                    </Grid>
                </TabItem>

                <!-- 读/写寄存器 -->
                <TabItem Header="读/写寄存器" IsSelected="True" Style="{StaticResource CustomTabItemStyle}" Width="150">
                    <Grid Margin="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!-- 读寄存器 -->
                        <Border Grid.Column="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="1" Margin="5">
                            <Grid Margin="15">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                <TextBlock Grid.Row="0" Text="读寄存器 (0x3)" FontWeight="Bold" Margin="0,0,0,10" />
                                <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10">
                                    <TextBlock Text="起始地址:" VerticalAlignment="Center" Margin="0,0,5,0" />
                                    <TextBox Text="{Binding ReadStartAddress, UpdateSourceTrigger=PropertyChanged}" Width="80" />
                                    <TextBlock Text="数量:" VerticalAlignment="Center" Margin="10,0,5,0" />
                                    <TextBox Text="{Binding ReadQuantity, UpdateSourceTrigger=PropertyChanged}" Width="50" />
                                    <Button Content="读取" Margin="10,0,0,0" Command="{Binding ReadRegisterCommand}" />
                                </StackPanel>
                                <DataGrid Grid.Row="2" ItemsSource="{Binding ReadRegisters}" AutoGenerateColumns="False" VerticalScrollBarVisibility="Auto"
                                          CanUserAddRows="False" IsReadOnly="True">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="地址(Dec)" Binding="{Binding AddressDec}" />
                                        <DataGridTextColumn Header="地址(Hex)" Binding="{Binding AddressHex}" />
                                        <DataGridTextColumn Header="数值(Dec)" Binding="{Binding ValueDec}" />
                                        <DataGridTextColumn Header="数值(Hex)" Binding="{Binding ValueHex}" />
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </Border>

                        <!-- 右侧面板 -->
                        <Grid Grid.Column="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" MinHeight="100" />
                            </Grid.RowDefinitions>

                            <!-- 写多个寄存器 -->
                            <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="1" Margin="5">
                                <Grid Margin="15">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    <TextBlock Grid.Row="0" Text="写多个寄存器 (0x10)" FontWeight="Bold" Margin="0,0,0,10" />
                                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10">
                                        <TextBlock Text="起始地址:" VerticalAlignment="Center" Margin="0,0,5,0" />
                                        <TextBox Text="{Binding WriteStartAddress, UpdateSourceTrigger=PropertyChanged}" Width="80" />
                                        <TextBlock Text="数量:" VerticalAlignment="Center" Margin="10,0,5,0" />
                                        <TextBox Text="{Binding WriteQuantity, UpdateSourceTrigger=PropertyChanged}" Width="50" />
                                        <Button Content="写入" Margin="10,0,0,0" Command="{Binding WriteRegisterCommand}" />
                                    </StackPanel>
                                    <DataGrid Grid.Row="2" ItemsSource="{Binding WriteRegisters}" AutoGenerateColumns="False" VerticalScrollBarVisibility="Auto"
                                              CanUserAddRows="False">
                                        <DataGrid.Columns>
                                            <DataGridTextColumn Header="地址(Dec)" Binding="{Binding AddressDec}" />
                                            <DataGridTextColumn Header="地址(Hex)" Binding="{Binding AddressHex}" />
                                            <DataGridTemplateColumn Header="数值(Dec)">
                                                <DataGridTemplateColumn.CellTemplate>
                                                    <DataTemplate>
                                                        <TextBox Text="{Binding ValueDec, UpdateSourceTrigger=PropertyChanged}" />
                                                    </DataTemplate>
                                                </DataGridTemplateColumn.CellTemplate>
                                            </DataGridTemplateColumn>
                                            <DataGridTextColumn Header="数值(Hex)" Binding="{Binding ValueHex}" IsReadOnly="True" />
                                        </DataGrid.Columns>
                                    </DataGrid>
                                </Grid>
                            </Border>

                            <GridSplitter Grid.Row="1" Height="5" HorizontalAlignment="Stretch" Background="#E0E0E0" VerticalAlignment="Center" />

                            <!-- 指令历史 -->
                            <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="1" Margin="5">
                                <Grid Margin="15">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="*" />
                                    </Grid.RowDefinitions>

                                    <DockPanel LastChildFill="False" Margin="0,0,0,10">
                                        <TextBlock Text="指令历史" FontSize="14" VerticalAlignment="Center" DockPanel.Dock="Left" />
                                        <Button Content="清除" Padding="8,4" DockPanel.Dock="Right" Command="{Binding ClearRegisterHistoryCommand}" />
                                        <CheckBox Content="CRC取反" IsChecked="True" VerticalAlignment="Center" DockPanel.Dock="Right" Margin="0,0,10,0" />
                                    </DockPanel>

                                    <TextBox Grid.Row="1" IsReadOnly="True" VerticalScrollBarVisibility="Auto"
                                             Background="#F8F8F8" BorderBrush="#E0E0E0" VerticalContentAlignment="Top"
                                             Text="{Binding RegisterHistoryText, Mode=OneWay}" TextWrapping="Wrap" />
                                </Grid>
                            </Border>
                        </Grid>
                    </Grid>
                </TabItem>

            </TabControl>
        </Grid>
    </Grid>
</UserControl>