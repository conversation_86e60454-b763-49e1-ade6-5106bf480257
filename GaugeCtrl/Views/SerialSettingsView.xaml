<UserControl x:Class="GaugeCtrl.Views.SerialSettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             xmlns:local="clr-namespace:GaugeCtrl.Views"
             xmlns:vm="clr-namespace:GaugeCtrl.ViewModels"
             xmlns:conv="clr-namespace:ValueConverters;assembly=ValueConverters"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800"
             d:DataContext="{d:DesignInstance vm:SerialSettingsViewModel}">
    <UserControl.Resources>
        <conv:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter" TrueValue="Visible" FalseValue="Collapsed"></conv:BoolToVisibilityConverter>
        <Style x:Key="ToggleButtonSwitch" TargetType="ToggleButton">
            <Setter Property="Width" Value="50" />
            <Setter Property="Height" Value="25" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToggleButton">
                        <Border x:Name="Border" Background="#CCCCCC" CornerRadius="12.5" BorderThickness="1"
                                BorderBrush="#AAAAAA">
                            <Ellipse x:Name="Thumb" Width="21" Height="21" Fill="White" HorizontalAlignment="Left"
                                     Margin="2,2,0,0">
                                <Ellipse.RenderTransform>
                                    <TranslateTransform x:Name="ThumbTransform" X="0" />
                                </Ellipse.RenderTransform>
                            </Ellipse>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#4CAF50" />
                                <Setter TargetName="Thumb" Property="Margin" Value="27,2,0,0" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>
    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Grid.ColumnSpan="2" Text="串口设置" FontSize="24" FontWeight="Bold"
                   Foreground="#333" Margin="10,0,0,20" HorizontalAlignment="Left" />

        <!-- 参数输入区域 -->
        <StackPanel Grid.Row="1" Grid.Column="0" Margin="5,0,20,20" HorizontalAlignment="Left" Width="200">
            <!-- 串口配置参数 -->
            <hc:ComboBox hc:InfoElement.Title="串口号"
                         ItemsSource="{Binding AvailablePorts}"
                         SelectedItem="{Binding SelectedPortName}"
                         DropDownOpened="OnPortComboBoxDropDownOpened"
                         Margin="5" />

            <hc:ComboBox hc:InfoElement.Title="波特率"
                         ItemsSource="{Binding BaudRates}"
                         SelectedItem="{Binding SelectedBaudRate}"
                         Margin="5" />

            <hc:ComboBox hc:InfoElement.Title="数据位"
                         ItemsSource="{Binding DataBits}"
                         SelectedItem="{Binding SelectedDataBits}"
                         Margin="5" />

            <hc:ComboBox hc:InfoElement.Title="奇偶校验"
                         ItemsSource="{Binding ParityOptions}"
                         SelectedItem="{Binding SelectedParity}"
                         Margin="5" />

            <hc:ComboBox hc:InfoElement.Title="停止位"
                         ItemsSource="{Binding StopBitsOptions}"
                         SelectedItem="{Binding SelectedStopBits}"
                         Margin="5" />
            <!-- 选择驱动器类型 -->
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="5">
                <ToggleButton Style="{StaticResource ToggleButtonSwitch}"
                              IsEnabled="{Binding IsConnected, Converter={StaticResource Boolean2BooleanReConverter}}"
                              IsChecked="{Binding IsMasterNodeEnabled}" />
                <TextBlock Text="是否主节点" VerticalAlignment="Center" Margin="5,0,0,0" />
            </StackPanel>
            <!-- 连接操作按钮 -->
            <StackPanel Orientation="Horizontal" Margin="5">
                <!-- 连接按钮 -->
                <Button Content="连接" Width="80"
                        Command="{Binding ConnectCommand}"
                        Margin="0,0,10,0">
                    <Button.Style>
                        <Style TargetType="Button" BasedOn="{StaticResource ButtonPrimary}">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsConnected}" Value="True">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding IsConnected}" Value="False">
                                    <Setter Property="Visibility" Value="Visible" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>

                <!-- 断开连接按钮 -->
                <Button Content="断开" Width="80"
                        Command="{Binding DisconnectCommand}"
                        Margin="0">
                    <Button.Style>
                        <Style TargetType="Button" BasedOn="{StaticResource ButtonDanger}">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsConnected}" Value="True">
                                    <Setter Property="Visibility" Value="Visible" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding IsConnected}" Value="False">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </StackPanel>

            <!-- 搜索从站按钮和结果显示 -->
            <StackPanel Orientation="Horizontal" Margin="5">
                <TextBlock Text="{Binding SearchResult}"
                           Foreground="#666"
                           FontSize="12"
                           VerticalAlignment="Bottom"
                           TextWrapping="Wrap" />
            </StackPanel>

        </StackPanel>

        <!-- 操作按钮区域 -->
        <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Vertical" HorizontalAlignment="Left"
                    VerticalAlignment="Bottom" Margin="20,0,0,20">


        </StackPanel>
    </Grid>
</UserControl>