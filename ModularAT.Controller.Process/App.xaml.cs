using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using ModularAT.Shared;

namespace ModularAT.Controller.Process;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private IHost? _host;
    private ProcessStartupConfig? _startupConfig;

    protected override async void OnStartup(StartupEventArgs e)
    {
        // 解析启动配置
        _startupConfig = ConfigurationHelper.ParseStartupConfig(e.Args);

        if (_startupConfig == null)
        {
            MessageBox.Show("无法解析启动配置，程序将退出。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
            return;
        }

        // 创建主机
        _host = CreateHostBuilder(e.Args).Build();

        // 启动主机
        await _host.StartAsync();

        // 显示主窗口
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();

        base.OnStartup(e);
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }

        base.OnExit(e);
    }

    private IHostBuilder CreateHostBuilder(string[] args) =>
        Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder(args)
            .ConfigureSharedInfrastructure(ProcessType.Controller)
            .ConfigureServices((context, services) =>
            {
                // 注册启动配置
                if (_startupConfig != null)
                {
                    services.AddSingleton(_startupConfig);
                    services.AddSingleton(_startupConfig.UserInfo);
                }

                // 注册窗口和视图模型
                services.AddSingleton<MainWindow>();
                services.AddSingleton<MainViewModel>();
            });
}

