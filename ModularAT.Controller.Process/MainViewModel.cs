using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using System.Windows.Media;
using ModularAT.Shared;

namespace ModularAT.Controller.Process
{
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly IProcessCommunication _processCommunication;
        private readonly ISharedConfigurationService _configService;
        private readonly UserAuthenticationInfo _userInfo;
        
        private string _currentUser = "未登录";
        private string _connectionStatus = "未连接";
        private string _statusMessage = "控制器进程已启动";
        private string _ipAddress = "*************";
        private int _port = 8080;
        private string _statusText = "离线";
        private Brush _statusColor = Brushes.Red;
        private string _systemStatus = "系统待机";
        private string _runningMode = "手动模式";

        public MainViewModel(IProcessCommunication processCommunication, 
                           ISharedConfigurationService configService,
                           UserAuthenticationInfo userInfo)
        {
            _processCommunication = processCommunication;
            _configService = configService;
            _userInfo = userInfo;
            
            // 初始化命令
            ConnectCommand = new RelayCommand(ExecuteConnect, CanExecuteConnect);
            DisconnectCommand = new RelayCommand(ExecuteDisconnect, CanExecuteDisconnect);
            StartCommand = new RelayCommand(ExecuteStart, CanExecuteControllerCommand);
            StopCommand = new RelayCommand(ExecuteStop, CanExecuteControllerCommand);
            ResetCommand = new RelayCommand(ExecuteReset, CanExecuteControllerCommand);
            EmergencyStopCommand = new RelayCommand(ExecuteEmergencyStop, CanExecuteControllerCommand);
            
            // 初始化轴状态
            AxisStatus = new ObservableCollection<AxisInfo>
            {
                new AxisInfo { AxisName = "X轴", Status = "待机", Position = "0.000" },
                new AxisInfo { AxisName = "Y轴", Status = "待机", Position = "0.000" },
                new AxisInfo { AxisName = "Z轴", Status = "待机", Position = "0.000" },
                new AxisInfo { AxisName = "R轴", Status = "待机", Position = "0.000" }
            };
            
            // 初始化数据
            InitializeData();
            
            // 启动进程间通信
            StartCommunication();
        }

        #region Properties

        public string CurrentUser
        {
            get => _currentUser;
            set
            {
                _currentUser = value;
                OnPropertyChanged();
            }
        }

        public string ConnectionStatus
        {
            get => _connectionStatus;
            set
            {
                _connectionStatus = value;
                OnPropertyChanged();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        public string IpAddress
        {
            get => _ipAddress;
            set
            {
                _ipAddress = value;
                OnPropertyChanged();
                ((RelayCommand)ConnectCommand).RaiseCanExecuteChanged();
            }
        }

        public int Port
        {
            get => _port;
            set
            {
                _port = value;
                OnPropertyChanged();
            }
        }

        public string StatusText
        {
            get => _statusText;
            set
            {
                _statusText = value;
                OnPropertyChanged();
            }
        }

        public Brush StatusColor
        {
            get => _statusColor;
            set
            {
                _statusColor = value;
                OnPropertyChanged();
            }
        }

        public string SystemStatus
        {
            get => _systemStatus;
            set
            {
                _systemStatus = value;
                OnPropertyChanged();
            }
        }

        public string RunningMode
        {
            get => _runningMode;
            set
            {
                _runningMode = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<AxisInfo> AxisStatus { get; }

        #endregion

        #region Commands

        public ICommand ConnectCommand { get; }
        public ICommand DisconnectCommand { get; }
        public ICommand StartCommand { get; }
        public ICommand StopCommand { get; }
        public ICommand ResetCommand { get; }
        public ICommand EmergencyStopCommand { get; }

        private bool CanExecuteConnect()
        {
            return !string.IsNullOrEmpty(IpAddress) && StatusText == "离线";
        }

        private bool CanExecuteDisconnect()
        {
            return StatusText == "在线";
        }

        private bool CanExecuteControllerCommand()
        {
            return StatusText == "在线";
        }

        private async void ExecuteConnect()
        {
            try
            {
                StatusMessage = "正在连接控制器...";
                
                // 模拟连接过程
                await Task.Delay(1000);
                
                // 更新状态
                StatusText = "在线";
                StatusColor = Brushes.Green;
                ConnectionStatus = $"已连接 {IpAddress}:{Port}";
                StatusMessage = "控制器连接成功";
                SystemStatus = "系统就绪";
                
                // 更新命令状态
                UpdateCommandStates();
                
                // 开始状态更新
                StartStatusUpdate();
            }
            catch (Exception ex)
            {
                StatusMessage = $"连接失败: {ex.Message}";
            }
        }

        private async void ExecuteDisconnect()
        {
            try
            {
                StatusMessage = "正在断开连接...";
                
                // 模拟断开过程
                await Task.Delay(500);
                
                // 更新状态
                StatusText = "离线";
                StatusColor = Brushes.Red;
                ConnectionStatus = "未连接";
                StatusMessage = "控制器已断开";
                SystemStatus = "系统待机";
                
                // 更新命令状态
                UpdateCommandStates();
            }
            catch (Exception ex)
            {
                StatusMessage = $"断开失败: {ex.Message}";
            }
        }

        private void ExecuteStart()
        {
            StatusMessage = "控制器已启动";
            SystemStatus = "系统运行中";
            RunningMode = "自动模式";
        }

        private void ExecuteStop()
        {
            StatusMessage = "控制器已停止";
            SystemStatus = "系统停止";
            RunningMode = "手动模式";
        }

        private void ExecuteReset()
        {
            StatusMessage = "控制器已复位";
            SystemStatus = "系统就绪";
            RunningMode = "手动模式";
            
            // 重置轴状态
            foreach (var axis in AxisStatus)
            {
                axis.Status = "待机";
                axis.Position = "0.000";
            }
        }

        private void ExecuteEmergencyStop()
        {
            StatusMessage = "紧急停止已执行";
            SystemStatus = "紧急停止";
            RunningMode = "急停模式";
            
            // 更新轴状态为急停
            foreach (var axis in AxisStatus)
            {
                axis.Status = "急停";
            }
        }

        #endregion

        #region Private Methods

        private void InitializeData()
        {
            CurrentUser = _userInfo.UserName;
            StatusMessage = "控制器进程已启动，等待连接...";
        }

        private async void StartCommunication()
        {
            try
            {
                await _processCommunication.StartAsync(ProcessNames.Controller);
                StatusMessage = "进程间通信已建立";
            }
            catch (Exception ex)
            {
                StatusMessage = $"通信启动失败: {ex.Message}";
            }
        }

        private void UpdateCommandStates()
        {
            ((RelayCommand)ConnectCommand).RaiseCanExecuteChanged();
            ((RelayCommand)DisconnectCommand).RaiseCanExecuteChanged();
            ((RelayCommand)StartCommand).RaiseCanExecuteChanged();
            ((RelayCommand)StopCommand).RaiseCanExecuteChanged();
            ((RelayCommand)ResetCommand).RaiseCanExecuteChanged();
            ((RelayCommand)EmergencyStopCommand).RaiseCanExecuteChanged();
        }

        private async void StartStatusUpdate()
        {
            // 模拟状态更新
            while (StatusText == "在线")
            {
                await Task.Delay(1000);
                
                // 模拟轴位置变化
                foreach (var axis in AxisStatus)
                {
                    if (axis.Status == "运行中")
                    {
                        var currentPos = double.Parse(axis.Position);
                        axis.Position = (currentPos + (Random.Shared.NextDouble() - 0.5) * 0.1).ToString("F3");
                    }
                }
            }
        }

        #endregion

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class AxisInfo : INotifyPropertyChanged
    {
        private string _status = string.Empty;
        private string _position = string.Empty;

        public string AxisName { get; set; } = string.Empty;
        
        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
            }
        }
        
        public string Position
        {
            get => _position;
            set
            {
                _position = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // RelayCommand实现
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged;

        public bool CanExecute(object? parameter)
        {
            return _canExecute?.Invoke() ?? true;
        }

        public void Execute(object? parameter)
        {
            _execute();
        }

        public void RaiseCanExecuteChanged()
        {
            CanExecuteChanged?.Invoke(this, EventArgs.Empty);
        }
    }
}
