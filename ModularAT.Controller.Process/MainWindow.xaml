<Window x:Class="ModularAT.Controller.Process.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ModularAT.Controller.Process"
        mc:Ignorable="d"
        Title="ModularAT 控制器控制台" Height="700" Width="1200"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#FF9C27B0" Height="60">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="ModularAT 控制器控制台" FontSize="20" FontWeight="Bold"
                          Foreground="White" VerticalAlignment="Center"/>
                <TextBlock Text="{Binding CurrentUser}" FontSize="14"
                          Foreground="White" VerticalAlignment="Center" Margin="20,0,0,0"/>
                <TextBlock Text="{Binding ConnectionStatus}" FontSize="14"
                          Foreground="White" VerticalAlignment="Center" Margin="20,0,0,0"/>
            </StackPanel>
        </Border>

        <!-- 主内容区域 -->
        <TabControl Grid.Row="1" Margin="10">
            <!-- 控制器连接选项卡 -->
            <TabItem Header="控制器连接">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="300"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左侧控制面板 -->
                    <GroupBox Grid.Column="0" Header="TCP连接控制" Margin="5">
                        <StackPanel Margin="10">
                            <TextBlock Text="TCP设置:" FontWeight="Bold" Margin="0,0,0,10"/>

                            <TextBlock Text="IP地址:"/>
                            <TextBox Text="{Binding IpAddress}" Height="25" Margin="0,2,0,5"/>

                            <TextBlock Text="端口:"/>
                            <TextBox Text="{Binding Port}" Height="25" Margin="0,2,0,5"/>

                            <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                                <Button Content="连接" Command="{Binding ConnectCommand}"
                                       Width="60" Height="30" Margin="0,0,10,0"/>
                                <Button Content="断开" Command="{Binding DisconnectCommand}"
                                       Width="60" Height="30"/>
                            </StackPanel>

                            <Separator Margin="0,15,0,15"/>

                            <TextBlock Text="控制器命令:" FontWeight="Bold" Margin="0,0,0,10"/>

                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <Button Content="启动" Command="{Binding StartCommand}"
                                       Width="60" Height="30" Margin="0,0,10,0"/>
                                <Button Content="停止" Command="{Binding StopCommand}"
                                       Width="60" Height="30"/>
                            </StackPanel>

                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <Button Content="复位" Command="{Binding ResetCommand}"
                                       Width="60" Height="30" Margin="0,0,10,0"/>
                                <Button Content="急停" Command="{Binding EmergencyStopCommand}"
                                       Width="60" Height="30"/>
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>

                    <!-- 右侧状态显示 -->
                    <GroupBox Grid.Column="1" Header="控制器状态" Margin="5">
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- 状态指示器 -->
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                                <Border Background="{Binding StatusColor}" Width="20" Height="20"
                                       CornerRadius="10" Margin="0,0,10,0"/>
                                <TextBlock Text="{Binding StatusText}" FontWeight="Bold" VerticalAlignment="Center"/>
                            </StackPanel>

                            <!-- 实时数据 -->
                            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                                <StackPanel>
                                    <TextBlock Text="系统状态:" FontWeight="Bold" Margin="0,0,0,10"/>
                                    <TextBlock Text="{Binding SystemStatus}" Margin="0,0,0,10"/>

                                    <TextBlock Text="运行模式:" FontWeight="Bold" Margin="0,10,0,5"/>
                                    <TextBlock Text="{Binding RunningMode}" Margin="0,0,0,10"/>

                                    <TextBlock Text="轴状态:" FontWeight="Bold" Margin="0,10,0,5"/>
                                    <ListBox ItemsSource="{Binding AxisStatus}" Height="200">
                                        <ListBox.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding AxisName}" Width="60"/>
                                                    <TextBlock Text="{Binding Status}" Width="80"/>
                                                    <TextBlock Text="{Binding Position}" Width="100"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </ListBox.ItemTemplate>
                                    </ListBox>
                                </StackPanel>
                            </ScrollViewer>
                        </Grid>
                    </GroupBox>
                </Grid>
            </TabItem>

            <!-- 轴控制选项卡 -->
            <TabItem Header="轴控制">
                <Grid Margin="10">
                    <TextBlock Text="轴控制界面 - 待实现" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16"/>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="#FFF0F0F0" Height="30">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                <TextBlock Text="控制器进程" Margin="0,0,20,0"/>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
