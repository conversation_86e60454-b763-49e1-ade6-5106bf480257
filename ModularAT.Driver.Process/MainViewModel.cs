using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using System.Windows.Media;
using ModularAT.Shared;

namespace ModularAT.Driver.Process
{
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly IProcessCommunication _processCommunication;
        private readonly ISharedConfigurationService _configService;
        private readonly UserAuthenticationInfo _userInfo;
        
        private string _currentUser = "未登录";
        private string _connectionStatus = "未连接";
        private string _statusMessage = "驱动器进程已启动";
        private string _selectedPort = "COM1";
        private int _selectedBaudRate = 9600;
        private string _statusText = "离线";
        private Brush _statusColor = Brushes.Red;
        
        // 实时数据
        private double _currentPosition = 0.0;
        private double _currentVelocity = 0.0;
        private double _currentCurrent = 0.0;
        private double _currentTemperature = 25.0;
        private string _errorCode = "无";

        public MainViewModel(IProcessCommunication processCommunication, 
                           ISharedConfigurationService configService,
                           UserAuthenticationInfo userInfo)
        {
            _processCommunication = processCommunication;
            _configService = configService;
            _userInfo = userInfo;
            
            // 初始化命令
            ConnectCommand = new RelayCommand(ExecuteConnect, CanExecuteConnect);
            DisconnectCommand = new RelayCommand(ExecuteDisconnect, CanExecuteDisconnect);
            EnableCommand = new RelayCommand(ExecuteEnable, CanExecuteDriverCommand);
            DisableCommand = new RelayCommand(ExecuteDisable, CanExecuteDriverCommand);
            ResetCommand = new RelayCommand(ExecuteReset, CanExecuteDriverCommand);
            StopCommand = new RelayCommand(ExecuteStop, CanExecuteDriverCommand);
            
            // 初始化数据
            InitializeData();
            
            // 启动进程间通信
            StartCommunication();
        }

        #region Properties

        public string CurrentUser
        {
            get => _currentUser;
            set
            {
                _currentUser = value;
                OnPropertyChanged();
            }
        }

        public string ConnectionStatus
        {
            get => _connectionStatus;
            set
            {
                _connectionStatus = value;
                OnPropertyChanged();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<string> AvailablePorts { get; } = new()
        {
            "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8"
        };

        public ObservableCollection<int> BaudRates { get; } = new()
        {
            9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600
        };

        public string SelectedPort
        {
            get => _selectedPort;
            set
            {
                _selectedPort = value;
                OnPropertyChanged();
                ((RelayCommand)ConnectCommand).RaiseCanExecuteChanged();
            }
        }

        public int SelectedBaudRate
        {
            get => _selectedBaudRate;
            set
            {
                _selectedBaudRate = value;
                OnPropertyChanged();
            }
        }

        public string StatusText
        {
            get => _statusText;
            set
            {
                _statusText = value;
                OnPropertyChanged();
            }
        }

        public Brush StatusColor
        {
            get => _statusColor;
            set
            {
                _statusColor = value;
                OnPropertyChanged();
            }
        }

        public double CurrentPosition
        {
            get => _currentPosition;
            set
            {
                _currentPosition = value;
                OnPropertyChanged();
            }
        }

        public double CurrentVelocity
        {
            get => _currentVelocity;
            set
            {
                _currentVelocity = value;
                OnPropertyChanged();
            }
        }

        public double CurrentCurrent
        {
            get => _currentCurrent;
            set
            {
                _currentCurrent = value;
                OnPropertyChanged();
            }
        }

        public double CurrentTemperature
        {
            get => _currentTemperature;
            set
            {
                _currentTemperature = value;
                OnPropertyChanged();
            }
        }

        public string ErrorCode
        {
            get => _errorCode;
            set
            {
                _errorCode = value;
                OnPropertyChanged();
            }
        }

        public object? ServoSettingsView { get; set; }

        #endregion

        #region Commands

        public ICommand ConnectCommand { get; }
        public ICommand DisconnectCommand { get; }
        public ICommand EnableCommand { get; }
        public ICommand DisableCommand { get; }
        public ICommand ResetCommand { get; }
        public ICommand StopCommand { get; }

        private bool CanExecuteConnect()
        {
            return !string.IsNullOrEmpty(SelectedPort) && StatusText == "离线";
        }

        private bool CanExecuteDisconnect()
        {
            return StatusText == "在线";
        }

        private bool CanExecuteDriverCommand()
        {
            return StatusText == "在线";
        }

        private async void ExecuteConnect()
        {
            try
            {
                StatusMessage = "正在连接驱动器...";
                
                // 模拟连接过程
                await Task.Delay(1000);
                
                // 更新状态
                StatusText = "在线";
                StatusColor = Brushes.Green;
                ConnectionStatus = $"已连接 {SelectedPort}@{SelectedBaudRate}";
                StatusMessage = "驱动器连接成功";
                
                // 更新命令状态
                UpdateCommandStates();
                
                // 开始数据更新
                StartDataUpdate();
            }
            catch (Exception ex)
            {
                StatusMessage = $"连接失败: {ex.Message}";
            }
        }

        private async void ExecuteDisconnect()
        {
            try
            {
                StatusMessage = "正在断开连接...";
                
                // 模拟断开过程
                await Task.Delay(500);
                
                // 更新状态
                StatusText = "离线";
                StatusColor = Brushes.Red;
                ConnectionStatus = "未连接";
                StatusMessage = "驱动器已断开";
                
                // 更新命令状态
                UpdateCommandStates();
            }
            catch (Exception ex)
            {
                StatusMessage = $"断开失败: {ex.Message}";
            }
        }

        private void ExecuteEnable()
        {
            StatusMessage = "驱动器已使能";
        }

        private void ExecuteDisable()
        {
            StatusMessage = "驱动器已失能";
        }

        private void ExecuteReset()
        {
            StatusMessage = "驱动器已复位";
            ErrorCode = "无";
        }

        private void ExecuteStop()
        {
            StatusMessage = "驱动器已停止";
        }

        #endregion

        #region Private Methods

        private void InitializeData()
        {
            CurrentUser = _userInfo.UserName;
            StatusMessage = "驱动器进程已启动，等待连接...";
        }

        private async void StartCommunication()
        {
            try
            {
                await _processCommunication.StartAsync(ProcessNames.Driver);
                StatusMessage = "进程间通信已建立";
            }
            catch (Exception ex)
            {
                StatusMessage = $"通信启动失败: {ex.Message}";
            }
        }

        private void UpdateCommandStates()
        {
            ((RelayCommand)ConnectCommand).RaiseCanExecuteChanged();
            ((RelayCommand)DisconnectCommand).RaiseCanExecuteChanged();
            ((RelayCommand)EnableCommand).RaiseCanExecuteChanged();
            ((RelayCommand)DisableCommand).RaiseCanExecuteChanged();
            ((RelayCommand)ResetCommand).RaiseCanExecuteChanged();
            ((RelayCommand)StopCommand).RaiseCanExecuteChanged();
        }

        private async void StartDataUpdate()
        {
            // 模拟实时数据更新
            while (StatusText == "在线")
            {
                await Task.Delay(100);
                
                // 模拟数据变化
                CurrentPosition += (Random.Shared.NextDouble() - 0.5) * 0.1;
                CurrentVelocity = (Random.Shared.NextDouble() - 0.5) * 10;
                CurrentCurrent = 1.5 + (Random.Shared.NextDouble() - 0.5) * 0.5;
                CurrentTemperature = 25 + (Random.Shared.NextDouble() - 0.5) * 5;
            }
        }

        #endregion

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }


}
