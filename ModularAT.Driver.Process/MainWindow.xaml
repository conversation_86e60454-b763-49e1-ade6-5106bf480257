<Window x:Class="ModularAT.Driver.Process.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ModularAT.Driver.Process"
        mc:Ignorable="d"
        Title="ModularAT 驱动器控制台" Height="700" Width="1200"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#FF4CAF50" Height="60">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="ModularAT 驱动器控制台" FontSize="20" FontWeight="Bold"
                          Foreground="White" VerticalAlignment="Center"/>
                <TextBlock Text="{Binding CurrentUser}" FontSize="14"
                          Foreground="White" VerticalAlignment="Center" Margin="20,0,0,0"/>
                <TextBlock Text="{Binding ConnectionStatus}" FontSize="14"
                          Foreground="White" VerticalAlignment="Center" Margin="20,0,0,0"/>
            </StackPanel>
        </Border>

        <!-- 主内容区域 -->
        <TabControl Grid.Row="1" Margin="10">
            <!-- 驱动器控制选项卡 -->
            <TabItem Header="驱动器控制">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="300"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左侧控制面板 -->
                    <GroupBox Grid.Column="0" Header="连接控制" Margin="5">
                        <StackPanel Margin="10">
                            <TextBlock Text="串口设置:" FontWeight="Bold" Margin="0,0,0,10"/>

                            <TextBlock Text="端口:"/>
                            <ComboBox ItemsSource="{Binding AvailablePorts}"
                                     SelectedItem="{Binding SelectedPort}" Height="25" Margin="0,2,0,5"/>

                            <TextBlock Text="波特率:"/>
                            <ComboBox ItemsSource="{Binding BaudRates}"
                                     SelectedItem="{Binding SelectedBaudRate}" Height="25" Margin="0,2,0,5"/>

                            <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                                <Button Content="连接" Command="{Binding ConnectCommand}"
                                       Width="60" Height="30" Margin="0,0,10,0"/>
                                <Button Content="断开" Command="{Binding DisconnectCommand}"
                                       Width="60" Height="30"/>
                            </StackPanel>

                            <Separator Margin="0,15,0,15"/>

                            <TextBlock Text="驱动器控制:" FontWeight="Bold" Margin="0,0,0,10"/>

                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <Button Content="使能" Command="{Binding EnableCommand}"
                                       Width="60" Height="30" Margin="0,0,10,0"/>
                                <Button Content="失能" Command="{Binding DisableCommand}"
                                       Width="60" Height="30"/>
                            </StackPanel>

                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <Button Content="复位" Command="{Binding ResetCommand}"
                                       Width="60" Height="30" Margin="0,0,10,0"/>
                                <Button Content="停止" Command="{Binding StopCommand}"
                                       Width="60" Height="30"/>
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>

                    <!-- 右侧状态显示 -->
                    <GroupBox Grid.Column="1" Header="驱动器状态" Margin="5">
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- 状态指示器 -->
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                                <Border Background="{Binding StatusColor}" Width="20" Height="20"
                                       CornerRadius="10" Margin="0,0,10,0"/>
                                <TextBlock Text="{Binding StatusText}" FontWeight="Bold" VerticalAlignment="Center"/>
                            </StackPanel>

                            <!-- 实时数据 -->
                            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                                <StackPanel>
                                    <TextBlock Text="实时数据:" FontWeight="Bold" Margin="0,0,0,10"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="位置:" Margin="0,2"/>
                                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding CurrentPosition}" Margin="10,2"/>

                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="速度:" Margin="0,2"/>
                                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding CurrentVelocity}" Margin="10,2"/>

                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="电流:" Margin="0,2"/>
                                        <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding CurrentCurrent}" Margin="10,2"/>

                                        <TextBlock Grid.Row="3" Grid.Column="0" Text="温度:" Margin="0,2"/>
                                        <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding CurrentTemperature}" Margin="10,2"/>

                                        <TextBlock Grid.Row="4" Grid.Column="0" Text="错误码:" Margin="0,2"/>
                                        <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding ErrorCode}" Margin="10,2"/>
                                    </Grid>
                                </StackPanel>
                            </ScrollViewer>
                        </Grid>
                    </GroupBox>
                </Grid>
            </TabItem>

            <!-- 参数设置选项卡 -->
            <TabItem Header="参数设置">
                <ContentControl Content="{Binding ServoSettingsView}"/>
            </TabItem>
        </TabControl>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="#FFF0F0F0" Height="30">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                <TextBlock Text="驱动器进程" Margin="0,0,20,0"/>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
