using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ModularAT.Driver.Process
{
    public class ServoControlViewModel : INotifyPropertyChanged
    {
        public ServoControlViewModel()
        {
            // 初始化伺服控制相关的逻辑
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
