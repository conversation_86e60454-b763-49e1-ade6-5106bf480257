<UserControl x:Class="ModularAT.Driver.Process.ServoSettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ModularAT.Driver.Process"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <ScrollViewer VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="20">
                <TextBlock Text="伺服参数设置" FontSize="18" FontWeight="Bold" Margin="0,0,0,20"/>
                
                <GroupBox Header="电机参数" Margin="0,0,0,15">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="额定功率:" VerticalAlignment="Center" Margin="0,5"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding RatedPower}" Margin="5"/>
                        
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="额定电压:" VerticalAlignment="Center" Margin="10,5,0,5"/>
                        <TextBox Grid.Row="0" Grid.Column="3" Text="{Binding RatedVoltage}" Margin="5"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="额定电流:" VerticalAlignment="Center" Margin="0,5"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding RatedCurrent}" Margin="5"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="2" Text="额定转速:" VerticalAlignment="Center" Margin="10,5,0,5"/>
                        <TextBox Grid.Row="1" Grid.Column="3" Text="{Binding RatedSpeed}" Margin="5"/>
                    </Grid>
                </GroupBox>
                
                <GroupBox Header="控制参数" Margin="0,0,0,15">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="位置增益:" VerticalAlignment="Center" Margin="0,5"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding PositionGain}" Margin="5"/>
                        
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="速度增益:" VerticalAlignment="Center" Margin="10,5,0,5"/>
                        <TextBox Grid.Row="0" Grid.Column="3" Text="{Binding VelocityGain}" Margin="5"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="积分时间:" VerticalAlignment="Center" Margin="0,5"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding IntegralTime}" Margin="5"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="2" Text="微分时间:" VerticalAlignment="Center" Margin="10,5,0,5"/>
                        <TextBox Grid.Row="1" Grid.Column="3" Text="{Binding DerivativeTime}" Margin="5"/>
                    </Grid>
                </GroupBox>
                
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                    <Button Content="读取参数" Command="{Binding ReadParametersCommand}" 
                           Width="100" Height="35" Margin="0,0,10,0"/>
                    <Button Content="写入参数" Command="{Binding WriteParametersCommand}" 
                           Width="100" Height="35" Margin="0,0,10,0"/>
                    <Button Content="恢复默认" Command="{Binding RestoreDefaultsCommand}" 
                           Width="100" Height="35"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
