using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;

namespace ModularAT.Driver.Process
{
    public class ServoSettingsViewModel : INotifyPropertyChanged
    {
        private double _ratedPower = 1000.0;
        private double _ratedVoltage = 220.0;
        private double _ratedCurrent = 5.0;
        private double _ratedSpeed = 3000.0;
        private double _positionGain = 100.0;
        private double _velocityGain = 50.0;
        private double _integralTime = 10.0;
        private double _derivativeTime = 1.0;

        public ServoSettingsViewModel()
        {
            ReadParametersCommand = new RelayCommand(ExecuteReadParameters);
            WriteParametersCommand = new RelayCommand(ExecuteWriteParameters);
            RestoreDefaultsCommand = new RelayCommand(ExecuteRestoreDefaults);
        }

        #region Properties

        public double RatedPower
        {
            get => _ratedPower;
            set
            {
                _ratedPower = value;
                OnPropertyChanged();
            }
        }

        public double RatedVoltage
        {
            get => _ratedVoltage;
            set
            {
                _ratedVoltage = value;
                OnPropertyChanged();
            }
        }

        public double RatedCurrent
        {
            get => _ratedCurrent;
            set
            {
                _ratedCurrent = value;
                OnPropertyChanged();
            }
        }

        public double RatedSpeed
        {
            get => _ratedSpeed;
            set
            {
                _ratedSpeed = value;
                OnPropertyChanged();
            }
        }

        public double PositionGain
        {
            get => _positionGain;
            set
            {
                _positionGain = value;
                OnPropertyChanged();
            }
        }

        public double VelocityGain
        {
            get => _velocityGain;
            set
            {
                _velocityGain = value;
                OnPropertyChanged();
            }
        }

        public double IntegralTime
        {
            get => _integralTime;
            set
            {
                _integralTime = value;
                OnPropertyChanged();
            }
        }

        public double DerivativeTime
        {
            get => _derivativeTime;
            set
            {
                _derivativeTime = value;
                OnPropertyChanged();
            }
        }

        #endregion

        #region Commands

        public ICommand ReadParametersCommand { get; }
        public ICommand WriteParametersCommand { get; }
        public ICommand RestoreDefaultsCommand { get; }

        private async void ExecuteReadParameters()
        {
            // 模拟从驱动器读取参数
            await Task.Delay(500);
            
            // 这里应该实现实际的参数读取逻辑
            // 暂时使用模拟数据
        }

        private async void ExecuteWriteParameters()
        {
            // 模拟向驱动器写入参数
            await Task.Delay(500);
            
            // 这里应该实现实际的参数写入逻辑
        }

        private void ExecuteRestoreDefaults()
        {
            RatedPower = 1000.0;
            RatedVoltage = 220.0;
            RatedCurrent = 5.0;
            RatedSpeed = 3000.0;
            PositionGain = 100.0;
            VelocityGain = 50.0;
            IntegralTime = 10.0;
            DerivativeTime = 1.0;
        }

        #endregion

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }


}
