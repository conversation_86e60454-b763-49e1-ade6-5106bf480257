using System;
using System.Linq;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo.Adapter;

internal class ServoSerialPortResponse : IBetweenAndRequestInfo, IServoSerialPortResponse
{
    private readonly Action m_actionForReset;

    public ServoSerialPortResponse(Action action)
    {
        m_actionForReset = action;
    }

    public static byte[] StartCode => [0x55, 0xAA, 0xC8, 0xC9];

    public static byte[] EndCode => [0xA5, 0x5A];

    void IBetweenAndRequestInfo.OnParsingBody(byte[] body)
    {
        //例子：0x55, 0xAA, 0xC8, 0xC9, ********, 0xA5, 0x5A 
        //body为******部分
        using (var datas = new ByteBlock(body))
        {
            DataSize = datas.ReadUInt16(); //功能码和数据的长度
            if (datas.Length >= DataSize + 1 + 2) //至少1字节功能码&2字节校验
            {
                Data = datas.ReadToArray(DataSize);
                if (datas.CanReadLen >= 2) CRC = datas.ReadUInt16(); //2 byte      
                if (CRC != 65535) //为65535表示不启用CRC
                {
                    var crcCalc = SerialPortUtility.CalcCrc(Data);
                    if (CRC != crcCalc) m_actionForReset.Invoke();
                }
            }
            else
            {
                m_actionForReset.Invoke();
            }
        }
    }

    bool IBetweenAndRequestInfo.OnParsingEndCode(byte[] endCode)
    {
        return endCode.SequenceEqual(EndCode);
        //if (!endCode.SequenceEqual(EndCode))
        //    this.m_actionForReset.Invoke();
        //    return false;
    }

    bool IBetweenAndRequestInfo.OnParsingStartCode(byte[] startCode)
    {
        return startCode.SequenceEqual(StartCode);
        //if (!startCode.SequenceEqual(StartCode))
        //    this.m_actionForReset.Invoke();
        //    return false;
    }

    public byte Head0 => 0x55;

    public byte Head1 => 0xAA;

    public byte DstAddr => 0xC8;

    public byte SrcAddr => 0xC9;

    public ushort DataSize { get; set; }

    public byte[] Data { get; set; }

    public ushort CRC { get; set; }

    public byte Tail0 => 0xA5;

    public byte Tail1 => 0x5A;
}