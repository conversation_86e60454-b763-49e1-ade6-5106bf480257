using ModularAT.Localization.Resources;
using System;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.Messaging;
using CommunityToolkit.Mvvm.Messaging.Messages;
using ModularAT.Common.Log;
using ModularAT.Driver.Servo.Adapter;
using ModularAT.Driver.Servo.Packages;
using ModularAT.Entity;
using ModularAT.Entity.Config;
using ModularAT.Entity.Dtos.Servo;
using TouchSocket.Core;
using TouchSocket.SerialPorts;
using TouchSocket.Sockets;

namespace ModularAT.Driver.Servo;

public class ServoSerialPortClient
{
    private readonly SerialPortClient client = new();
    private bool _isConnected;
    private readonly object _lockObject = new object();
    private readonly IWaitingClient<SerialPortClient> waitClient;

    public bool IsConnected
    {
        get => _isConnected;
        private set
        {
            _isConnected = value;
            WeakReferenceMessenger.Default.Send(new ValueChangedMessage<bool>(_isConnected), "ServoSerialPort");
        }
    }

    public ServoSerialPortClient()
    {
        client.Connected = async (client, e) =>
        {
            IsConnected = client.Online;
            MsgToUiHelper.SendMsgInfo(Lang.ServoSerialPortClient_Driver_connected_successfully);

            // 连接成功后发送授权包并等待回复
            // await SendAuthorizationWithTimeout();
        };
        client.Disconnected = (client, e) =>
        {
            IsConnected = client.Online;
            return Task.CompletedTask;
        };
        waitClient = client.CreateWaitingClient(new WaitingOptions
        {
            FilterFunc = null
        });
    }

    public void Setup(SerialPortConfig config)
    {
        client.Setup(new TouchSocketConfig()
            .SetSerialPortOption(new SerialPortOption
            {
                BaudRate = config.SelectedBaudRate, //波特率
                DataBits = config.SelectedDataBits, //数据位
                Parity = config.SelectedParity, //校验位
                PortName = config.SelectedPort, //COM
                StopBits = config.SelectedStopBits //停止位
            })
            .SetSerialDataHandlingAdapter(() => new ServoCustomBetweenAndAdapter()) //头尾适配
            //.SetSerialDataHandlingAdapter(() => new TerminatorPackageAdapter(2, [0xA5, 0x5A]))//尾部适配，miniseze记得修改
            .SetSendTimeout(10000)
            .ConfigureContainer(a => a.AddLogger(new ServoLog()))
        );
        client.Config.MinBufferSize(1024 * 10);
        client.Config.MaxBufferSize(1024 * 1024 * 10);
        client.Received = Receive;
        TouchSocketBitConverter.DefaultEndianType = EndianType.Little;
    }

    public Task Connnect()
    {
        return (client as IConnectObject).TryConnectAsync();
    }

    public void Close()
    {
        client.SafeClose();
    }

    /// <summary>
    ///     心跳
    ///     55 AA C9 C8 01 00 0C 05 AF A5 5A
    /// </summary>
    private void Heartbeat()
    {
        Task.Run(async () =>
        {
            while (IsConnected)
                try
                {
                    RequestAuthorization();
                    await Task.Delay(1000);
                }
                catch (Exception ex)
                {
                    client.Logger.Error(ex, Lang.ServoSerialPortClient_Servo_heartbeat_failed);
                    IsConnected = false;
                }
        });
    }


    /// <summary>
    ///     根据[data]部分构建完整数据帧
    ///     比如完整数据为： 55 AA C9 C8 01 00 0F EF F1 A5 5A
    ///     自动加前缀:55 AA、站地址:C9 C8、长度:01 00、CRC校验:EF F1、后缀:A5 5A
    ///     只需要入参 [0x0F]
    /// </summary>
    /// <param name="data">数据部分,指功能码和后面的数据，不包括长度</param>
    private byte[] BuildBytes(byte[] data)
    {
        var lengthBytes = BitConverter.GetBytes((ushort)data.Length);
        byte[] prefix = [.. new byte[] { 0x55, 0xAA, 0xC9, 0xC8 }, .. lengthBytes];
        var crc = SerialPortUtility.CalcCrc(data);
        using var block = new ValueByteBlock();
        block.Write(prefix);
        block.Write(data);
        block.Write(crc);
        block.Write([0xA5, 0x5A]);
        return [.. block];
    }

    public void Send(ServoPackageBase servoPackage)
    {
        var requestInfo = new ServoSerialPortRequest();
        requestInfo.Package = servoPackage;
        client.Send(requestInfo);
        client.Logger.Info($"发送数据：{requestInfo.BuildAsBytes().ByBytesToHexString(" ")}");
    }

    /// <summary>
    ///     直接发送数据部分数组
    ///     比如完整数据为： 55 AA C9 C8 01 00 0F EF F1 A5 5A
    ///     自动加前缀:55 AA、站地址:C9 C8、长度:01 00、CRC校验:EF F1、后缀:A5 5A
    ///     只需要入参 [0x0F]
    /// </summary>
    /// <param name="data">数据部分</param>
    public void SendData(byte[] data)
    {
        var arr = BuildBytes(data);
        client.Send(arr);
        client.Logger.Info($"发送数据：{arr.ByBytesToHexString(" ")}");
    }


    public async Task<byte[]> SendWaitReturnAsync(byte[] inputData, int timeout = 5000)
    {
        // 检查是否已连接
        if (!IsConnected) return null;
        // 发送数据并等待返回
        var result = await waitClient.SendThenReturnAsync(inputData, timeout);
        // 记录发送的数据和返回的数据
        client.Logger.Info($"发送数据：{inputData.ByBytesToHexString(" ")}; 收到回应：{result?.ByBytesToHexString(" ")}");
        // 返回结果
        return result;
    }

    private Task Receive(SerialPortClient client, ReceivedDataEventArgs e)
    {
        // return Task.Run(() =>
        // {
        //收到信息
        if (e.RequestInfo is ServoSerialPortResponse requestInfo)
        {
            var data = requestInfo.Data;
            if (data == null) return Task.CompletedTask;
            var firstCode = data.FirstOrDefault();
            switch (firstCode)
            {
                case (byte)FirstCode.FW_UPDATE_VERREPLY: //固件升级信息
                    //var ret = new FirmwareUpdatePackage();
                    //ret.Unpackage(new ByteBlock(data));
                    break;
                case (byte)FirstCode.FW_HANDLE_PARA_INFO_GET: //固件参数获取
                    break;
                case (byte)FirstCode.FW_HANDLE_PARA_INFO_REPLY: //固件参数应答?
                    break;
                case (byte)FirstCode.FW_STS_RESP_CMD: //状态信息
                    break;
                case (byte)FirstCode.FW_DATA_RESP_CMD: //设备数据信息
                    ServoReceivedDeviceDataResp(data);
                    break;
                case (byte)FirstCode.FW_LOG_CMD: //设备日志
                    break;
            }
        }

        // else
        // {
        //     var data = e;
        // }
        return Task.CompletedTask;
        // });
    }

    ScopeChannelsPackage _scopePackage = new ScopeChannelsPackage();

    public void ServoReceivedDeviceDataResp(byte[] data)
    {
        if (data.Length < 3)
        {
            switch (data.ByBytesToHexString(" "))
            {
                case "0B AA":
                    MsgToUiHelper.SendMsgNotice(Lang.ServoSerialPortClient_Driver_parameter_recovery_successful);
                    break;
            }

            return;
        }

        //只更新当前选择轴数据,示波器不传AxisSelect
        var secondCode = (SecondDeviceDataRespCode)data[2];
        if (data[1] is (int)SecondDeviceDataRespCode.Scope
            or (int)SecondDeviceDataRespCode.ErrDataRecord
            or (int)SecondDeviceDataRespCode.ProcessRecord
            or (int)SecondDeviceDataRespCode.PlcCtrlSignalRecord
            or (int)SecondDeviceDataRespCode.PlcCtrlSignalCount)
            secondCode = (SecondDeviceDataRespCode)data[1];

        using var block = new ByteBlock(data);
        switch (secondCode)
        {
            case SecondDeviceDataRespCode.Motor:
            {
                var ret = new MotorParamPackage();
                ret.Unpackage(block);
                if (ServoContext.Instance.AxisSelect != ret.AxisSelect) break;
                ServoPackageMapper.MapperDic[secondCode].Map(ret, ServoContext.Instance.MotorParam);
            }
                break;
            case SecondDeviceDataRespCode.System:
            {
                var ret = new SystemParamPackage();
                ret.Unpackage(block);
                if (ServoContext.Instance.AxisSelect != ret.AxisSelect) break;
                ServoPackageMapper.MapperDic[secondCode].Map(ret, ServoContext.Instance.SystemParam);
            }
                break;
            case SecondDeviceDataRespCode.Encoder:
            {
                var ret = new EncoderParamPackage();
                ret.Unpackage(block);
                if (ServoContext.Instance.AxisSelect != ret.AxisSelect) break;
                ServoPackageMapper.MapperDic[secondCode].Map(ret, ServoContext.Instance.EncoderParam);
            }
                break;
            case SecondDeviceDataRespCode.Protect:
            {
                var ret = new ProtectParamPackage();
                ret.Unpackage(block);
                if (ServoContext.Instance.AxisSelect != ret.AxisSelect) break;
                ServoPackageMapper.MapperDic[secondCode].Map(ret, ServoContext.Instance.ProtectParam);
            }
                break;
            case SecondDeviceDataRespCode.Error:
            {
                var ret = new ErrorRecordParamPackage();
                ret.Unpackage(block);
                if (ServoContext.Instance.AxisSelect != ret.AxisSelect) break;
                ServoPackageMapper.MapperDic[secondCode].Map(ret, ServoContext.Instance.ErrorRecordParam);
            }
                break;
            case SecondDeviceDataRespCode.Control:
            {
                var ret = new ControlStateParamPackage();
                ret.Unpackage(block);
                if (ServoContext.Instance.AxisSelect != ret.AxisSelect) break;
                ServoPackageMapper.MapperDic[secondCode].Map(ret, ServoContext.Instance.ControlStateParam);
            }
                break;
            case SecondDeviceDataRespCode.Position:
            {
                var ret = new PositionParamPackage();
                ret.Unpackage(block);
                if (ServoContext.Instance.AxisSelect != ret.AxisSelect) break;
                ServoPackageMapper.MapperDic[secondCode].Map(ret, ServoContext.Instance.PositionParam);
            }
                break;
            case SecondDeviceDataRespCode.Speed:
            {
                var ret = new SpeedParamPackage();
                ret.Unpackage(block);
                if (ServoContext.Instance.AxisSelect != ret.AxisSelect) break;
                ServoPackageMapper.MapperDic[secondCode].Map(ret, ServoContext.Instance.SpeedParam);
            }
                break;
            case SecondDeviceDataRespCode.Torque:
            {
                var ret = new TorqueParamPackage();
                ret.Unpackage(block);
                if (ServoContext.Instance.AxisSelect != ret.AxisSelect) break;
                ServoPackageMapper.MapperDic[secondCode].Map(ret, ServoContext.Instance.TorqueParam);
            }
                break;
            case SecondDeviceDataRespCode.Scope:
            {
                _scopePackage.Unpackage(block);
                lock (_lockObject) // 确保线程安全
                {
                    ServoContext.Instance.ScopeFeedbacks.AddLast(_scopePackage.Channels);
                }

                ServoContext.Instance.ScopeFeedback = _scopePackage.Channels;
                // ServoContext.Instance.ScopeReceived.BeginInvoke(_scopePackage.Channels, null, null);
            }
                break;
            case SecondDeviceDataRespCode.ErrDataRecord:
            {
                var ret = new ErrDataChannelsPackage();
                ret.Unpackage(block);
                ServoContext.Instance.ErrDataChannelDraw?.Invoke(ret.ErrAxis, (byte)ret.ErrDataType, ret.Datas);
            }
                break;
            case SecondDeviceDataRespCode.ProcessRecord:
            {
                var ret = new ProcessRecordChannelsPackage();
                ret.Unpackage(block);
                ServoContext.Instance.ProcessRecordChannelDraw?.Invoke(ret.RecordAxis, (byte)ret.RecordDataType,
                    ret.Datas);
                break;
            }
            case SecondDeviceDataRespCode.PlcCtrlSignalRecord:
            {
                var ret = new PlcCtrlSignalRecordChannelsPackage();
                ret.Unpackage(block);
                ServoContext.Instance.PlcCtrlSignalRecordChannelDraw?.Invoke(ret.RecordAxis,
                    (byte)ThirdPlcCtrlSignalRecordDataType.BrakeSignal,
                    ret.BrakeRecordDatas);
                ServoContext.Instance.PlcCtrlSignalRecordChannelDraw?.Invoke(ret.RecordAxis,
                    (byte)ThirdPlcCtrlSignalRecordDataType.AccelerationSignal,
                    ret.AccelerationRecordDatas);
                break;
            }
            case SecondDeviceDataRespCode.PlcCtrlSignalCount:
            {
                var ret = new PlcCtrlSignalCountPackage();
                ret.Unpackage(block);

                // 转换为DTO对象，避免直接传递Package对象到UI层
                var dto = new PlcCtrlSignalCountDto
                {
                    RecordAxis = ret.RecordAxis,
                    StopSignalCount = ret.StopSignalCount,
                    AccSignalCount = ret.AccSignalCount
                };

                // 发送DTO消息到UI (带Token)
                WeakReferenceMessenger.Default.Send(new ValueChangedMessage<PlcCtrlSignalCountDto>(dto),
                    "PlcCtrlSignalCount");
                break;
            }
        }
    }

    #region 预设数据包

    /// <summary>
    /// 发送授权包并等待回复，使用TouchSocket的发送后等待方法
    /// </summary>
    private async Task SendAuthorizationWithTimeout()
    {
        try
        {
            if (!IsConnected) return;

            var adapter = client.DataHandlingAdapter as ServoCustomBetweenAndAdapter;
            adapter?.ManualReset();

            // 使用TouchSocket的发送后等待方法
            var authData = new byte[] { 0x55, 0xAA, 0xC9, 0xC8, 0x01, 0x00, 0x0C, 0x8C, 0xC1, 0xA5, 0x5A };
            var response = await SendWaitReturnAsync(authData, 200000);

            if (response != null && response.Length > 0)
            {
                // 检查回复是否为设备数据信息 (FW_DATA_RESP_CMD = 0x0B)
                var firstCode = response.FirstOrDefault();
                if (firstCode == (byte)FirstCode.FW_DATA_RESP_CMD)
                {
                    // 授权成功，刷新数据
                    ServoContext.Instance.RefreshData();
                    return;
                }
            }

            // 未收到正确回复，断开连接
            MsgToUiHelper.SendMsgError(Lang.ServoSerialPortClient_Serial_port_device_not_supported_no_authorization_response);
            Close();
        }
        catch (Exception ex)
        {
            client.Logger?.Error(ex, Lang.ServoSerialPortClient_Error_sending_authorization_packet);
            MsgToUiHelper.SendMsgError(Lang.ServoSerialPortClient_Serial_port_device_not_supported_authorization_failed);
            Close();
        }
    }

    public void RequestAuthorization()
    {
        if (IsConnected)
        {
            var adapter = client.DataHandlingAdapter as ServoCustomBetweenAndAdapter;
            adapter?.ManualReset();
            client.SendAsync([0x55, 0xAA, 0xC9, 0xC8, 0x01, 0x00, 0x0C, 0x8C, 0xC1, 0xA5, 0x5A]);
        }
    }

    public void ErrorReset()
    {
        if (IsConnected)
            //0x55, 0xAA, 0xC9, 0xC8, 0x01, 0x00, 0x0E, 0xCE, 0xE1, 0xA5, 0x5A
            SendData([0x0E]);
    }

    public void ErrorRecordClear()
    {
        if (IsConnected)
            //55 AA C9 C8 01 00 0F EF F1 A5 5A 
            SendData([0x0F]);
    }

    public void ParaClear()
    {
        //55 AA C9 C8 02 00 08 AA 09 9D A5 5A 
        if (IsConnected) SendData([0x08, 0xAA]);
    }


    public void GetErrRecord()
    {
        if (IsConnected) SendData([0x10]); //55 AA C9 C8 01 00 10 31 12 A5 5A   
    }

    /// <summary>
    /// 系统软复位
    /// </summary>
    public void SoftReset()
    {
        if (IsConnected) SendData([0x12]); //55 AA C9 C8 01 00 12 31 12 A5 5A   
    }

    public void SetDriverMode(DriverModeSetPackage package)
    {
        if (IsConnected)
            //SendData([(byte)FirstDriverModeSetCode.FW_DRIVER_MODE_SET,package.ControlBy, package.Mode, package.SubMode]);
            Send(package);
    }

    public void SetCdmTargetPos(uint targetPosition)
    {
        if (IsConnected)
            SendData([(byte)FirstCode.CDM_TARGET_POS_SET_CMD, .. BitConverter.GetBytes(targetPosition)]);
    }

    // 设置CDM驱动器动子检测位置参数
    public void SetCdmMoverCheckPos(uint moverCheckPosition)
    {
        if (IsConnected)
            SendData([(byte)FirstCode.CDM_MOVER_CHECK_POS_SET_CMD, .. BitConverter.GetBytes(moverCheckPosition)]);
    }  

    // 设置CDM驱动器使能电机位置参数
    public void SetCdmFocEnsPos(uint focEnsPosition)
    {
        if (IsConnected)
            SendData([(byte)FirstCode.CDM_FOC_ENS_POS_SET_CMD, .. BitConverter.GetBytes(focEnsPosition)]);
    }

    // 获取过程调试监控数据
    public void GetProcessDbgRecord()
    {
        if (IsConnected) SendData([(byte)FirstCode.GET_PROCESS_DBG_RECORD_CMD]); //55 AA C9 C8 01 00 16 F7 72 A5 5A
    }

    public void GetPlcCtrlSignalRecord()
    {
        if (IsConnected) SendData([(byte)FirstCode.GET_PLC_CTRL_SIGNAL_CMD]); //55 AA C9 C8 01 00 17 D6 62 A5 5A
    }

    public void GetPlcCtrlSignalCount()
    {
        if (IsConnected) SendData([(byte)FirstCode.GET_PLC_CTRL_SIGNAL_CNT]);
    }

    #endregion
}