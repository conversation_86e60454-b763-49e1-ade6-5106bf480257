using ModularAT.Localization.Resources;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using MiniExcelLibs.Attributes;
using ModularAT.Common.Helper;
using ModularAT.Entity.Enum;
using ModularAT.Entity.Extension;
using ModularAT.Localization;

namespace ModularAT.Entity;

[Serializable]
public class ParameterModel : ParameterBase, IAddressItem, IComparable, IDataErrorInfo
{
    private List<int> _AxisNumrs;

    private string _EditString;

    private long? _EditValue;

    private bool _HasError;

    private bool _IsMonitorVar;

    private long? _Value;
    public long? editValue;
    private long? int64Value;
    private long? readValue;

    public ParameterModel()
    {
        Neighbors = [];
        Children = [];
    }

    public IList<IAddressItem> Neighbors { get; set; }

    public IList<IAddressItem> Children { get; set; }

    public IAddressItem Parent { get; set; }

    public List<int> AxisNumrs
    {
        get => _AxisNumrs;
        set
        {
            _AxisNumrs = value;
            OnPropertyChanged();
        }
    }

    public bool IsSelected { get; set; }

    [ExcelColumnName("Max")] public string MaxValue { get; set; }
    [ExcelColumnName("Min")] public string MinValue { get; set; }

    [ExcelColumnName("Default")] public string DefaultValue { get; set; }

    [ExcelIgnore] public string Group { get; set; }
    public bool IsRefresh { get; set; }
    public float Coefficient { get; set; }

    /// <summary>
    ///     Index+SubNo
    /// </summary>
    [ExcelColumnName("No.")]
    public string No { get; set; }

    public string Name { get; set; }

    /// <summary>
    ///     一级参数分组序号
    /// </summary>
    public string Index { get; set; }

    public int? SubIndex { get; set; }

    public string SubNo { get; set; }

    public DataType DataType { get; set; }

    public string Unit { get; set; }
    public int IsHide { get; set; }

    public string Description
    {
        get => LocalizationManager.GetInstance().GetDbLocalizedString(Name) ?? field;
        set;
    }

    public string Access { get; set; }

    // public string PointNumber { get; set; }

    // public long? EditValue
    // {
    //     get => _EditValue;
    //     set
    //     {
    //         _EditValue = value;
    //         OnPropertyChanged();
    //     }
    // }
    

    public string Error => null;

    public string this[string columnName]
    {
        get
        {
            if (columnName == nameof(EditString))
            {
                return ValidateEditString();
            }
            return null;
        }
    }

    public string ValidateEditString()
    {
        if (string.IsNullOrEmpty(_EditString))
        {
            return Lang.ParameterModel_Value_cannot_be_empty;
        }

        // 检查是否包含非法字符
        if (_EditString.Contains(" ") || _EditString.Contains("\t"))
        {
            return Lang.ParameterModel_Input_value_cannot_contain_spaces_tabs;
        }

        // 检查是否为纯数字格式（允许负号和小数点）
        if (!System.Text.RegularExpressions.Regex.IsMatch(_EditString, @"^-?\d+(\.\d+)?$"))
        {
            return Lang.ParameterModel_Please_enter_valid_number_format;
        }

        try
        {
            switch (DataType)
            {
                case DataType.UINT16:
                    return ValidateUInt16();
                case DataType.UINT32:
                    return ValidateUInt32();
                case DataType.INT16:
                    return ValidateInt16();
                case DataType.INT32:
                    return ValidateInt32();
                case DataType.FLOAT:
                    return ValidateFloat();
                case DataType.DOUBLE:
                    return ValidateDouble();
                default:
                    return ValidateGenericNumber();
            }
        }
        catch (Exception ex)
        {
            return $"数据验证错误: {ex.Message}";
        }
    }

    private string ValidateUInt16()
    {
        if (!ushort.TryParse(_EditString, out var value))
        {
            return Lang.ParameterModel_Please_enter_valid_16bit_unsigned_integer;
        }

        if (ushort.TryParse(MinValue, out var min) && value < min)
        {
            return $"值不能小于最小值: {min}";
        }

        if (ushort.TryParse(MaxValue, out var max) && value > max)
        {
            return $"值不能大于最大值: {max}";
        }

        return null;
    }

    private string ValidateUInt32()
    {
        if (!uint.TryParse(_EditString, out var value))
        {
            return Lang.ParameterModel_Please_enter_valid_32bit_unsigned_integer;
        }

        if (uint.TryParse(MinValue, out var min) && value < min)
        {
            return $"值不能小于最小值: {min}";
        }

        if (uint.TryParse(MaxValue, out var max) && value > max)
        {
            return $"值不能大于最大值: {max}";
        }

        return null;
    }

    private string ValidateInt16()
    {
        if (!short.TryParse(_EditString, out var value))
        {
            return Lang.ParameterModel_Please_enter_valid_16bit_signed_integer;
        }

        if (short.TryParse(MinValue, out var min) && value < min)
        {
            return $"值不能小于最小值: {min}";
        }

        if (short.TryParse(MaxValue, out var max) && value > max)
        {
            return $"值不能大于最大值: {max}";
        }

        return null;
    }

    private string ValidateInt32()
    {
        if (!int.TryParse(_EditString, out var value))
        {
            return Lang.ParameterModel_Please_enter_valid_32bit_signed_integer;
        }

        if (int.TryParse(MinValue, out var min) && value < min)
        {
            return $"值不能小于最小值: {min}";
        }

        if (int.TryParse(MaxValue, out var max) && value > max)
        {
            return $"值不能大于最大值: {max}";
        }

        return null;
    }

    private string ValidateFloat()
    {
        if (!float.TryParse(_EditString, out var value))
        {
            return Lang.ParameterModel_Please_enter_valid_floating_point_number;
        }

        if (float.IsNaN(value) || float.IsInfinity(value))
        {
            return Lang.ParameterModel_Input_value_cannot_be_nan_or_infinity;
        }

        if (float.TryParse(MinValue, out var min) && value < min)
        {
            return $"值不能小于最小值: {min}";
        }

        if (float.TryParse(MaxValue, out var max) && value > max)
        {
            return $"值不能大于最大值: {max}";
        }

        return null;
    }

    private string ValidateDouble()
    {
        if (!double.TryParse(_EditString, out var value))
        {
            return Lang.ParameterModel_Please_enter_valid_double_precision_floating_point_number;
        }

        if (double.IsNaN(value) || double.IsInfinity(value))
        {
            return Lang.ParameterModel_Input_value_cannot_be_nan_or_infinity;
        }

        if (double.TryParse(MinValue, out var min) && value < min)
        {
            return $"值不能小于最小值: {min}";
        }

        if (double.TryParse(MaxValue, out var max) && value > max)
        {
            return $"值不能大于最大值: {max}";
        }

        return null;
    }

    private string ValidateGenericNumber()
    {
        if (!double.TryParse(_EditString, out var value))
        {
            return Lang.ParameterModel_Please_enter_valid_number;
        }

        if (double.TryParse(MinValue, out var min) && value < min)
        {
            return $"值不能小于最小值: {min}";
        }

        if (double.TryParse(MaxValue, out var max) && value > max)
        {
            return $"值不能大于最大值: {max}";
        }

        return null;
    }

    public string EditString
    {
        get => _EditString;
        set
        {
            _EditString = value;
            _HasError = !string.IsNullOrEmpty(this[nameof(EditString)]);

            // 当EditString变更时，尝试验证并更新Int64Value
            if (!_HasError && !string.IsNullOrEmpty(value))
            {
                switch (DataType)
                {
                    case DataType.UINT16:
                    case DataType.UINT32:
                        if (ulong.TryParse(value, out var uintValue) &&
                            ulong.TryParse(MinValue, out var uintMin) &&
                            ulong.TryParse(MaxValue, out var uintMax) &&
                            uintValue >= uintMin && uintValue <= uintMax)
                        {
                            Int64Value = (long)uintValue;
                        }
                        break;

                    case DataType.INT16:
                    case DataType.INT32:
                        if (long.TryParse(value, out var intValue) &&
                            long.TryParse(MinValue, out var intMin) &&
                            long.TryParse(MaxValue, out var intMax) &&
                            intValue >= intMin && intValue <= intMax)
                        {
                            Int64Value = intValue;
                        }
                        break;
                }
            }

            OnPropertyChanged();
        }
    }

    public long? ReadValue
    {
        get => readValue;
        set
        {
            readValue = value;
            OnPropertyChanged();
        }
    }

    public long? Value
    {
        get => _Value;
        set
        {
            _Value = value;
            OnPropertyChanged();
        }
    }

    public long? Int64Value
    {
        get => int64Value;
        set
        {
            int64Value = value;
            OnPropertyChanged();
        }
    }

    public int RolePermission { get; set; }

    public int CompareTo(object obj)
    {
        if (!(obj is ParameterModel parameter)) return 1;
        if (Index != parameter.Index) return Convert.ToInt16(Index) - Convert.ToInt16(parameter.Index);
        if (SubNo == null && parameter.SubNo == null) return 0;
        if (SubNo != null && parameter.SubNo != null) return Convert.ToInt16(SubNo) - Convert.ToInt16(parameter.SubNo);
        if (SubNo == null) return -1;
        return 1;
    }

}