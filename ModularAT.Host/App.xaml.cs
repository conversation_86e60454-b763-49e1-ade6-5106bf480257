using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using ModularAT.Shared;

namespace ModularAT.Host;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override async void OnStartup(StartupEventArgs e)
    {
        // 创建主机
        _host = CreateHostBuilder(e.Args).Build();

        // 启动主机
        await _host.StartAsync();

        // 显示登录窗口
        var loginWindow = _host.Services.GetRequiredService<LoginWindow>();
        loginWindow.Show();

        base.OnStartup(e);
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }

        base.OnExit(e);
    }

    private static IHostBuilder CreateHostBuilder(string[] args) =>
        Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder(args)
            .ConfigureSharedInfrastructure(ProcessType.Host)
            .ConfigureServices((context, services) =>
            {
                // 注册窗口和视图模型
                services.AddSingleton<LoginWindow>();
                services.AddSingleton<LoginViewModel>();
                services.AddSingleton<MainWindow>();
                services.AddSingleton<MainViewModel>();
            });
}

