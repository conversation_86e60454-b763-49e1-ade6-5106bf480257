using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;
using Microsoft.Extensions.DependencyInjection;
using ModularAT.Shared;

namespace ModularAT.Host
{
    public class LoginViewModel : INotifyPropertyChanged
    {
        private readonly IProcessManager _processManager;
        private readonly ISharedConfigurationService _configService;
        private readonly IServiceProvider _serviceProvider;
        
        private string _username = string.Empty;
        private string _password = string.Empty;
        private ProcessType _selectedProcessType = ProcessType.Driver;
        private bool _rememberPassword;
        private string _errorMessage = string.Empty;

        public LoginViewModel(IProcessManager processManager, 
                            ISharedConfigurationService configService,
                            IServiceProvider serviceProvider)
        {
            _processManager = processManager;
            _configService = configService;
            _serviceProvider = serviceProvider;
            
            LoginCommand = new RelayCommand(ExecuteLogin, CanExecuteLogin);
        }

        public string Username
        {
            get => _username;
            set
            {
                _username = value;
                OnPropertyChanged();
                ((RelayCommand)LoginCommand).RaiseCanExecuteChanged();
            }
        }

        public string Password
        {
            get => _password;
            set
            {
                _password = value;
                OnPropertyChanged();
                ((RelayCommand)LoginCommand).RaiseCanExecuteChanged();
            }
        }

        public ProcessType SelectedProcessType
        {
            get => _selectedProcessType;
            set
            {
                _selectedProcessType = value;
                OnPropertyChanged();
            }
        }

        public bool RememberPassword
        {
            get => _rememberPassword;
            set
            {
                _rememberPassword = value;
                OnPropertyChanged();
            }
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                _errorMessage = value;
                OnPropertyChanged();
            }
        }

        public ICommand LoginCommand { get; }

        private bool CanExecuteLogin()
        {
            return !string.IsNullOrWhiteSpace(Username) && !string.IsNullOrWhiteSpace(Password);
        }

        private async void ExecuteLogin()
        {
            try
            {
                ErrorMessage = string.Empty;

                // 这里应该进行实际的用户认证
                // 为了演示，我们简单验证用户名和密码
                if (await AuthenticateUser(Username, Password))
                {
                    // 创建用户认证信息
                    var userInfo = new UserAuthenticationInfo
                    {
                        UserName = Username,
                        UserId = Username,
                        Roles = new[] { "User" },
                        Permissions = new[] { "Read", "Write" },
                        LoginTime = DateTime.Now,
                        SelectedProcessType = SelectedProcessType
                    };

                    // 创建启动配置
                    var config = ConfigurationHelper.CreateDefaultConfig(SelectedProcessType, userInfo);

                    // 启动选定的进程
                    await _processManager.StartProcessAsync(SelectedProcessType, config);

                    // 关闭登录窗口
                    Application.Current.MainWindow?.Close();
                }
                else
                {
                    ErrorMessage = "用户名或密码错误";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"登录失败: {ex.Message}";
            }
        }

        private async Task<bool> AuthenticateUser(string username, string password)
        {
            // 模拟异步认证过程
            await Task.Delay(500);
            
            // 简单的演示认证逻辑
            // 在实际应用中，这里应该调用认证服务
            return username == "admin" && password == "123456";
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // 简单的RelayCommand实现
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged;

        public bool CanExecute(object? parameter)
        {
            return _canExecute?.Invoke() ?? true;
        }

        public void Execute(object? parameter)
        {
            _execute();
        }

        public void RaiseCanExecuteChanged()
        {
            CanExecuteChanged?.Invoke(this, EventArgs.Empty);
        }
    }
}
