<Window x:Class="ModularAT.Host.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="ModularAT 登录" Height="400" Width="600"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="3*"/>
        </Grid.ColumnDefinitions>
        
        <!-- 左侧背景 -->
        <Border Grid.Column="0" Background="#FF2196F3">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <TextBlock Text="ModularAT" FontSize="32" FontWeight="Bold" 
                          Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="模块化自动测试系统" FontSize="16" 
                          Foreground="White" HorizontalAlignment="Center" Margin="0,10,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- 右侧登录表单 -->
        <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="40">
            <TextBlock Text="用户登录" FontSize="24" FontWeight="Bold" 
                      HorizontalAlignment="Center" Margin="0,0,0,30"/>
            
            <!-- 用户名 -->
            <TextBlock Text="用户名:" Margin="0,0,0,5"/>
            <TextBox Name="UsernameTextBox" Height="35" FontSize="14" 
                    Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"/>
            
            <!-- 密码 -->
            <TextBlock Text="密码:" Margin="0,15,0,5"/>
            <PasswordBox Name="PasswordBox" Height="35" FontSize="14"/>
            
            <!-- 进程类型选择 -->
            <TextBlock Text="进程类型:" Margin="0,15,0,5"/>
            <ComboBox Name="ProcessTypeComboBox" Height="35" FontSize="14"
                     SelectedItem="{Binding SelectedProcessType}">
                <ComboBoxItem Content="驱动器进程" Tag="Driver"/>
                <ComboBoxItem Content="控制器进程" Tag="Controller"/>
            </ComboBox>
            
            <!-- 记住密码 -->
            <CheckBox Content="记住密码" Margin="0,15,0,0" 
                     IsChecked="{Binding RememberPassword}"/>
            
            <!-- 错误信息 -->
            <TextBlock Text="{Binding ErrorMessage}" Foreground="Red" 
                      Margin="0,10,0,0" TextWrapping="Wrap"/>
            
            <!-- 登录按钮 -->
            <Button Content="登录" Height="40" FontSize="16" FontWeight="Bold"
                   Background="#FF2196F3" Foreground="White" Margin="0,20,0,0"
                   Command="{Binding LoginCommand}"/>
        </StackPanel>
    </Grid>
</Window>
