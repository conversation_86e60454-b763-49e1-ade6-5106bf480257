using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using ModularAT.Shared;

namespace ModularAT.Host
{
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly IProcessManager _processManager;
        private readonly ISharedConfigurationService _configService;
        
        private string _currentUser = "未登录";
        private string _systemStatus = "系统正常";
        private string _lastUpdateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        private string _configurationInfo = "配置已加载";
        private string _statusMessage = "系统就绪";

        public MainViewModel(IProcessManager processManager, ISharedConfigurationService configService)
        {
            _processManager = processManager;
            _configService = configService;
            
            RunningProcesses = new ObservableCollection<ProcessInfo>();
            
            StartDriverCommand = new RelayCommand(() => StartProcess(ProcessType.Driver));
            StartControllerCommand = new RelayCommand(() => StartProcess(ProcessType.Controller));
            StopProcessCommand = new RelayCommand(StopSelectedProcess, () => SelectedProcess != null);
            
            // 订阅进程状态变化事件
            _processManager.ProcessStatusChanged += OnProcessStatusChanged;
            
            // 初始化数据
            InitializeData();
        }

        public ObservableCollection<ProcessInfo> RunningProcesses { get; }

        public ProcessInfo? SelectedProcess { get; set; }

        public string CurrentUser
        {
            get => _currentUser;
            set
            {
                _currentUser = value;
                OnPropertyChanged();
            }
        }

        public string SystemStatus
        {
            get => _systemStatus;
            set
            {
                _systemStatus = value;
                OnPropertyChanged();
            }
        }

        public string LastUpdateTime
        {
            get => _lastUpdateTime;
            set
            {
                _lastUpdateTime = value;
                OnPropertyChanged();
            }
        }

        public string ConfigurationInfo
        {
            get => _configurationInfo;
            set
            {
                _configurationInfo = value;
                OnPropertyChanged();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        public ICommand StartDriverCommand { get; }
        public ICommand StartControllerCommand { get; }
        public ICommand StopProcessCommand { get; }

        private async void StartProcess(ProcessType processType)
        {
            try
            {
                StatusMessage = $"正在启动{GetProcessTypeName(processType)}...";
                
                // 创建默认配置
                var userInfo = new UserAuthenticationInfo
                {
                    UserName = CurrentUser,
                    UserId = CurrentUser,
                    Roles = new[] { "User" },
                    Permissions = new[] { "Read", "Write" },
                    LoginTime = DateTime.Now,
                    SelectedProcessType = processType
                };
                
                var config = ConfigurationHelper.CreateDefaultConfig(processType, userInfo);
                
                // 启动进程
                await _processManager.StartProcessAsync(processType, config);
                
                StatusMessage = $"{GetProcessTypeName(processType)}启动成功";
            }
            catch (Exception ex)
            {
                StatusMessage = $"启动失败: {ex.Message}";
            }
        }

        private async void StopSelectedProcess()
        {
            if (SelectedProcess == null) return;

            try
            {
                StatusMessage = $"正在停止{SelectedProcess.ProcessName}...";
                await _processManager.StopProcessAsync(SelectedProcess.ProcessType);
                StatusMessage = $"{SelectedProcess.ProcessName}已停止";
            }
            catch (Exception ex)
            {
                StatusMessage = $"停止失败: {ex.Message}";
            }
        }

        private void OnProcessStatusChanged(object? sender, ProcessStatusChangedEventArgs e)
        {
            App.Current.Dispatcher.Invoke(() =>
            {
                var existingProcess = RunningProcesses.FirstOrDefault(p => p.ProcessType == e.ProcessType);
                
                if (e.IsRunning)
                {
                    if (existingProcess == null)
                    {
                        RunningProcesses.Add(new ProcessInfo
                        {
                            ProcessType = e.ProcessType,
                            ProcessName = GetProcessTypeName(e.ProcessType),
                            Status = "运行中"
                        });
                    }
                    else
                    {
                        existingProcess.Status = "运行中";
                    }
                }
                else
                {
                    if (existingProcess != null)
                    {
                        RunningProcesses.Remove(existingProcess);
                    }
                }
                
                LastUpdateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            });
        }

        private void InitializeData()
        {
            CurrentUser = "管理员";
            SystemStatus = "系统正常运行";
            ConfigurationInfo = "数据库连接正常\n进程间通信已建立\n配置文件已加载";
        }

        private static string GetProcessTypeName(ProcessType processType)
        {
            return processType switch
            {
                ProcessType.Driver => "驱动器进程",
                ProcessType.Controller => "控制器进程",
                _ => "未知进程"
            };
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class ProcessInfo : INotifyPropertyChanged
    {
        private string _status = string.Empty;

        public ProcessType ProcessType { get; set; }
        public string ProcessName { get; set; } = string.Empty;
        
        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
