<Window x:Class="ModularAT.Host.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ModularAT.Host"
        mc:Ignorable="d"
        Title="ModularAT 主控制台" Height="600" Width="900"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#FF2196F3" Height="60">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="ModularAT 主控制台" FontSize="20" FontWeight="Bold"
                          Foreground="White" VerticalAlignment="Center"/>
                <TextBlock Text="{Binding CurrentUser}" FontSize="14"
                          Foreground="White" VerticalAlignment="Center" Margin="20,0,0,0"/>
            </StackPanel>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 进程管理 -->
            <GroupBox Grid.Column="0" Header="进程管理" Margin="0,0,10,0">
                <StackPanel Margin="10">
                    <TextBlock Text="运行中的进程:" FontWeight="Bold" Margin="0,0,0,10"/>
                    <ListBox ItemsSource="{Binding RunningProcesses}" Height="200">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Ellipse Width="10" Height="10" Fill="Green" Margin="0,0,10,0"/>
                                    <TextBlock Text="{Binding ProcessName}"/>
                                    <TextBlock Text="{Binding Status}" Margin="10,0,0,0"/>
                                </StackPanel>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                    <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                        <Button Content="启动驱动器进程" Command="{Binding StartDriverCommand}"
                               Width="120" Height="30" Margin="0,0,10,0"/>
                        <Button Content="启动控制器进程" Command="{Binding StartControllerCommand}"
                               Width="120" Height="30"/>
                    </StackPanel>

                    <Button Content="停止选中进程" Command="{Binding StopProcessCommand}"
                           Width="120" Height="30" Margin="0,10,0,0"/>
                </StackPanel>
            </GroupBox>

            <!-- 系统信息 -->
            <GroupBox Grid.Column="1" Header="系统信息" Margin="10,0,0,0">
                <StackPanel Margin="10">
                    <TextBlock Text="系统状态:" FontWeight="Bold" Margin="0,0,0,10"/>
                    <TextBlock Text="{Binding SystemStatus}" Margin="0,0,0,5"/>
                    <TextBlock Text="{Binding LastUpdateTime}" Margin="0,0,0,10"/>

                    <TextBlock Text="配置信息:" FontWeight="Bold" Margin="0,10,0,10"/>
                    <TextBlock Text="{Binding ConfigurationInfo}" TextWrapping="Wrap"/>
                </StackPanel>
            </GroupBox>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="#FFF0F0F0" Height="30">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                <TextBlock Text="就绪" Margin="0,0,20,0"/>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
