<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Language" xml:space="preserve">
    <value>言語</value>
  </data>
  <data name="Main_Conn_disconnected" xml:space="preserve">
    <value>接続切断</value>
  </data>
  <data name="Main_Conn_successful" xml:space="preserve">
    <value>接続成功</value>
  </data>
  <data name="Main_Auto" xml:space="preserve">
    <value>自動</value>
  </data>
  <data name="Main_Manual" xml:space="preserve">
    <value>手動</value>
  </data>
  <data name="Main_Init" xml:space="preserve">
    <value>初期化</value>
  </data>
  <data name="Main_Start" xml:space="preserve">
    <value>起動</value>
  </data>
  <data name="Main_Stop" xml:space="preserve">
    <value>停止</value>
  </data>
  <data name="Main_Emergency_stop" xml:space="preserve">
    <value>きんきゅうてい</value>
  </data>
  <data name="Main_Reset" xml:space="preserve">
    <value>リセット</value>
  </data>
  <data name="Main_Enable" xml:space="preserve">
    <value>有効化</value>
  </data>
  <data name="Main_Axis_err_reset" xml:space="preserve">
    <value>軸エラーリセット</value>
  </data>
  <data name="Main_Sys_restart" xml:space="preserve">
    <value>システム再起動</value>
  </data>
  <data name="Main_Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="Main_Station_init" xml:space="preserve">
    <value>ワークステーション初期化</value>
  </data>
  <data name="Main_Station_enable" xml:space="preserve">
    <value>ワークステーション有効化</value>
  </data>
  <data name="Main_Station_mask" xml:space="preserve">
    <value>ワークステーションマスク</value>
  </data>
  <data name="Main_Fault" xml:space="preserve">
    <value>故障</value>
  </data>
  <data name="Main_Maint" xml:space="preserve">
    <value>保守</value>
  </data>
  <data name="Main_Running" xml:space="preserve">
    <value>稼動中</value>
  </data>
  <data name="Main_Equip_conn" xml:space="preserve">
    <value>機器接続</value>
  </data>
  <data name="Main_Driver" xml:space="preserve">
    <value>ドライバ</value>
  </data>
  <data name="Main_Ctrl" xml:space="preserve">
    <value>コントローラ</value>
  </data>
  <data name="Main_Plaintext_msg" xml:space="preserve">
    <value>平文メッセージ</value>
  </data>
  <data name="Main_Fw_upgrade" xml:space="preserve">
    <value>ファームウェアアップグレード</value>
  </data>
  <data name="Main_Offline_conf" xml:space="preserve">
    <value>オフライン設定</value>
  </data>
  <data name="Main_Sys_assembly" xml:space="preserve">
    <value>システムアセンブリ</value>
  </data>
  <data name="Main_Axis_ctrl" xml:space="preserve">
    <value>軸制御</value>
  </data>
  <data name="Main_Conn_stat" xml:space="preserve">
    <value>接続状態</value>
  </data>
  <data name="Main_Station_ctrl" xml:space="preserve">
    <value>ワークステーション制御</value>
  </data>
  <data name="Main_Sys_ctrl" xml:space="preserve">
    <value>システム制御</value>
  </data>
  <data name="Main_Feedback_info" xml:space="preserve">
    <value>フィードバック情報</value>
  </data>
  <data name="Main_Err_fault" xml:space="preserve">
    <value>エラーと故障</value>
  </data>
  <data name="Main_Online_conf" xml:space="preserve">
    <value>オンライン設定</value>
  </data>
  <data name="Main_Dev_comp" xml:space="preserve">
    <value>偏差補正</value>
  </data>
  <data name="Main_Curve_recip" xml:space="preserve">
    <value>曲線強度往復</value>
  </data>
  <data name="Main_Conf_gen" xml:space="preserve">
    <value>設定生成</value>
  </data>
  <data name="Main_Digital_io" xml:space="preserve">
    <value>デジタル IO</value>
  </data>
  <data name="Main_Servo_conf" xml:space="preserve">
    <value>サーボ設定</value>
  </data>
  <data name="Main_Oscillo" xml:space="preserve">
    <value>オシロスコープ</value>
  </data>
  <data name="Main_Basic_sett" xml:space="preserve">
    <value>基本設定</value>
  </data>
  <data name="Main_Role_mgmt" xml:space="preserve">
    <value>役割管理</value>
  </data>
  <data name="Main_User_mgmt" xml:space="preserve">
    <value>ユーザ管理</value>
  </data>
  <data name="Main_Func_list" xml:space="preserve">
    <value>機能リスト</value>
  </data>
  <data name="Main_Perm_assign" xml:space="preserve">
    <value>権限割り当て</value>
  </data>
  <data name="Main_Data_trace" xml:space="preserve">
    <value>データ追跡</value>
  </data>
  <data name="Main_Op_log" xml:space="preserve">
    <value>操作ログ</value>
  </data>
  <data name="Main_Sel_axis_sn" xml:space="preserve">
    <value>軸番号を選択:</value>
  </data>
  <data name="Main_Driver_conn" xml:space="preserve">
    <value>ドライバ接続:</value>
  </data>
  <data name="Main_Ctrl_conn" xml:space="preserve">
    <value>コントローラ接続:</value>
  </data>
  <data name="ControlerAxis_Mover_axis_ctrl" xml:space="preserve">
    <value>ムーバ軸制御</value>
  </data>
  <data name="ControlerAxis_Axis_mot_mode" xml:space="preserve">
    <value>軸運動モード：</value>
  </data>
  <data name="ControlerAxis_Jog_mot" xml:space="preserve">
    <value>ジョグ運動</value>
  </data>
  <data name="ControlerAxis_Abs_mot" xml:space="preserve">
    <value>絶対運動</value>
  </data>
  <data name="ControlerAxis_Rel_mot" xml:space="preserve">
    <value>相対運動</value>
  </data>
  <data name="ControlerAxis_Station_mot" xml:space="preserve">
    <value>ワークステーション運動</value>
  </data>
  <data name="ControlerAxis_Axis_id" xml:space="preserve">
    <value>軸 ID:</value>
  </data>
  <data name="ControlerAxis_Axis_type" xml:space="preserve">
    <value>軸の種類：</value>
  </data>
  <data name="ControlerAxis_Mover" xml:space="preserve">
    <value>ムーバ</value>
  </data>
  <data name="ControlerAxis_Rotary_motor" xml:space="preserve">
    <value>回転モータ</value>
  </data>
  <data name="ControlerAxis_Linear_motor" xml:space="preserve">
    <value>直線モータ</value>
  </data>
  <data name="ControlerAxis_Speed_mode" xml:space="preserve">
    <value>速度モード：</value>
  </data>
  <data name="ControlerAxis_Axis_ctrl_mode" xml:space="preserve">
    <value>軸制御モード：</value>
  </data>
  <data name="ControlerAxis_Target_line_id" xml:space="preserve">
    <value>ターゲットライン ID:</value>
  </data>
  <data name="ControlerAxis_Target_station_id" xml:space="preserve">
    <value>ターゲットワークステーション ID:</value>
  </data>
  <data name="ControlerAxis_Speed" xml:space="preserve">
    <value>速度：</value>
  </data>
  <data name="ControlerAxis_Accel" xml:space="preserve">
    <value>加速度：</value>
  </data>
  <data name="ControlerAxis_Decel" xml:space="preserve">
    <value>減速度：</value>
  </data>
  <data name="ControlerAxis_Jerk" xml:space="preserve">
    <value>急加速：</value>
  </data>
  <data name="ControlerAxis_Pos_accu" xml:space="preserve">
    <value>位置決め精度：</value>
  </data>
  <data name="ControlerAxis_Anti_coll_accu" xml:space="preserve">
    <value>衝突防止精度：</value>
  </data>
  <data name="ControlerAxis_Target_pos" xml:space="preserve">
    <value>目標位置：</value>
  </data>
  <data name="ControlerAxis_Sel_op" xml:space="preserve">
    <value>操作を選択：</value>
  </data>
  <data name="ControlerAxis_Exec" xml:space="preserve">
    <value>実行</value>
  </data>
  <data name="ControlerAxis_Read" xml:space="preserve">
    <value>読み取り</value>
  </data>
  <data name="ControlerAxis_Stop" xml:space="preserve">
    <value>停止</value>
  </data>
  <data name="ControlerAxis_Axis_obj" xml:space="preserve">
    <value>軸が属するオブジェクト</value>
  </data>
  <data name="ControlerAxis_Axis_line" xml:space="preserve">
    <value>軸が属するライン</value>
  </data>
  <data name="ControlerAxis_Driver_err" xml:space="preserve">
    <value>ドライバエラー</value>
  </data>
  <data name="ControlerAxis_Axis_err" xml:space="preserve">
    <value>軸エラー</value>
  </data>
  <data name="ControlerAxis_Axis_curr_pos_mm" xml:space="preserve">
    <value>軸の現在位置（mm）</value>
  </data>
  <data name="ControlerAxis_Axis_curr_speed" xml:space="preserve">
    <value>軸の現在速度</value>
  </data>
  <data name="ControlerAxis_Axis_curr_stat" xml:space="preserve">
    <value>軸の現在状態</value>
  </data>
  <data name="ControlerClient_Ctrl_conn" xml:space="preserve">
    <value>コントローラ接続</value>
  </data>
  <data name="ControlerClient_Port" xml:space="preserve">
    <value>ポート</value>
  </data>
  <data name="ControlerClient_Connect" xml:space="preserve">
    <value>接続</value>
  </data>
  <data name="ControlerClient_Disconnect" xml:space="preserve">
    <value>切断</value>
  </data>
  <data name="ControlerClient_Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="ControlerDebug_Send" xml:space="preserve">
    <value>送信：</value>
  </data>
  <data name="ControlerDebug_Log" xml:space="preserve">
    <value>ログ：</value>
  </data>
  <data name="ControlerDebug_Clear" xml:space="preserve">
    <value>クリア</value>
  </data>
  <data name="ControlerGenerateConfig_Conf_gen" xml:space="preserve">
    <value>設定生成</value>
  </data>
  <data name="ControlerGenerateConfig_Sys_conf_num" xml:space="preserve">
    <value>システム設定数：</value>
  </data>
  <data name="ControlerGenerateConfig_Motor_conf_num" xml:space="preserve">
    <value>モータ設定数：</value>
  </data>
  <data name="ControlerGenerateConfig_Slave_node_conf_num" xml:space="preserve">
    <value>サブノード設定数：</value>
  </data>
  <data name="ControlerGenerateConfig_Line_seg_conf_num" xml:space="preserve">
    <value>ラインセグメント設定数：</value>
  </data>
  <data name="ControlerGenerateConfig_Station_conf_num" xml:space="preserve">
    <value>ワークステーション設定数：</value>
  </data>
  <data name="ControlerGenerateConfig_Mover_conf_num" xml:space="preserve">
    <value>ムーバ設定数：</value>
  </data>
  <data name="ControlerGenerateConfig_Rot_axis_conf_num" xml:space="preserve">
    <value>回転軸設定数：</value>
  </data>
  <data name="ControlerGenerateConfig_Io_conf_num" xml:space="preserve">
    <value>IO 設定数：</value>
  </data>
  <data name="ControlerGenerateConfig_Gen_conf_file" xml:space="preserve">
    <value>設定ファイル生成</value>
  </data>
  <data name="ControlerOnlineConfig_Online_conf" xml:space="preserve">
    <value>オンライン設定</value>
  </data>
  <data name="ControlerOnlineConfig_Sel_conf" xml:space="preserve">
    <value>設定を選択:</value>
  </data>
  <data name="ControlerOnlineConfig_Sys_conf" xml:space="preserve">
    <value>システム設定</value>
  </data>
  <data name="ControlerOnlineConfig_Station_conf" xml:space="preserve">
    <value>ワークステーション設定</value>
  </data>
  <data name="ControlerOnlineConfig_Write" xml:space="preserve">
    <value>書き込み</value>
  </data>
  <data name="ControlerOnlineConfig_Param_name" xml:space="preserve">
    <value>パラメータ名</value>
  </data>
  <data name="ControlerOnlineConfig_Set_type" xml:space="preserve">
    <value>設定タイプ</value>
  </data>
  <data name="ControlerOnlineConfig_Read_val" xml:space="preserve">
    <value>読み取り値</value>
  </data>
  <data name="ControlerOnlineConfig_Set_val" xml:space="preserve">
    <value>設定値</value>
  </data>
  <data name="ControlerOnlineConfig_Desc" xml:space="preserve">
    <value>説明</value>
  </data>
  <data name="ControlerSys_Sys_ctrl" xml:space="preserve">
    <value>システム制御</value>
  </data>
  <data name="ControlerSys_Ctrl_obj" xml:space="preserve">
    <value>制御対象：</value>
  </data>
  <data name="ControlerSys_Mover" xml:space="preserve">
    <value>ムーバ</value>
  </data>
  <data name="ControlerSys_Rotary_motor" xml:space="preserve">
    <value>回転モータ</value>
  </data>
  <data name="ControlerSys_Linear_motor" xml:space="preserve">
    <value>直線モータ</value>
  </data>
  <data name="ControlerSys_Sys_op_mode" xml:space="preserve">
    <value>システム動作モード：</value>
  </data>
  <data name="ControlerSys_Axis_teach" xml:space="preserve">
    <value>軸ティーチング</value>
  </data>
  <data name="ControlerSys_Conn_teach" xml:space="preserve">
    <value>接続ティーチング</value>
  </data>
  <data name="ControlerSys_Auto_op" xml:space="preserve">
    <value>自動動作</value>
  </data>
  <data name="ControlerSys_Auto_op_mode" xml:space="preserve">
    <value>自動動作モード：</value>
  </data>
  <data name="ControlerSys_Sync" xml:space="preserve">
    <value>同期</value>
  </data>
  <data name="ControlerSys_Async" xml:space="preserve">
    <value>非同期</value>
  </data>
  <data name="ControlerSys_Speed_perc" xml:space="preserve">
    <value>速度パーセント：</value>
  </data>
  <data name="ControlerSys_Slave_node_id" xml:space="preserve">
    <value>サブノード ID：</value>
  </data>
  <data name="ControlerSys_Ctrl_mode" xml:space="preserve">
    <value>制御モード：</value>
  </data>
  <data name="ControlerSys_Sel_op" xml:space="preserve">
    <value>操作を選択：</value>
  </data>
  <data name="ControlerSys_Exec" xml:space="preserve">
    <value>実行</value>
  </data>
  <data name="ControlerSys_Read" xml:space="preserve">
    <value>読み取り</value>
  </data>
  <data name="ControlerSys_Sys_err_axis_id" xml:space="preserve">
    <value>システムエラー軸 ID</value>
  </data>
  <data name="ControlerSys_Sys_err_driver" xml:space="preserve">
    <value>システムエラードライバ</value>
  </data>
  <data name="ControlerSys_Sys_err_code" xml:space="preserve">
    <value>システムエラーコード</value>
  </data>
  <data name="ControlerSys_Sys_err_num" xml:space="preserve">
    <value>システムエラー番号</value>
  </data>
  <data name="ControlerSys_Sys_stat" xml:space="preserve">
    <value>システム状態</value>
  </data>
  <data name="ControlerTranStatus_Conn_ctrl" xml:space="preserve">
    <value>接続制御</value>
  </data>
  <data name="ControlerTranStatus_Conn_conf" xml:space="preserve">
    <value>接続設定：</value>
  </data>
  <data name="ControlerTranStatus_Curr_obj_id" xml:space="preserve">
    <value>現在のオブジェクト ID：</value>
  </data>
  <data name="ControlerTranStatus_Left_obj_id" xml:space="preserve">
    <value>左側のオブジェクト ID：</value>
  </data>
  <data name="ControlerTranStatus_Conn_stat" xml:space="preserve">
    <value>接続状態：</value>
  </data>
  <data name="ControlerTranStatus_Disconnect" xml:space="preserve">
    <value>切断</value>
  </data>
  <data name="ControlerTranStatus_Est_conn" xml:space="preserve">
    <value>接続を確立</value>
  </data>
  <data name="ControlerTranStatus_Right_obj_id" xml:space="preserve">
    <value>右側のオブジェクト ID：</value>
  </data>
  <data name="ControlerTranStatus_Sel_op" xml:space="preserve">
    <value>操作を選択：</value>
  </data>
  <data name="ControlerTranStatus_Exec" xml:space="preserve">
    <value>実行</value>
  </data>
  <data name="ControlerTranStatus_Read" xml:space="preserve">
    <value>読み取り</value>
  </data>
  <data name="ControlerTranStatus_Conn_id" xml:space="preserve">
    <value>接続 ID:</value>
  </data>
  <data name="ControlerTranStatus_Target_station_id" xml:space="preserve">
    <value>ターゲットワークステーション ID:</value>
  </data>
  <data name="ControlerTranStatus_Speed" xml:space="preserve">
    <value>速度：</value>
  </data>
  <data name="ControlerTranStatus_Accel" xml:space="preserve">
    <value>加速度：</value>
  </data>
  <data name="ControlerTranStatus_Decel" xml:space="preserve">
    <value>減速度：</value>
  </data>
  <data name="ControlerTranStatus_Target_pos" xml:space="preserve">
    <value>目標位置：</value>
  </data>
  <data name="ControlerTranStatus_Ctrl_cmd" xml:space="preserve">
    <value>制御コマンド：</value>
  </data>
  <data name="ControlerTranStatus_Line_id" xml:space="preserve">
    <value>ライン ID</value>
  </data>
  <data name="ControlerTranStatus_Line_left_conn_obj_id" xml:space="preserve">
    <value>ラインの左側接続オブジェクト ID</value>
  </data>
  <data name="ControlerTranStatus_Line_right_conn_obj_id" xml:space="preserve">
    <value>ラインの右側接続オブジェクト ID</value>
  </data>
  <data name="ControlerTranStatus_Enable_stat" xml:space="preserve">
    <value>有効化状態</value>
  </data>
  <data name="ControlerTranStatus_Run_stat" xml:space="preserve">
    <value>稼動状態</value>
  </data>
  <data name="ControlerTranStatus_Homing_done" xml:space="preserve">
    <value>原点復帰完了</value>
  </data>
  <data name="ControlerTranStatus_Err_code" xml:space="preserve">
    <value>エラーコード</value>
  </data>
  <data name="ControlerTranStatus_Act_speed" xml:space="preserve">
    <value>実際の速度</value>
  </data>
  <data name="ControlerTranStatus_Act_pos" xml:space="preserve">
    <value>実際の位置</value>
  </data>
  <data name="Login_User_name" xml:space="preserve">
    <value>ユーザ名</value>
  </data>
  <data name="Login_Passwd" xml:space="preserve">
    <value>パスワード</value>
  </data>
  <data name="Login_Rem_passwd" xml:space="preserve">
    <value>パスワードを記憶</value>
  </data>
  <data name="Login_Login" xml:space="preserve">
    <value>ログイン</value>
  </data>
  <data name="OperateLog_Enter_keywords" xml:space="preserve">
    <value>キーワードを入力してください</value>
  </data>
  <data name="OperateLog_Refresh" xml:space="preserve">
    <value>更新</value>
  </data>
  <data name="OperateLog_Start_time" xml:space="preserve">
    <value>開始時間:</value>
  </data>
  <data name="OperateLog_Time" xml:space="preserve">
    <value>時間</value>
  </data>
  <data name="OperateLog_Module" xml:space="preserve">
    <value>モジュール</value>
  </data>
  <data name="OperateLog_Op" xml:space="preserve">
    <value>操作</value>
  </data>
  <data name="OperateLog_Behav" xml:space="preserve">
    <value>行動</value>
  </data>
  <data name="OperateLog_Desc" xml:space="preserve">
    <value>説明</value>
  </data>
  <data name="OperateLog_Operator" xml:space="preserve">
    <value>操作者</value>
  </data>
  <data name="OperateLog_View" xml:space="preserve">
    <value>表示</value>
  </data>
  <data name="OperateLog_Details" xml:space="preserve">
    <value>詳細</value>
  </data>
  <data name="OperateLog_Detailed_desc" xml:space="preserve">
    <value>詳細説明:</value>
  </data>
  <data name="OperateLog_Cancel" xml:space="preserve">
    <value>キャンセル</value>
  </data>
  <data name="Scope_Stop" xml:space="preserve">
    <value>停止</value>
  </data>
  <data name="Scope_Collect" xml:space="preserve">
    <value>収集</value>
  </data>
  <data name="Scope_Reset" xml:space="preserve">
    <value>リセット</value>
  </data>
  <data name="Scope_Cross_star" xml:space="preserve">
    <value>十字線</value>
  </data>
  <data name="Scope_X_axis_scale" xml:space="preserve">
    <value>X 軸目盛り</value>
  </data>
  <data name="Scope_Y_axis_scale" xml:space="preserve">
    <value>Y 軸目盛り</value>
  </data>
  <data name="Scope_Import" xml:space="preserve">
    <value>インポート</value>
  </data>
  <data name="Scope_Export" xml:space="preserve">
    <value>エクスポート</value>
  </data>
  <data name="Scope_Check_err" xml:space="preserve">
    <value>エラーチェック</value>
  </data>
  <data name="Scope_Zoom" xml:space="preserve">
    <value>ズーム</value>
  </data>
  <data name="Scope_Sample_freq_1_300_ms" xml:space="preserve">
    <value>サンプリング周波数 (1 - 300、単位 ms)：</value>
  </data>
  <data name="Scope_Channel" xml:space="preserve">
    <value>チャンネル</value>
  </data>
  <data name="Scope_Sel_obj" xml:space="preserve">
    <value>オブジェクトを選択</value>
  </data>
  <data name="Scope_Please_select" xml:space="preserve">
    <value>選択してください</value>
  </data>
  <data name="Scope_Value" xml:space="preserve">
    <value>数値</value>
  </data>
  <data name="Scope_Is_visible" xml:space="preserve">
    <value>表示するか</value>
  </data>
  <data name="Scope_Offset" xml:space="preserve">
    <value>オフセット</value>
  </data>
  <data name="Scope_Magni" xml:space="preserve">
    <value>倍率</value>
  </data>
  <data name="Scope_Color" xml:space="preserve">
    <value>色</value>
  </data>
  <data name="Scope_Debug" xml:space="preserve">
    <value>デバッグ</value>
  </data>
  <data name="ServoSerialPort_Driver_conn" xml:space="preserve">
    <value>ドライバ接続</value>
  </data>
  <data name="ServoSerialPort_Serial_port" xml:space="preserve">
    <value>シリアルポート</value>
  </data>
  <data name="ServoSerialPort_Baud_rate" xml:space="preserve">
    <value>ボーレート</value>
  </data>
  <data name="ServoSerialPort_Data_bits" xml:space="preserve">
    <value>データビット</value>
  </data>
  <data name="ServoSerialPort_Parity_bit" xml:space="preserve">
    <value>パリティビット</value>
  </data>
  <data name="ServoSerialPort_Stop_bits" xml:space="preserve">
    <value>ストップビット</value>
  </data>
  <data name="ServoSerialPort_Connect" xml:space="preserve">
    <value>接続</value>
  </data>
  <data name="ServoSerialPort_Disconnect" xml:space="preserve">
    <value>切断</value>
  </data>
  <data name="ServoSetting_Driver_params" xml:space="preserve">
    <value>ドライバパラメータ</value>
  </data>
  <data name="ServoSetting_Sel_op" xml:space="preserve">
    <value>操作を選択:</value>
  </data>
  <data name="ServoSetting_Sel_write" xml:space="preserve">
    <value>書き込みを選択</value>
  </data>
  <data name="ServoSetting_Write_all" xml:space="preserve">
    <value>すべて書き込み</value>
  </data>
  <data name="ServoSetting_Restore_def_params" xml:space="preserve">
    <value>デフォルトパラメータに戻す</value>
  </data>
  <data name="ServoSetting_Err_reset" xml:space="preserve">
    <value>エラーリセット</value>
  </data>
  <data name="ServoSetting_Fault_rec_clear" xml:space="preserve">
    <value>故障記録を消去</value>
  </data>
  <data name="ServoSetting_Drive_mode_set" xml:space="preserve">
    <value>ドライブモード設定:</value>
  </data>
  <data name="ServoSetting_Ctrl_right" xml:space="preserve">
    <value>制御権:</value>
  </data>
  <data name="ServoSetting_Local_ctrl_mode" xml:space="preserve">
    <value>ローカル制御モード:</value>
  </data>
  <data name="ServoSetting_Sub_mode" xml:space="preserve">
    <value>サブモード:</value>
  </data>
  <data name="ServoSetting_Select" xml:space="preserve">
    <value>選択</value>
  </data>
  <data name="ServoSetting_Param_name" xml:space="preserve">
    <value>パラメータ名</value>
  </data>
  <data name="ServoSetting_Set_type" xml:space="preserve">
    <value>設定タイプ</value>
  </data>
  <data name="ServoSetting_Min_val" xml:space="preserve">
    <value>最小値</value>
  </data>
  <data name="ServoSetting_Max_val" xml:space="preserve">
    <value>最大値</value>
  </data>
  <data name="ServoSetting_Read_val" xml:space="preserve">
    <value>読み取り値</value>
  </data>
  <data name="ServoSetting_Set_val" xml:space="preserve">
    <value>設定値</value>
  </data>
  <data name="ServoSetting_Perm" xml:space="preserve">
    <value>権限</value>
  </data>
  <data name="ServoSetting_Coeff" xml:space="preserve">
    <value>係数</value>
  </data>
  <data name="ServoSetting_Monitor" xml:space="preserve">
    <value>モニタリング</value>
  </data>
  <data name="ServoSetting_Desc" xml:space="preserve">
    <value>説明</value>
  </data>
  <data name="BasePermAssign_Role" xml:space="preserve">
    <value>役割:</value>
  </data>
  <data name="BasePermAssign_Refresh" xml:space="preserve">
    <value>更新</value>
  </data>
  <data name="BasePermAssign_Perm" xml:space="preserve">
    <value>権限:</value>
  </data>
  <data name="BasePermAssign_Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="BasePermission_Enter_keywords" xml:space="preserve">
    <value>キーワードを入力してください</value>
  </data>
  <data name="BasePermission_New" xml:space="preserve">
    <value>新規</value>
  </data>
  <data name="BasePermission_Refresh" xml:space="preserve">
    <value>更新</value>
  </data>
  <data name="BasePermission_Menu" xml:space="preserve">
    <value>メニュー</value>
  </data>
  <data name="BasePermission_Bind_code" xml:space="preserve">
    <value>バインドコード</value>
  </data>
  <data name="BasePermission_Is_button" xml:space="preserve">
    <value>ボタンか</value>
  </data>
  <data name="BasePermission_Is_hidden" xml:space="preserve">
    <value>非表示か</value>
  </data>
  <data name="BasePermission_Btn_event" xml:space="preserve">
    <value>ボタンイベント</value>
  </data>
  <data name="BasePermission_Desc" xml:space="preserve">
    <value>説明</value>
  </data>
  <data name="BasePermission_Level" xml:space="preserve">
    <value>レベル</value>
  </data>
  <data name="BasePermission_Enable" xml:space="preserve">
    <value>有効化</value>
  </data>
  <data name="BasePermission_Creator" xml:space="preserve">
    <value>作成者</value>
  </data>
  <data name="BasePermission_Create_time" xml:space="preserve">
    <value>作成日時</value>
  </data>
  <data name="BasePermission_Modifier" xml:space="preserve">
    <value>修正者</value>
  </data>
  <data name="BasePermission_Mod_time" xml:space="preserve">
    <value>修正日時</value>
  </data>
  <data name="BasePermission_Op" xml:space="preserve">
    <value>操作</value>
  </data>
  <data name="BasePermission_Edit" xml:space="preserve">
    <value>編集</value>
  </data>
  <data name="BasePermission_Delete" xml:space="preserve">
    <value>削除</value>
  </data>
  <data name="BasePermission_Menu_name" xml:space="preserve">
    <value>メニュー名:</value>
  </data>
  <data name="BasePermission_Parent_menu" xml:space="preserve">
    <value>親メニュー:</value>
  </data>
  <data name="BasePermission_Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="BasePermission_Cancel" xml:space="preserve">
    <value>キャンセル</value>
  </data>
  <data name="BaseRole_Enter_keywords" xml:space="preserve">
    <value>キーワードを入力してください</value>
  </data>
  <data name="BaseRole_New" xml:space="preserve">
    <value>新規</value>
  </data>
  <data name="BaseRole_Refresh" xml:space="preserve">
    <value>更新</value>
  </data>
  <data name="BaseRole_Role_name" xml:space="preserve">
    <value>役割名</value>
  </data>
  <data name="BaseRole_Desc" xml:space="preserve">
    <value>説明</value>
  </data>
  <data name="BaseRole_Level" xml:space="preserve">
    <value>レベル</value>
  </data>
  <data name="BaseRole_Creator" xml:space="preserve">
    <value>作成者</value>
  </data>
  <data name="BaseRole_Create_time" xml:space="preserve">
    <value>作成日時</value>
  </data>
  <data name="BaseRole_Modifier" xml:space="preserve">
    <value>修正者</value>
  </data>
  <data name="BaseRole_Mod_time" xml:space="preserve">
    <value>修正日時</value>
  </data>
  <data name="BaseRole_Is_enabled" xml:space="preserve">
    <value>有効にするか</value>
  </data>
  <data name="BaseRole_Op" xml:space="preserve">
    <value>操作</value>
  </data>
  <data name="BaseRole_Edit" xml:space="preserve">
    <value>編集</value>
  </data>
  <data name="BaseRole_Delete" xml:space="preserve">
    <value>削除</value>
  </data>
  <data name="BaseRole_Pri_smaller_perm_bigger" xml:space="preserve">
    <value>優先度が小さいほど、権限が大きい</value>
  </data>
  <data name="BaseRole_Enable_curr_role" xml:space="preserve">
    <value>現在の役割を有効にするか:</value>
  </data>
  <data name="BaseRole_Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="BaseRole_Cancel" xml:space="preserve">
    <value>キャンセル</value>
  </data>
  <data name="BaseUser_Enter_keywords" xml:space="preserve">
    <value>キーワードを入力してください</value>
  </data>
  <data name="BaseUser_New" xml:space="preserve">
    <value>新規</value>
  </data>
  <data name="BaseUser_Refresh" xml:space="preserve">
    <value>更新</value>
  </data>
  <data name="BaseUser_User_name" xml:space="preserve">
    <value>ユーザ名</value>
  </data>
  <data name="BaseUser_Real_name" xml:space="preserve">
    <value>本名</value>
  </data>
  <data name="BaseUser_Role" xml:space="preserve">
    <value>役割</value>
  </data>
  <data name="BaseUser_Status" xml:space="preserve">
    <value>状態</value>
  </data>
  <data name="BaseUser_Remark" xml:space="preserve">
    <value>備考</value>
  </data>
  <data name="BaseUser_Create_time" xml:space="preserve">
    <value>作成日時</value>
  </data>
  <data name="BaseUser_Mod_time" xml:space="preserve">
    <value>修正日時</value>
  </data>
  <data name="BaseUser_Last_login" xml:space="preserve">
    <value>最終ログイン</value>
  </data>
  <data name="BaseUser_Op" xml:space="preserve">
    <value>操作</value>
  </data>
  <data name="BaseUser_Edit" xml:space="preserve">
    <value>編集</value>
  </data>
  <data name="BaseUser_Delete" xml:space="preserve">
    <value>削除</value>
  </data>
  <data name="BaseUser_Login_name" xml:space="preserve">
    <value>ログイン名:</value>
  </data>
  <data name="BaseUser_Passwd" xml:space="preserve">
    <value>パスワード:</value>
  </data>
  <data name="BaseUser_Change_passwd" xml:space="preserve">
    <value>パスワードを変更</value>
  </data>
  <data name="BaseUser_Pending_enable" xml:space="preserve">
    <value>有効化待ち</value>
  </data>
  <data name="BaseUser_Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="BaseUser_Cancel" xml:space="preserve">
    <value>キャンセル</value>
  </data>
  <data name="PromptUserControl_No_menu_perm" xml:space="preserve">
    <value>このメニューの権限がありません</value>
  </data>
  <data name="App_xaml_Ui_thread" xml:space="preserve">
    <value>UI スレッド：</value>
  </data>
  <data name="App_xaml_Ui_thread_exception" xml:space="preserve">
    <value>UI スレッドの異常：</value>
  </data>
  <data name="App_xaml_Ui_thread_fatal_error" xml:space="preserve">
    <value>UI スレッドで致命的なエラーが発生しました！</value>
  </data>
  <data name="App_xaml_Non_ui_thread_fatal_error" xml:space="preserve">
    <value>非 UI スレッドで致命的なエラーが発生しました</value>
  </data>
  <data name="App_xaml_Non_ui_thread_exception" xml:space="preserve">
    <value>非 UI スレッドの異常：</value>
  </data>
  <data name="App_xaml_Task_thread" xml:space="preserve">
    <value>Task スレッド：</value>
  </data>
  <data name="App_xaml_Task_thread_exception" xml:space="preserve">
    <value>Task スレッドの異常：</value>
  </data>
  <data name="DesignerHelper_Main_thread" xml:space="preserve">
    <value>メインスレッド</value>
  </data>
  <data name="ImageAttached_Switch" xml:space="preserve">
    <value>スイッチ</value>
  </data>
  <data name="PermissionHelper_No_permission_operation" xml:space="preserve">
    <value>あなたはこの操作の権限がありません</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_enable_status" xml:space="preserve">
    <value>単軸のエネーブル状態</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_running_status" xml:space="preserve">
    <value>単軸の動作状態</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_alarm_status" xml:space="preserve">
    <value>単軸のアラーム状態</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_error_status" xml:space="preserve">
    <value>単軸のエラー状態</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_left_collision" xml:space="preserve">
    <value>単軸の左衝突</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_right_collision" xml:space="preserve">
    <value>単軸の右衝突</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_positive_limit" xml:space="preserve">
    <value>単軸の正限位置</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_negative_limit" xml:space="preserve">
    <value>単軸の負限位置</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_on_workstation" xml:space="preserve">
    <value>単軸がワークステーション上にあります</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_reached_target" xml:space="preserve">
    <value>単軸が目標位置に到達しました</value>
  </data>
  <data name="SysFeedBackMapping_System_ready" xml:space="preserve">
    <value>システムは準備ができています</value>
  </data>
  <data name="SysFeedBackMapping_System_enable_status" xml:space="preserve">
    <value>システムのエネーブル状態</value>
  </data>
  <data name="SysFeedBackMapping_System_error_status" xml:space="preserve">
    <value>システムのエラー状態</value>
  </data>
  <data name="SysFeedBackMapping_System_running_status" xml:space="preserve">
    <value>システムの動作状態</value>
  </data>
  <data name="SysFeedBackMapping_System_bus_status" xml:space="preserve">
    <value>システムのバス状態</value>
  </data>
  <data name="SysFeedBackMapping_System_platform_verification" xml:space="preserve">
    <value>システムのプラットフォームチェック状態</value>
  </data>
  <data name="SysFeedBackMapping_Axis_config_completed" xml:space="preserve">
    <value>軸の構成が完了し、軸シーケンスの初期化を行うことができます</value>
  </data>
  <data name="SysFeedBackMapping_Motion_param_config_completed" xml:space="preserve">
    <value>動作パラメータの構成が完了し、システムの旧状態の復旧を行うことができます</value>
  </data>
  <data name="SysFeedBackMapping_System_state_restored" xml:space="preserve">
    <value>システムの旧状態の復旧が完了しました</value>
  </data>
  <data name="SysFeedBackMapping_Bit8_31_reserved" xml:space="preserve">
    <value>bit8-31: 予約\n</value>
  </data>
  <data name="SqlsugarSetup_Sql_statement" xml:space="preserve">
    <value>【SQL 文】：</value>
  </data>
  <data name="SqlsugarSetup_Sql_parameters" xml:space="preserve">
    <value>【SQL パラメータ】：</value>
  </data>
  <data name="InputConverter_Input_value_range" xml:space="preserve">
    <value>入力値は指定範囲内になければなりません</value>
  </data>
  <data name="BasePermAssignViewModel_Root_node" xml:space="preserve">
    <value>ルートノード</value>
  </data>
  <data name="BasePermAssignViewModel_Get_success" xml:space="preserve">
    <value>取得成功</value>
  </data>
  <data name="BasePermissionViewModel_Root_node" xml:space="preserve">
    <value>ルートノード</value>
  </data>
  <data name="BasePermissionViewModel_Get_success" xml:space="preserve">
    <value>取得成功</value>
  </data>
  <data name="BasePermissionViewModel_Add_success" xml:space="preserve">
    <value>追加成功</value>
  </data>
  <data name="BasePermissionViewModel_Update_success" xml:space="preserve">
    <value>更新成功</value>
  </data>
  <data name="BasePermissionViewModel_Delete_success" xml:space="preserve">
    <value>削除成功</value>
  </data>
  <data name="BaseUserViewModel_Get_success" xml:space="preserve">
    <value>取得成功</value>
  </data>
  <data name="BaseUserViewModel_Add_success" xml:space="preserve">
    <value>追加成功</value>
  </data>
  <data name="BaseUserViewModel_Update_success" xml:space="preserve">
    <value>更新成功</value>
  </data>
  <data name="BaseUserViewModel_Delete_success" xml:space="preserve">
    <value>削除成功</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_forward" xml:space="preserve">
    <value>Jog 正方向運動</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_reverse" xml:space="preserve">
    <value>Jog 逆方向運動</value>
  </data>
  <data name="ControlerAxisViewModel_Absolute_movement" xml:space="preserve">
    <value>絶対運動</value>
  </data>
  <data name="ControlerAxisViewModel_Relative_movement" xml:space="preserve">
    <value>相対運動</value>
  </data>
  <data name="ControlerAxisViewModel_Workstation_movement" xml:space="preserve">
    <value>ワークステーション運動</value>
  </data>
  <data name="ControlerAxisViewModel_Set_zero_point" xml:space="preserve">
    <value>ゼロ設定</value>
  </data>
  <data name="ControlerAxisViewModel_Axis_reset" xml:space="preserve">
    <value>軸リセット</value>
  </data>
  <data name="ControlerGenerateConfigViewModel_Config_file_generated" xml:space="preserve">
    <value>構成ファイルの生成が成功しました</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Workstation_config_distributed" xml:space="preserve">
    <value>ワークステーションの構成の送信が成功しました</value>
  </data>
  <data name="ControlerTranStatusViewModel_Do_nothing" xml:space="preserve">
    <value>何も処理しない</value>
  </data>
  <data name="DataViewModel_Controller_disconnected" xml:space="preserve">
    <value>コントローラの接続が切断されました！</value>
  </data>
  <data name="DataViewModel_Controller_connected" xml:space="preserve">
    <value>コントローラの接続が成功しました！</value>
  </data>
  <data name="MainViewModel_Controller_feedback_zero" xml:space="preserve">
    <value>コントローラからの軸数のフィードバックが 0 で、この操作を行うことができません！</value>
  </data>
  <data name="ServoSettingViewModel_No_control" xml:space="preserve">
    <value>制御なし</value>
  </data>
  <data name="ServoSettingViewModel_Dual_axis_position_control" xml:space="preserve">
    <value>両軸位置制御</value>
  </data>
  <data name="ServoSettingViewModel_Axis0_electrical_angle" xml:space="preserve">
    <value>軸 0 の電気角の識別</value>
  </data>
  <data name="ServoSettingViewModel_Dc_sampling_test" xml:space="preserve">
    <value>直流サンプリングテスト</value>
  </data>
  <data name="ServoSettingViewModel_Ac_sampling_test" xml:space="preserve">
    <value>交流サンプリングテスト</value>
  </data>
  <data name="ScopeView_xaml_Csv_file_filter" xml:space="preserve">
    <value>CSV ファイル (*.csv)|*.csv|すべてのファイル (*.*)|*.*</value>
  </data>
  <data name="ScopeView_xaml_Select_csv_file" xml:space="preserve">
    <value>CSV ファイルを 1 つ選択してください</value>
  </data>
  <data name="ScopeView_xaml_Select_save_path" xml:space="preserve">
    <value>保存先を選択してください</value>
  </data>
  <data name="ScopeView_xaml_Data_export_success" xml:space="preserve">
    <value>データのエクスポートが成功しました</value>
  </data>
  <data name="ObjectUtil_Object_not_empty" xml:space="preserve">
    <value>渡されたオブジェクトは空でなければなりません！</value>
  </data>
  <data name="FileHelper_Newly_appended_content" xml:space="preserve">
    <value>新規追加内容</value>
  </data>
  <data name="FileHelper_What_i_wrote" xml:space="preserve">
    <value>これは私が書いた内容ですよ</value>
  </data>
  <data name="FileHelper_Directory_not_exist" xml:space="preserve">
    <value>対応するディレクトリが存在しません</value>
  </data>
  <data name="RecursionHelper_Button" xml:space="preserve">
    <value>ボタン</value>
  </data>
  <data name="ControlerTcpClient_Send_data" xml:space="preserve">
    <value>データの送信：</value>
  </data>
  <data name="ControlerTcpClient_Adapter_parsing_failed" xml:space="preserve">
    <value>アダプターがデータの解析に失敗しました！</value>
  </data>
  <data name="ControlerTcpClient_Controller_not_connected" xml:space="preserve">
    <value>コントローラが接続されていません！</value>
  </data>
  <data name="ControlerTcpClient_Controller_heartbeat_failed" xml:space="preserve">
    <value>コントローラのハートビートの送信に失敗しました</value>
  </data>
  <data name="ControllerConst_Upper_enable" xml:space="preserve">
    <value>上のエネーブル</value>
  </data>
  <data name="ControllerConst_Lower_enable" xml:space="preserve">
    <value>下のエネーブル</value>
  </data>
  <data name="ControllerConst_Stop" xml:space="preserve">
    <value>停止</value>
  </data>
  <data name="ControllerConst_Reset" xml:space="preserve">
    <value>リセット</value>
  </data>
  <data name="ControllerConst_Set_zero_point" xml:space="preserve">
    <value>ゼロ設定</value>
  </data>
  <data name="ControllerConst_Forward_jog" xml:space="preserve">
    <value>前方のジョグ</value>
  </data>
  <data name="ControllerConst_Backward_jog" xml:space="preserve">
    <value>後方のジョグ</value>
  </data>
  <data name="ControllerConst_Absolute_movement" xml:space="preserve">
    <value>絶対運動</value>
  </data>
  <data name="ControllerConst_Relative_movement" xml:space="preserve">
    <value>相対運動</value>
  </data>
  <data name="ControllerConst_Workstation_movement" xml:space="preserve">
    <value>ワークステーション運動</value>
  </data>
  <data name="SysCtrlCmdEnum_Upper_enable" xml:space="preserve">
    <value>上のエネーブル</value>
  </data>
  <data name="SysCtrlCmdEnum_Lower_enable" xml:space="preserve">
    <value>下のエネーブル</value>
  </data>
  <data name="SysCtrlCmdEnum_Error_reset" xml:space="preserve">
    <value>エラーのリセット</value>
  </data>
  <data name="SysCtrlCmdEnum_Run" xml:space="preserve">
    <value>実行</value>
  </data>
  <data name="SysCtrlCmdEnum_Pause" xml:space="preserve">
    <value>一時停止</value>
  </data>
  <data name="SysCtrlCmdEnum_Emergency_stop" xml:space="preserve">
    <value>緊急停止</value>
  </data>
  <data name="AxisCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>制御オブジェクトは既にプロトコルから削除されており、このプロパティを使用しないでください</value>
  </data>
  <data name="SysCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>制御オブジェクトは既にプロトコルから削除されており、このプロパティを使用しないでください</value>
  </data>
  <data name="ScopeConst_Position_parameter" xml:space="preserve">
    <value>位置パラメータ</value>
  </data>
  <data name="ScopeConst_Axis0_position_feedback" xml:space="preserve">
    <value>軸 0 の位置フィードバック</value>
  </data>
  <data name="ScopeConst_Axis1_position_feedback" xml:space="preserve">
    <value>軸 1 の位置フィードバック</value>
  </data>
  <data name="ScopeConst_Speed_parameter" xml:space="preserve">
    <value>速度パラメータ</value>
  </data>
  <data name="ScopeConst_Axis0_speed_instruction" xml:space="preserve">
    <value>軸 0 の速度指令</value>
  </data>
  <data name="ScopeConst_Axis0_speed_feedback" xml:space="preserve">
    <value>軸 0 の速度フィードバック</value>
  </data>
  <data name="ScopeConst_Axis1_speed_instruction" xml:space="preserve">
    <value>軸 1 の速度指令</value>
  </data>
  <data name="ScopeConst_Axis1_speed_feedback" xml:space="preserve">
    <value>軸 1 の速度フィードバック</value>
  </data>
  <data name="ScopeConst_Current_parameter" xml:space="preserve">
    <value>電流パラメータ</value>
  </data>
  <data name="ScopeConst_Axis0_current_instruction" xml:space="preserve">
    <value>軸 0 の電流指令</value>
  </data>
  <data name="ScopeConst_Axis0_current_feedback" xml:space="preserve">
    <value>軸 0 の電流フィードバック</value>
  </data>
  <data name="ScopeConst_Axis1_current_instruction" xml:space="preserve">
    <value>軸 1 の電流指令</value>
  </data>
  <data name="ScopeConst_Axis1_current_feedback" xml:space="preserve">
    <value>軸 1 の電流フィードバック</value>
  </data>
  <data name="ScopeConst_Voltage_parameter" xml:space="preserve">
    <value>電圧パラメータ</value>
  </data>
  <data name="ScopeConst_Axis0_d_axis_voltage" xml:space="preserve">
    <value>軸 0 D 軸の基準電圧</value>
  </data>
  <data name="ScopeConst_Axis1_d_axis_voltage" xml:space="preserve">
    <value>軸 1 D 軸の基準電圧</value>
  </data>
  <data name="ScopeConst_Axis0_q_axis_voltage" xml:space="preserve">
    <value>軸 0 Q 軸の基準電圧</value>
  </data>
  <data name="ScopeConst_Axis1_q_axis_voltage" xml:space="preserve">
    <value>軸 1 Q 軸の基準電圧</value>
  </data>
  <data name="ScopeConst_Axis0_bus_voltage" xml:space="preserve">
    <value>軸 0 の母線電圧</value>
  </data>
  <data name="ScopeConst_Axis1_bus_voltage" xml:space="preserve">
    <value>軸 1 の母線電圧</value>
  </data>
  <data name="ScopeConst_Axis0_u_phase_current" xml:space="preserve">
    <value>軸 0 U 相電流</value>
  </data>
  <data name="ScopeConst_Axis1_u_phase_current" xml:space="preserve">
    <value>軸 1 U 相電流</value>
  </data>
  <data name="ScopeConst_Axis0_v_phase_current" xml:space="preserve">
    <value>軸 0 V 相電流</value>
  </data>
  <data name="ScopeConst_Axis1_v_phase_current" xml:space="preserve">
    <value>軸 1 V 相電流</value>
  </data>
  <data name="ScopeConst_Axis0_w_phase_current" xml:space="preserve">
    <value>軸 0 W 相電流</value>
  </data>
  <data name="ScopeConst_Axis1_w_phase_current" xml:space="preserve">
    <value>軸 1 W 相電流</value>
  </data>
  <data name="ScopeConst_Axis0_control_voltage" xml:space="preserve">
    <value>軸 0 の制御電圧</value>
  </data>
  <data name="ScopeConst_Axis1_control_voltage" xml:space="preserve">
    <value>軸 1 の制御電圧</value>
  </data>
  <data name="ServoContext_Motor_parameter" xml:space="preserve">
    <value>1-モーターパラメータ</value>
  </data>
  <data name="ServoContext_System_parameter" xml:space="preserve">
    <value>2-システムパラメータ</value>
  </data>
  <data name="ServoContext_Encoder_parameter" xml:space="preserve">
    <value>3-エンコーダーパラメータ</value>
  </data>
  <data name="ServoContext_Protection_parameter" xml:space="preserve">
    <value>4-保護パラメータ</value>
  </data>
  <data name="ServoContext_Fault_record" xml:space="preserve">
    <value>5-故障記録</value>
  </data>
  <data name="ServoContext_Control_status" xml:space="preserve">
    <value>6-制御状態</value>
  </data>
  <data name="ServoContext_Position_parameter" xml:space="preserve">
    <value>7-位置パラメータ</value>
  </data>
  <data name="ServoContext_Speed_parameter" xml:space="preserve">
    <value>8-速度パラメータ</value>
  </data>
  <data name="ServoContext_Torque_parameter" xml:space="preserve">
    <value>9-トルクパラメータ</value>
  </data>
  <data name="ServoContext_Get_from_drive_context_exception" xml:space="preserve">
    <value>GetFromDriveContext の異常</value>
  </data>
  <data name="ServoSerialPortClient_Servo_heartbeat_failed" xml:space="preserve">
    <value>サーボハートビートの送信に失敗しました</value>
  </data>
  <data name="ElectricParaPackage_Third_instruction_not_exist" xml:space="preserve">
    <value>第 3 の指令は存在しません</value>
  </data>
  <data name="RoleDto_Role_name_not_empty" xml:space="preserve">
    <value>ロール名は空でなければなりません</value>
  </data>
  <data name="ParameterModel_Input_value_exceed_limit" xml:space="preserve">
    <value>入力値が制限を超えています！</value>
  </data>
  <data name="ParameterModel_Input_value_incorrect" xml:space="preserve">
    <value>入力値が間違っています！</value>
  </data>
  <data name="LineConfigEnum_System" xml:space="preserve">
    <value>システム</value>
  </data>
  <data name="LineConfigEnum_Motor" xml:space="preserve">
    <value>モータ</value>
  </data>
  <data name="LineConfigEnum_Slave_node" xml:space="preserve">
    <value>サブステーションノード</value>
  </data>
  <data name="LineConfigEnum_Line" xml:space="preserve">
    <value>ライン</value>
  </data>
  <data name="LineConfigEnum_Workstation" xml:space="preserve">
    <value>ワークステーション</value>
  </data>
  <data name="LineConfigEnum_Axis" xml:space="preserve">
    <value>軸</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence" xml:space="preserve">
    <value>軸シーケンス</value>
  </data>
  <data name="LineConfigEnum_Axis_pid" xml:space="preserve">
    <value>軸 PID</value>
  </data>
  <data name="LineConfigEnum_Axis_offset" xml:space="preserve">
    <value>軸オフセット</value>
  </data>
  <data name="LineConfigEnum_Device_wiring_direction" xml:space="preserve">
    <value>デバイスの接続方向</value>
  </data>
  <data name="LineConfigEnum_Workstation_offset" xml:space="preserve">
    <value>ワークステーションオフセット</value>
  </data>
  <data name="LineConfigEnum_Ui_view" xml:space="preserve">
    <value>UI ビュー</value>
  </data>
  <data name="LineConfigEnum_Configuration_parameter" xml:space="preserve">
    <value>構成パラメータ</value>
  </data>
  <data name="LineConfigEnum_System_configuration_parameter" xml:space="preserve">
    <value>システムの構成パラメータ</value>
  </data>
  <data name="LineConfigEnum_Motor_configuration_parameter" xml:space="preserve">
    <value>モータの構成パラメータ</value>
  </data>
  <data name="LineConfigEnum_Slave_node_configuration_parameter" xml:space="preserve">
    <value>サブステーションノードの構成パラメータ</value>
  </data>
  <data name="LineConfigEnum_Line_segment_configuration_parameter" xml:space="preserve">
    <value>ラインセグメントの構成パラメータ</value>
  </data>
  <data name="LineConfigEnum_Workstation_running_configuration_parameter" xml:space="preserve">
    <value>ワークステーションの動作構成パラメータ</value>
  </data>
  <data name="LineConfigEnum_Rotor_configuration_parameter" xml:space="preserve">
    <value>モーターの構成パラメータ</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence_configuration_parameter" xml:space="preserve">
    <value>軸シーケンスの構成パラメータ</value>
  </data>
  <data name="LineConfigEnum_Axis_running_pid_configuration_parameter" xml:space="preserve">
    <value>軸の動作 PID の構成パラメータ</value>
  </data>
  <data name="LineConfigEnum_Rotor_compensation_configuration_parameter" xml:space="preserve">
    <value>モーター補償の構成パラメータ</value>
  </data>
  <data name="LineConfigEnum_Line_wiring_direction_configuration_parameter" xml:space="preserve">
    <value>ラインの接続方向の構成パラメータ</value>
  </data>
  <data name="LineConfigEnum_Workstation_compensation_configuration_parameter" xml:space="preserve">
    <value>ワークステーションの補償の構成パラメータ</value>
  </data>
  <data name="LineConfigEnum_Line_view_configuration_parameter" xml:space="preserve">
    <value>ラインビューの構成パラメータ</value>
  </data>
  <data name="ParamTableEnum_Motor_parameter" xml:space="preserve">
    <value>1-モーターパラメータ</value>
  </data>
  <data name="ParamTableEnum_System_parameter" xml:space="preserve">
    <value>2-システムパラメータ</value>
  </data>
  <data name="ParamTableEnum_Encoder_parameter" xml:space="preserve">
    <value>3-エンコーダーパラメータ</value>
  </data>
  <data name="ParamTableEnum_Protection_parameter" xml:space="preserve">
    <value>4-保護パラメータ</value>
  </data>
  <data name="ParamTableEnum_Fault_record" xml:space="preserve">
    <value>5-故障記録</value>
  </data>
  <data name="ParamTableEnum_Control_status" xml:space="preserve">
    <value>6-制御状態</value>
  </data>
  <data name="ParamTableEnum_Position_parameter" xml:space="preserve">
    <value>7-位置パラメータ</value>
  </data>
  <data name="ParamTableEnum_Speed_parameter" xml:space="preserve">
    <value>8-速度パラメータ</value>
  </data>
  <data name="ParamTableEnum_Torque_parameter" xml:space="preserve">
    <value>9-トルクパラメータ</value>
  </data>
  <data name="ParameterModelExtension_Parameter_model_extension_exception" xml:space="preserve">
    <value>ParameterModelExtension の異常</value>
  </data>
  <data name="LocalizationManager_Simplified_chinese" xml:space="preserve">
    <value>简体字</value>
  </data>
  <data name="LocalizationManager_Traditional_chinese" xml:space="preserve">
    <value>繁体字</value>
  </data>
  <data name="LocalizationManager_Japanese" xml:space="preserve">
    <value>日本語</value>
  </data>
  <data name="OnlineConfigService_Unknown" xml:space="preserve">
    <value>未知</value>
  </data>
  <data name="OnlineConfigService_No_description" xml:space="preserve">
    <value>説明なし</value>
  </data>
  <data name="OnlineConfigService_No_value" xml:space="preserve">
    <value>値なし</value>
  </data>
  <data name="SerialCore_Remote_terminal_closed" xml:space="preserve">
    <value>リモートターミナルが閉じられました</value>
  </data>
  <data name="SerialCore_New_serial_port_connected" xml:space="preserve">
    <value>新しい SerialPort は接続状態でなければなりません。</value>
  </data>
  <data name="SerialPortClient_Data_processing_error" xml:space="preserve">
    <value>データを処理している間にエラーが発生しました</value>
  </data>
  <data name="SerialPortClient_Config_file_not_empty" xml:space="preserve">
    <value>構成ファイルは空でなければなりません。</value>
  </data>
  <data name="SerialPortClient_Serial_port_config_not_empty" xml:space="preserve">
    <value>シリアルポートの構成は空でなければなりません。</value>
  </data>
  <data name="SerialPortClient_Adapter_not_support_send" xml:space="preserve">
    <value>現在のアダプターはオブジェクトの送信をサポートしていません。</value>
  </data>
  <data name="ControlerOnlineConfig_View_configuration" xml:space="preserve">
    <value>ビュー設定</value>
  </data>
  <data name="ControlerOnlineConfig_Motor_configuration" xml:space="preserve">
    <value>モーター設定</value>
  </data>
  <data name="ControlerOnlineConfig_Slave_node" xml:space="preserve">
    <value>スレーブノード</value>
  </data>
  <data name="ControlerOnlineConfig_Line_body_configuration" xml:space="preserve">
    <value>ライン設定</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_operation_configuration" xml:space="preserve">
    <value>ワークステーション運行設定</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_configuration" xml:space="preserve">
    <value>軸設定</value>
  </data>
  <data name="ControlerOnlineConfig_Sequence_configuration" xml:space="preserve">
    <value>シーケンス設定</value>
  </data>
  <data name="ControlerOnlineConfig_Pid_configuration" xml:space="preserve">
    <value>PID設定</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_compensation_configuration" xml:space="preserve">
    <value>軸補正設定</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_compensation_configuration" xml:space="preserve">
    <value>ワークステーション補正設定</value>
  </data>
  <data name="ControlerOnlineConfig_Upload_to_controller_with_one_click" xml:space="preserve">
    <value>ワンクリックでコントローラにアップロード</value>
  </data>
  <data name="ControlerOnlineConfig_Download_to_local_with_one_click" xml:space="preserve">
    <value>ワンクリックでローカルにダウンロード</value>
  </data>
  <data name="ControlerOnlineConfig_Load_configuration" xml:space="preserve">
    <value>設定を読み込む</value>
  </data>
  <data name="ControlerOnlineConfig_Save_configuration_as" xml:space="preserve">
    <value>設定を別名で保存</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Send_success" xml:space="preserve">
    <value>送信成功</value>
  </data>
  <data name="RoleDto_Role_name_cannot_be_empty" xml:space="preserve">
    <value>ロール名は空にできません</value>
  </data>
  <data name="Main_Online_demonstration" xml:space="preserve">
    <value>オンラインデモ</value>
  </data>
  <data name="Main_Alarm" xml:space="preserve">
    <value>アラーム</value>
  </data>
  <data name="NoticeListControl_Feedback_information" xml:space="preserve">
    <value>フィードバック情報</value>
  </data>
  <data name="NoticeListControl_Clear_all_notifications" xml:space="preserve">
    <value>すべての通知をクリア</value>
  </data>
  <data name="NoticeListControl_Type" xml:space="preserve">
    <value>タイプ</value>
  </data>
  <data name="NoticeListControl_Source" xml:space="preserve">
    <value>ソース</value>
  </data>
  <data name="NoticeListControl_Message_content" xml:space="preserve">
    <value>メッセージ内容</value>
  </data>
  <data name="ControlerClient_Global_data_reset" xml:space="preserve">
    <value>グローバルデータのリセット</value>
  </data>
  <data name="ControlerClient_Platform_verification" xml:space="preserve">
    <value>プラットフォーム検証</value>
  </data>
  <data name="ControlerClient_System_parameter_configuration_initialization" xml:space="preserve">
    <value>システムパラメータ設定の初期化</value>
  </data>
  <data name="ControlerClient_Slave_station_information_acquisition" xml:space="preserve">
    <value>スレーブステーション情報の取得</value>
  </data>
  <data name="ControlerClient_Mapping_of_slave_station_address_to_control_address" xml:space="preserve">
    <value>スレーブステーションアドレスを制御アドレスにマッピング</value>
  </data>
  <data name="ControlerClient_Master_slave_station_status_verification" xml:space="preserve">
    <value>マスター-スレーブステーションの状態検証</value>
  </data>
  <data name="ControlerClient_Completion_of_status_initialization_of_bus_system_etc" xml:space="preserve">
    <value>バス-システムなどの状態初期化完了</value>
  </data>
  <data name="ControlerClient_Initialization_of_movement_related_parameters" xml:space="preserve">
    <value>運動関連パラメータの初期化</value>
  </data>
  <data name="ControlerClient_Successful_initialization_of_magnetic_drive" xml:space="preserve">
    <value>磁気駆動の初期化成功</value>
  </data>
  <data name="ControlerSys_System_drive_error" xml:space="preserve">
    <value>システムドライブエラー</value>
  </data>
  <data name="FtpClient_Host" xml:space="preserve">
    <value>ホスト</value>
  </data>
  <data name="FtpClient_Port" xml:space="preserve">
    <value>ポート</value>
  </data>
  <data name="FtpClient_Username" xml:space="preserve">
    <value>ユーザ名</value>
  </data>
  <data name="FtpClient_Password" xml:space="preserve">
    <value>パスワード</value>
  </data>
  <data name="FtpClient_Connect" xml:space="preserve">
    <value>接続</value>
  </data>
  <data name="FtpClient_Disconnect" xml:space="preserve">
    <value>切断</value>
  </data>
  <data name="FtpClient_Remote_directory" xml:space="preserve">
    <value>リモートディレクトリ</value>
  </data>
  <data name="FtpClient_Back" xml:space="preserve">
    <value>戻る</value>
  </data>
  <data name="FtpClient_Forward" xml:space="preserve">
    <value>進む</value>
  </data>
  <data name="FtpClient_Up" xml:space="preserve">
    <value>上</value>
  </data>
  <data name="FtpClient_Refresh" xml:space="preserve">
    <value>更新</value>
  </data>
  <data name="FtpClient_Create_folder" xml:space="preserve">
    <value>フォルダを作成</value>
  </data>
  <data name="FtpClient_Delete" xml:space="preserve">
    <value>削除</value>
  </data>
  <data name="FtpClient_Download_to_local" xml:space="preserve">
    <value>ローカルにダウンロード</value>
  </data>
  <data name="FtpClient_Local_directory" xml:space="preserve">
    <value>ローカルディレクトリ</value>
  </data>
  <data name="FtpClient_Upload_to_server" xml:space="preserve">
    <value>サーバにアップロード</value>
  </data>
  <data name="FtpClient_Transmission_log" xml:space="preserve">
    <value>伝送ログ</value>
  </data>
  <data name="ServoSetting_System_soft_reset" xml:space="preserve">
    <value>システムソフトリセット</value>
  </data>
  <data name="FtpClientViewModel_Connecting_to_ftp_server" xml:space="preserve">
    <value>FTPサーバに接続中</value>
  </data>
  <data name="FtpClientViewModel_Connected_to_ftp_server" xml:space="preserve">
    <value>FTPサーバに接続済み</value>
  </data>
  <data name="FtpClientViewModel_Connect" xml:space="preserve">
    <value>接続</value>
  </data>
  <data name="FtpClientViewModel_Disconnected" xml:space="preserve">
    <value>接続を切断</value>
  </data>
  <data name="FtpClientViewModel_Disconnect" xml:space="preserve">
    <value>接続を切断</value>
  </data>
  <data name="FtpClientViewModel_Loading_remote_directory" xml:space="preserve">
    <value>リモートディレクトリを読み込み中</value>
  </data>
  <data name="FtpClientViewModel_Remote_directory_loaded" xml:space="preserve">
    <value>リモートディレクトリが読み込まれました</value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_remote_directory" xml:space="preserve">
    <value>リモートディレクトリの読み込みに失敗しました</value>
  </data>
  <data name="FtpClientViewModel_Browse" xml:space="preserve">
    <value>参照</value>
  </data>
  <data name="FtpClientViewModel_Loading_local_directory" xml:space="preserve">
    <value>ローカルディレクトリを読み込み中</value>
  </data>
  <data name="FtpClientViewModel_Local_directory_loaded" xml:space="preserve">
    <value>ローカルディレクトリが読み込まれました</value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_local_directory" xml:space="preserve">
    <value>ローカルディレクトリの読み込みに失敗しました</value>
  </data>
  <data name="FtpClientViewModel_Downloading" xml:space="preserve">
    <value>ダウンロード中</value>
  </data>
  <data name="FtpClientViewModel_Download_completed" xml:space="preserve">
    <value>ダウンロード完了</value>
  </data>
  <data name="FtpClientViewModel_Download" xml:space="preserve">
    <value>ダウンロード</value>
  </data>
  <data name="FtpClientViewModel_Download_failed" xml:space="preserve">
    <value>ダウンロード失敗</value>
  </data>
  <data name="FtpClientViewModel_Uploading" xml:space="preserve">
    <value>アップロード中</value>
  </data>
  <data name="FtpClientViewModel_Upload_completed" xml:space="preserve">
    <value>アップロード完了</value>
  </data>
  <data name="FtpClientViewModel_Upload" xml:space="preserve">
    <value>アップロード</value>
  </data>
  <data name="FtpClientViewModel_Upload_failed" xml:space="preserve">
    <value>アップロード失敗</value>
  </data>
  <data name="FtpClientViewModel_Directory_created" xml:space="preserve">
    <value>ディレクトリが作成されました</value>
  </data>
  <data name="FtpClientViewModel_Create_directory" xml:space="preserve">
    <value>ディレクトリを作成</value>
  </data>
  <data name="FtpClientViewModel_Failed_to_create_directory" xml:space="preserve">
    <value>ディレクトリの作成に失敗しました</value>
  </data>
  <data name="FtpClientViewModel_Directory_deleted" xml:space="preserve">
    <value>ディレクトリが削除されました</value>
  </data>
  <data name="FtpClientViewModel_Delete" xml:space="preserve">
    <value>削除</value>
  </data>
  <data name="FtpClientViewModel_File_deleted" xml:space="preserve">
    <value>ファイルが削除されました</value>
  </data>
  <data name="FtpClientViewModel_Open" xml:space="preserve">
    <value>開く</value>
  </data>
  <data name="ControllerHelper_System_is_running" xml:space="preserve">
    <value>システムが実行中</value>
  </data>
  <data name="ControllerHelper_System_is_ready" xml:space="preserve">
    <value>システムが準備完了</value>
  </data>
  <data name="ControllerHelper_System_is_enabled" xml:space="preserve">
    <value>システムが有効化されました</value>
  </data>
  <data name="ControllerHelper_System_bus_is_connected" xml:space="preserve">
    <value>システムバスが接続されました</value>
  </data>
  <data name="ControllerHelper_System_is_in_error_state" xml:space="preserve">
    <value>システムがエラー状態にあります</value>
  </data>
  <data name="ControllerHelper_Axis_driver_error" xml:space="preserve">
    <value>軸ドライブエラー</value>
  </data>
  <data name="ControllerHelper_Axis_movement_error" xml:space="preserve">
    <value>軸運動エラー</value>
  </data>
  <data name="ControllerHelper_Axis_error_status" xml:space="preserve">
    <value>軸エラー状態</value>
  </data>
  <data name="ControllerHelper_Axis_alarm" xml:space="preserve">
    <value>軸アラーム</value>
  </data>
  <data name="ControllerHelper_Positive_limit_of_axis" xml:space="preserve">
    <value>軸の正のリミット</value>
  </data>
  <data name="ControllerHelper_Negative_limit_of_axis" xml:space="preserve">
    <value>軸の負のリミット</value>
  </data>
  <data name="ControllerHelper_Axis_warning" xml:space="preserve">
    <value>軸警告</value>
  </data>
  <data name="ControllerHelper_Axis_in_left_position" xml:space="preserve">
    <value>軸が左に到達</value>
  </data>
  <data name="ControllerHelper_Axis_in_right_position" xml:space="preserve">
    <value>軸が右に到達</value>
  </data>
  <data name="ControllerHelper_Axis_has_reached_the_target_position" xml:space="preserve">
    <value>軸が目標位置に到達</value>
  </data>
  <data name="ControllerHelper_Axis_is_at_the_workstation" xml:space="preserve">
    <value>軸がワークステーションにあります</value>
  </data>
  <data name="ControllerHelper_Axis_notification" xml:space="preserve">
    <value>軸通知</value>
  </data>
  <data name="ControllerHelper_Axis_is_running" xml:space="preserve">
    <value>軸が実行中</value>
  </data>
  <data name="ControllerHelper_Axis_is_enabled" xml:space="preserve">
    <value>軸が有効化されました</value>
  </data>
  <data name="ControllerHelper_Axis_status" xml:space="preserve">
    <value>軸状態</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_running" xml:space="preserve">
    <value>回転軸が実行中</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_homing_completed" xml:space="preserve">
    <value>回転軸のホーミング完了</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_enabled" xml:space="preserve">
    <value>回転軸が有効化されました</value>
  </data>
  <data name="ControllerHelper_Rotary_axis" xml:space="preserve">
    <value>回転軸</value>
  </data>
  <data name="ServoSerialPortClient_Driver_connected_successfully" xml:space="preserve">
    <value>ドライブ接続成功</value>
  </data>
  <data name="ServoSerialPortClient_Driver_disconnected" xml:space="preserve">
    <value>ドライブ接続解除</value>
  </data>
  <data name="ServoSerialPortClient_Driver_parameter_recovery_successful" xml:space="preserve">
    <value>ドライブパラメータ回復成功</value>
  </data>
  <data name="Scope_Error_log" xml:space="preserve">
    <value>エラー記録</value>
  </data>
  <data name="Scope_Process_log" xml:space="preserve">
    <value>プロセス記録</value>
  </data>
  <data name="Scope_Plc_signal" xml:space="preserve">
    <value>PLC信号</value>
  </data>
  <data name="Scope_Plc_control_signal_count" xml:space="preserve">
    <value>PLC制御信号カウント</value>
  </data>
  <data name="Scope_Acquire_count" xml:space="preserve">
    <value>カウント取得</value>
  </data>
  <data name="Scope_Axis_0" xml:space="preserve">
    <value>軸0：</value>
  </data>
  <data name="Scope_Brake" xml:space="preserve">
    <value>ブレーキ：</value>
  </data>
  <data name="Scope_Acceleration" xml:space="preserve">
    <value>加速：</value>
  </data>
  <data name="Scope_Axis_1" xml:space="preserve">
    <value>軸1：</value>
  </data>
  <data name="ServoSetting_Cdm_driver_parameter_settings" xml:space="preserve">
    <value>CDMドライバーパラメータ設定：</value>
  </data>
  <data name="ServoSetting_Target_stop_position" xml:space="preserve">
    <value>目標停止位置：</value>
  </data>
  <data name="ServoSetting_Send" xml:space="preserve">
    <value>送信</value>
  </data>
  <data name="ServoSetting_Rotor_detection_position" xml:space="preserve">
    <value>可動子検出位置：</value>
  </data>
  <data name="ServoSetting_Enable_motor_position" xml:space="preserve">
    <value>モータ位置イネーブル：</value>
  </data>
  <data name="ServoSetting_Current_user_no_permission_modify_this_parameter" xml:space="preserve">
    <value>現在のユーザーはこのパラメータを変更する権限がありません</value>
  </data>
  <data name="ServoSettingViewModel_Csv_file_csv" xml:space="preserve">
    <value>CSVファイル|*.csv</value>
  </data>
  <data name="ServoSettingViewModel_Select_import_file" xml:space="preserve">
    <value>インポートファイルを選択</value>
  </data>
  <data name="ServoSettingViewModel_Select_export_path" xml:space="preserve">
    <value>エクスポートパスを選択</value>
  </data>
  <data name="ServoSettingViewModel_Driver_parameters_exported_successfully" xml:space="preserve">
    <value>ドライバーパラメータのエクスポートに成功</value>
  </data>
  <data name="ServoSettingViewModel_Invalid_parameters_exist_check_input_values_retry" xml:space="preserve">
    <value>無効なパラメータが存在します、入力値を確認した後再試行してください</value>
  </data>
  <data name="ServoSettingViewModel_Invalid_parameters_exist_check_all_input_values_retry" xml:space="preserve">
    <value>無効なパラメータが存在します、すべての入力値を確認した後再試行してください</value>
  </data>
  <data name="ServoSettingViewModel_All_parameters_set_complete" xml:space="preserve">
    <value>すべてのパラメータ設定完了</value>
  </data>
  <data name="ScopeView_xaml_Serial_port_disconnected_oscilloscope_auto_stopped" xml:space="preserve">
    <value>シリアルポートが切断され、オシロスコープは自動的に停止しました</value>
  </data>
  <data name="JsonHelper_Object_to_serialize_cannot_be_null" xml:space="preserve">
    <value>シリアル化するオブジェクトはnullにできません</value>
  </data>
  <data name="JsonHelper_File_path_cannot_be_empty" xml:space="preserve">
    <value>ファイルパスを空にできません</value>
  </data>
  <data name="JsonHelper_Target_type_cannot_be_null" xml:space="preserve">
    <value>ターゲットタイプはnullにできません</value>
  </data>
  <data name="AxisCtrlCmdPackage_Control_object_removed_from_protocol_do_not_use_this_property" xml:space="preserve">
    <value>制御オブジェクトはプロトコルから削除されました、このプロパティを使用しないでください</value>
  </data>
  <data name="SysCtrlCmdPackage_Control_object_removed_from_protocol_do_not_use_this_property" xml:space="preserve">
    <value>制御オブジェクトはプロトコルから削除されました、このプロパティを使用しないでください</value>
  </data>
  <data name="ServoSerialPortClient_Serial_port_device_not_supported_no_authorization_response" xml:space="preserve">
    <value>シリアルポートデバイスはサポートされていません、認証応答を受信していません</value>
  </data>
  <data name="ServoSerialPortClient_Error_sending_authorization_packet" xml:space="preserve">
    <value>認証パッケージの送信中にエラーが発生</value>
  </data>
  <data name="ServoSerialPortClient_Serial_port_device_not_supported_authorization_failed" xml:space="preserve">
    <value>シリアルポートデバイスはサポートされていません、認証に失敗</value>
  </data>
  <data name="ParameterModel_Value_cannot_be_empty" xml:space="preserve">
    <value>値を空にできません</value>
  </data>
  <data name="ParameterModel_Input_value_cannot_contain_spaces_tabs" xml:space="preserve">
    <value>入力値にスペースまたはタブを含めることはできません</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_number_format" xml:space="preserve">
    <value>有効な数値形式を入力してください</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_16bit_unsigned_integer" xml:space="preserve">
    <value>有効な16ビット符号なし整数(0-65535)を入力してください</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_32bit_unsigned_integer" xml:space="preserve">
    <value>有効な32ビット符号なし整数(0-4294967295)を入力してください</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_16bit_signed_integer" xml:space="preserve">
    <value>有効な16ビット符号付き整数(-32768～32767)を入力してください</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_32bit_signed_integer" xml:space="preserve">
    <value>有効な32ビット符号付き整数(-2147483648～2147483647)を入力してください</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_floating_point_number" xml:space="preserve">
    <value>有効な浮動小数点数を入力してください</value>
  </data>
  <data name="ParameterModel_Input_value_cannot_be_nan_or_infinity" xml:space="preserve">
    <value>入力値はNaNまたは無限大にできません</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_double_precision_floating_point_number" xml:space="preserve">
    <value>有効な倍精度浮動小数点数を入力してください</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_number" xml:space="preserve">
    <value>有効な数値を入力してください</value>
  </data>
  <data name="SerialCore_New_serialport_must_be_in_connected_state" xml:space="preserve">
    <value>新しいSerialPortは接続状態である必要があります。</value>
  </data>
  <data name="SerialPortClient_Error_processing_data" xml:space="preserve">
    <value>データ処理中にエラーが発生</value>
  </data>
  <data name="SerialPortClient_Configuration_file_cannot_be_empty" xml:space="preserve">
    <value>設定ファイルを空にできません。</value>
  </data>
  <data name="SerialPortClient_Serial_port_configuration_cannot_be_empty" xml:space="preserve">
    <value>シリアルポート設定を空にできません。</value>
  </data>
  <data name="SerialPortClient_Current_adapter_not_support_object_sending" xml:space="preserve">
    <value>現在のアダプタはオブジェクト送信をサポートしていません。</value>
  </data>
</root>