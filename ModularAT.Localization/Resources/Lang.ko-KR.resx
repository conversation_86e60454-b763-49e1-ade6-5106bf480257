<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Language" xml:space="preserve">
    <value>언어</value>
  </data>
  <data name="Main_Conn_disconnected" xml:space="preserve">
    <value>연결 끊김</value>
  </data>
  <data name="Main_Conn_successful" xml:space="preserve">
    <value>연결 성공</value>
  </data>
  <data name="Main_Auto" xml:space="preserve">
    <value>자동</value>
  </data>
  <data name="Main_Manual" xml:space="preserve">
    <value>수동</value>
  </data>
  <data name="Main_Init" xml:space="preserve">
    <value>초기화</value>
  </data>
  <data name="Main_Start" xml:space="preserve">
    <value>시작</value>
  </data>
  <data name="Main_Stop" xml:space="preserve">
    <value>중지</value>
  </data>
  <data name="Main_Emergency_stop" xml:space="preserve">
    <value>긴급정</value>
  </data>
  <data name="Main_Reset" xml:space="preserve">
    <value>리셋</value>
  </data>
  <data name="Main_Enable" xml:space="preserve">
    <value>활성화</value>
  </data>
  <data name="Main_Axis_err_reset" xml:space="preserve">
    <value>축 오류 리셋</value>
  </data>
  <data name="Main_Sys_restart" xml:space="preserve">
    <value>시스템 재시작</value>
  </data>
  <data name="Main_Save" xml:space="preserve">
    <value>저장</value>
  </data>
  <data name="Main_Station_init" xml:space="preserve">
    <value>작업장 초기화</value>
  </data>
  <data name="Main_Station_enable" xml:space="preserve">
    <value>작업장 활성화</value>
  </data>
  <data name="Main_Station_mask" xml:space="preserve">
    <value>작업장 마스킹</value>
  </data>
  <data name="Main_Fault" xml:space="preserve">
    <value>고장</value>
  </data>
  <data name="Main_Maint" xml:space="preserve">
    <value>유지보수</value>
  </data>
  <data name="Main_Running" xml:space="preserve">
    <value>운행</value>
  </data>
  <data name="Main_Equip_conn" xml:space="preserve">
    <value>장비 연결</value>
  </data>
  <data name="Main_Driver" xml:space="preserve">
    <value>드라이버</value>
  </data>
  <data name="Main_Ctrl" xml:space="preserve">
    <value>제어기</value>
  </data>
  <data name="Main_Plaintext_msg" xml:space="preserve">
    <value>평문 메시지</value>
  </data>
  <data name="Main_Fw_upgrade" xml:space="preserve">
    <value>펌웨어 업그레이드</value>
  </data>
  <data name="Main_Offline_conf" xml:space="preserve">
    <value>오프라인 설정</value>
  </data>
  <data name="Main_Sys_assembly" xml:space="preserve">
    <value>시스템 조립품</value>
  </data>
  <data name="Main_Axis_ctrl" xml:space="preserve">
    <value>축 제어</value>
  </data>
  <data name="Main_Conn_stat" xml:space="preserve">
    <value>연결 상태</value>
  </data>
  <data name="Main_Station_ctrl" xml:space="preserve">
    <value>작업장 제어</value>
  </data>
  <data name="Main_Sys_ctrl" xml:space="preserve">
    <value>시스템 제어</value>
  </data>
  <data name="Main_Feedback_info" xml:space="preserve">
    <value>피드백 정보</value>
  </data>
  <data name="Main_Err_fault" xml:space="preserve">
    <value>에러 및 고장</value>
  </data>
  <data name="Main_Online_conf" xml:space="preserve">
    <value>온라인 설정</value>
  </data>
  <data name="Main_Dev_comp" xml:space="preserve">
    <value>편차 보정</value>
  </data>
  <data name="Main_Curve_recip" xml:space="preserve">
    <value>곡선 강도 왕복</value>
  </data>
  <data name="Main_Conf_gen" xml:space="preserve">
    <value>설정 생성</value>
  </data>
  <data name="Main_Digital_io" xml:space="preserve">
    <value>디지털 IO</value>
  </data>
  <data name="Main_Servo_conf" xml:space="preserve">
    <value>서보 설정</value>
  </data>
  <data name="Main_Oscillo" xml:space="preserve">
    <value>오실로스코프</value>
  </data>
  <data name="Main_Basic_sett" xml:space="preserve">
    <value>기본 설정</value>
  </data>
  <data name="Main_Role_mgmt" xml:space="preserve">
    <value>역할 관리</value>
  </data>
  <data name="Main_User_mgmt" xml:space="preserve">
    <value>사용자 관리</value>
  </data>
  <data name="Main_Func_list" xml:space="preserve">
    <value>기능 목록</value>
  </data>
  <data name="Main_Perm_assign" xml:space="preserve">
    <value>권한 할당</value>
  </data>
  <data name="Main_Data_trace" xml:space="preserve">
    <value>데이터 추적</value>
  </data>
  <data name="Main_Op_log" xml:space="preserve">
    <value>조작 로그</value>
  </data>
  <data name="Main_Sel_axis_sn" xml:space="preserve">
    <value>축 번호 선택:</value>
  </data>
  <data name="Main_Driver_conn" xml:space="preserve">
    <value>드라이버 연결:</value>
  </data>
  <data name="Main_Ctrl_conn" xml:space="preserve">
    <value>제어기 연결:</value>
  </data>
  <data name="ControlerAxis_Mover_axis_ctrl" xml:space="preserve">
    <value>이동자 축 제어</value>
  </data>
  <data name="ControlerAxis_Axis_mot_mode" xml:space="preserve">
    <value>축 운동 모드:</value>
  </data>
  <data name="ControlerAxis_Jog_mot" xml:space="preserve">
    <value>조그 운동</value>
  </data>
  <data name="ControlerAxis_Abs_mot" xml:space="preserve">
    <value>절대 운동</value>
  </data>
  <data name="ControlerAxis_Rel_mot" xml:space="preserve">
    <value>상대 운동</value>
  </data>
  <data name="ControlerAxis_Station_mot" xml:space="preserve">
    <value>작업장 운동</value>
  </data>
  <data name="ControlerAxis_Axis_id" xml:space="preserve">
    <value>축 ID:</value>
  </data>
  <data name="ControlerAxis_Axis_type" xml:space="preserve">
    <value>축 유형:</value>
  </data>
  <data name="ControlerAxis_Mover" xml:space="preserve">
    <value>이동자</value>
  </data>
  <data name="ControlerAxis_Rotary_motor" xml:space="preserve">
    <value>회전 모터</value>
  </data>
  <data name="ControlerAxis_Linear_motor" xml:space="preserve">
    <value>직선 모터</value>
  </data>
  <data name="ControlerAxis_Speed_mode" xml:space="preserve">
    <value>속도 모드:</value>
  </data>
  <data name="ControlerAxis_Axis_ctrl_mode" xml:space="preserve">
    <value>축 제어 모드:</value>
  </data>
  <data name="ControlerAxis_Target_line_id" xml:space="preserve">
    <value>목표 라인 ID:</value>
  </data>
  <data name="ControlerAxis_Target_station_id" xml:space="preserve">
    <value>목표 작업장 ID:</value>
  </data>
  <data name="ControlerAxis_Speed" xml:space="preserve">
    <value>속도:</value>
  </data>
  <data name="ControlerAxis_Accel" xml:space="preserve">
    <value>가속도:</value>
  </data>
  <data name="ControlerAxis_Decel" xml:space="preserve">
    <value>감속도:</value>
  </data>
  <data name="ControlerAxis_Jerk" xml:space="preserve">
    <value>가가속도:</value>
  </data>
  <data name="ControlerAxis_Pos_accu" xml:space="preserve">
    <value>위치 정확도:</value>
  </data>
  <data name="ControlerAxis_Anti_coll_accu" xml:space="preserve">
    <value>충돌 방지 정확도:</value>
  </data>
  <data name="ControlerAxis_Target_pos" xml:space="preserve">
    <value>목표 위치:</value>
  </data>
  <data name="ControlerAxis_Sel_op" xml:space="preserve">
    <value>조작 선택:</value>
  </data>
  <data name="ControlerAxis_Exec" xml:space="preserve">
    <value>실행</value>
  </data>
  <data name="ControlerAxis_Read" xml:space="preserve">
    <value>읽기</value>
  </data>
  <data name="ControlerAxis_Stop" xml:space="preserve">
    <value>중지</value>
  </data>
  <data name="ControlerAxis_Axis_obj" xml:space="preserve">
    <value>축이 속한 대상</value>
  </data>
  <data name="ControlerAxis_Axis_line" xml:space="preserve">
    <value>축이 속한 라인</value>
  </data>
  <data name="ControlerAxis_Driver_err" xml:space="preserve">
    <value>드라이버 에러</value>
  </data>
  <data name="ControlerAxis_Axis_err" xml:space="preserve">
    <value>축 에러</value>
  </data>
  <data name="ControlerAxis_Axis_curr_pos_mm" xml:space="preserve">
    <value>축 현재 위치(mm)</value>
  </data>
  <data name="ControlerAxis_Axis_curr_speed" xml:space="preserve">
    <value>축 현재 속도</value>
  </data>
  <data name="ControlerAxis_Axis_curr_stat" xml:space="preserve">
    <value>축 현재 상태</value>
  </data>
  <data name="ControlerClient_Ctrl_conn" xml:space="preserve">
    <value>제어기 연결</value>
  </data>
  <data name="ControlerClient_Port" xml:space="preserve">
    <value>포트</value>
  </data>
  <data name="ControlerClient_Connect" xml:space="preserve">
    <value>연결</value>
  </data>
  <data name="ControlerClient_Disconnect" xml:space="preserve">
    <value>연결 해제</value>
  </data>
  <data name="ControlerClient_Save" xml:space="preserve">
    <value>저장</value>
  </data>
  <data name="ControlerDebug_Send" xml:space="preserve">
    <value>전송:</value>
  </data>
  <data name="ControlerDebug_Log" xml:space="preserve">
    <value>로그:</value>
  </data>
  <data name="ControlerDebug_Clear" xml:space="preserve">
    <value>지우기</value>
  </data>
  <data name="ControlerGenerateConfig_Conf_gen" xml:space="preserve">
    <value>설정 생성</value>
  </data>
  <data name="ControlerGenerateConfig_Sys_conf_num" xml:space="preserve">
    <value>시스템 설정 수:</value>
  </data>
  <data name="ControlerGenerateConfig_Motor_conf_num" xml:space="preserve">
    <value>모터 설정 수:</value>
  </data>
  <data name="ControlerGenerateConfig_Slave_node_conf_num" xml:space="preserve">
    <value>슬레이브 노드 설정 수:</value>
  </data>
  <data name="ControlerGenerateConfig_Line_seg_conf_num" xml:space="preserve">
    <value>라인 세그먼트 설정 수:</value>
  </data>
  <data name="ControlerGenerateConfig_Station_conf_num" xml:space="preserve">
    <value>작업장 설정 수:</value>
  </data>
  <data name="ControlerGenerateConfig_Mover_conf_num" xml:space="preserve">
    <value>이동자 설정 수:</value>
  </data>
  <data name="ControlerGenerateConfig_Rot_axis_conf_num" xml:space="preserve">
    <value>회전축 설정 수:</value>
  </data>
  <data name="ControlerGenerateConfig_Io_conf_num" xml:space="preserve">
    <value>IO 설정 수:</value>
  </data>
  <data name="ControlerGenerateConfig_Gen_conf_file" xml:space="preserve">
    <value>설정 파일 생성</value>
  </data>
  <data name="ControlerOnlineConfig_Online_conf" xml:space="preserve">
    <value>온라인 설정</value>
  </data>
  <data name="ControlerOnlineConfig_Sel_conf" xml:space="preserve">
    <value>설정 선택:</value>
  </data>
  <data name="ControlerOnlineConfig_Sys_conf" xml:space="preserve">
    <value>시스템 설정</value>
  </data>
  <data name="ControlerOnlineConfig_Station_conf" xml:space="preserve">
    <value>작업장 설정</value>
  </data>
  <data name="ControlerOnlineConfig_Write" xml:space="preserve">
    <value>쓰기</value>
  </data>
  <data name="ControlerOnlineConfig_Param_name" xml:space="preserve">
    <value>매개변수 이름</value>
  </data>
  <data name="ControlerOnlineConfig_Set_type" xml:space="preserve">
    <value>설정 유형</value>
  </data>
  <data name="ControlerOnlineConfig_Read_val" xml:space="preserve">
    <value>읽은 값</value>
  </data>
  <data name="ControlerOnlineConfig_Set_val" xml:space="preserve">
    <value>설정 값</value>
  </data>
  <data name="ControlerOnlineConfig_Desc" xml:space="preserve">
    <value>설명</value>
  </data>
  <data name="ControlerSys_Sys_ctrl" xml:space="preserve">
    <value>시스템 제어</value>
  </data>
  <data name="ControlerSys_Ctrl_obj" xml:space="preserve">
    <value>제어 대상:</value>
  </data>
  <data name="ControlerSys_Mover" xml:space="preserve">
    <value>이동자</value>
  </data>
  <data name="ControlerSys_Rotary_motor" xml:space="preserve">
    <value>회전 모터</value>
  </data>
  <data name="ControlerSys_Linear_motor" xml:space="preserve">
    <value>직선 모터</value>
  </data>
  <data name="ControlerSys_Sys_op_mode" xml:space="preserve">
    <value>시스템 운영 모드:</value>
  </data>
  <data name="ControlerSys_Axis_teach" xml:space="preserve">
    <value>축 교육</value>
  </data>
  <data name="ControlerSys_Conn_teach" xml:space="preserve">
    <value>연결 교육</value>
  </data>
  <data name="ControlerSys_Auto_op" xml:space="preserve">
    <value>자동 운행</value>
  </data>
  <data name="ControlerSys_Auto_op_mode" xml:space="preserve">
    <value>자동 운행 모드:</value>
  </data>
  <data name="ControlerSys_Sync" xml:space="preserve">
    <value>동기</value>
  </data>
  <data name="ControlerSys_Async" xml:space="preserve">
    <value>비동기</value>
  </data>
  <data name="ControlerSys_Speed_perc" xml:space="preserve">
    <value>속도 백분율:</value>
  </data>
  <data name="ControlerSys_Slave_node_id" xml:space="preserve">
    <value>슬레이브 노드 ID:</value>
  </data>
  <data name="ControlerSys_Ctrl_mode" xml:space="preserve">
    <value>제어 모드:</value>
  </data>
  <data name="ControlerSys_Sel_op" xml:space="preserve">
    <value>조작 선택:</value>
  </data>
  <data name="ControlerSys_Exec" xml:space="preserve">
    <value>실행</value>
  </data>
  <data name="ControlerSys_Read" xml:space="preserve">
    <value>읽기</value>
  </data>
  <data name="ControlerSys_Sys_err_axis_id" xml:space="preserve">
    <value>시스템 에러 축 ID</value>
  </data>
  <data name="ControlerSys_Sys_err_driver" xml:space="preserve">
    <value>시스템 에러 드라이버</value>
  </data>
  <data name="ControlerSys_Sys_err_code" xml:space="preserve">
    <value>시스템 에러 코드</value>
  </data>
  <data name="ControlerSys_Sys_err_num" xml:space="preserve">
    <value>시스템 에러 번호</value>
  </data>
  <data name="ControlerSys_Sys_stat" xml:space="preserve">
    <value>시스템 상태</value>
  </data>
  <data name="ControlerTranStatus_Conn_ctrl" xml:space="preserve">
    <value>연결 제어</value>
  </data>
  <data name="ControlerTranStatus_Conn_conf" xml:space="preserve">
    <value>연결 설정:</value>
  </data>
  <data name="ControlerTranStatus_Curr_obj_id" xml:space="preserve">
    <value>현재 대상 ID:</value>
  </data>
  <data name="ControlerTranStatus_Left_obj_id" xml:space="preserve">
    <value>왼쪽 대상 ID:</value>
  </data>
  <data name="ControlerTranStatus_Conn_stat" xml:space="preserve">
    <value>연결 상태:</value>
  </data>
  <data name="ControlerTranStatus_Disconnect" xml:space="preserve">
    <value>연결 해제</value>
  </data>
  <data name="ControlerTranStatus_Est_conn" xml:space="preserve">
    <value>연결 설정</value>
  </data>
  <data name="ControlerTranStatus_Right_obj_id" xml:space="preserve">
    <value>오른쪽 대상 ID:</value>
  </data>
  <data name="ControlerTranStatus_Sel_op" xml:space="preserve">
    <value>조작 선택:</value>
  </data>
  <data name="ControlerTranStatus_Exec" xml:space="preserve">
    <value>실행</value>
  </data>
  <data name="ControlerTranStatus_Read" xml:space="preserve">
    <value>읽기</value>
  </data>
  <data name="ControlerTranStatus_Conn_id" xml:space="preserve">
    <value>연결 ID:</value>
  </data>
  <data name="ControlerTranStatus_Target_station_id" xml:space="preserve">
    <value>목표 작업장 ID:</value>
  </data>
  <data name="ControlerTranStatus_Speed" xml:space="preserve">
    <value>속도:</value>
  </data>
  <data name="ControlerTranStatus_Accel" xml:space="preserve">
    <value>가속도:</value>
  </data>
  <data name="ControlerTranStatus_Decel" xml:space="preserve">
    <value>감속도:</value>
  </data>
  <data name="ControlerTranStatus_Target_pos" xml:space="preserve">
    <value>목표 위치:</value>
  </data>
  <data name="ControlerTranStatus_Ctrl_cmd" xml:space="preserve">
    <value>제어 명령:</value>
  </data>
  <data name="ControlerTranStatus_Line_id" xml:space="preserve">
    <value>라인 ID</value>
  </data>
  <data name="ControlerTranStatus_Line_left_conn_obj_id" xml:space="preserve">
    <value>라인 왼쪽 연결 대상 ID</value>
  </data>
  <data name="ControlerTranStatus_Line_right_conn_obj_id" xml:space="preserve">
    <value>라인 오른쪽 연결 대상 ID</value>
  </data>
  <data name="ControlerTranStatus_Enable_stat" xml:space="preserve">
    <value>활성화 상태</value>
  </data>
  <data name="ControlerTranStatus_Run_stat" xml:space="preserve">
    <value>운행 상태</value>
  </data>
  <data name="ControlerTranStatus_Homing_done" xml:space="preserve">
    <value>홈 복귀 완료</value>
  </data>
  <data name="ControlerTranStatus_Err_code" xml:space="preserve">
    <value>에러 코드</value>
  </data>
  <data name="ControlerTranStatus_Act_speed" xml:space="preserve">
    <value>실제 속도</value>
  </data>
  <data name="ControlerTranStatus_Act_pos" xml:space="preserve">
    <value>실제 위치</value>
  </data>
  <data name="Login_User_name" xml:space="preserve">
    <value>사용자 이름</value>
  </data>
  <data name="Login_Passwd" xml:space="preserve">
    <value>비밀번호</value>
  </data>
  <data name="Login_Rem_passwd" xml:space="preserve">
    <value>비밀번호 저장</value>
  </data>
  <data name="Login_Login" xml:space="preserve">
    <value>로그인</value>
  </data>
  <data name="OperateLog_Enter_keywords" xml:space="preserve">
    <value>키워드를 입력하세요</value>
  </data>
  <data name="OperateLog_Refresh" xml:space="preserve">
    <value>새로 고침</value>
  </data>
  <data name="OperateLog_Start_time" xml:space="preserve">
    <value>시작 시간: </value>
  </data>
  <data name="OperateLog_Time" xml:space="preserve">
    <value>시간</value>
  </data>
  <data name="OperateLog_Module" xml:space="preserve">
    <value>모듈</value>
  </data>
  <data name="OperateLog_Op" xml:space="preserve">
    <value>조작</value>
  </data>
  <data name="OperateLog_Behav" xml:space="preserve">
    <value>행위</value>
  </data>
  <data name="OperateLog_Desc" xml:space="preserve">
    <value>설명</value>
  </data>
  <data name="OperateLog_Operator" xml:space="preserve">
    <value>조작자</value>
  </data>
  <data name="OperateLog_View" xml:space="preserve">
    <value>보기</value>
  </data>
  <data name="OperateLog_Details" xml:space="preserve">
    <value>상세</value>
  </data>
  <data name="OperateLog_Detailed_desc" xml:space="preserve">
    <value>상세 설명:</value>
  </data>
  <data name="OperateLog_Cancel" xml:space="preserve">
    <value>취소</value>
  </data>
  <data name="Scope_Stop" xml:space="preserve">
    <value>중지</value>
  </data>
  <data name="Scope_Collect" xml:space="preserve">
    <value>수집</value>
  </data>
  <data name="Scope_Reset" xml:space="preserve">
    <value>재설정</value>
  </data>
  <data name="Scope_Cross_star" xml:space="preserve">
    <value>십자기호</value>
  </data>
  <data name="Scope_X_axis_scale" xml:space="preserve">
    <value>X축 눈금</value>
  </data>
  <data name="Scope_Y_axis_scale" xml:space="preserve">
    <value>Y축 눈금</value>
  </data>
  <data name="Scope_Import" xml:space="preserve">
    <value>가져오기</value>
  </data>
  <data name="Scope_Export" xml:space="preserve">
    <value>내보내기</value>
  </data>
  <data name="Scope_Check_err" xml:space="preserve">
    <value>에러 검사</value>
  </data>
  <data name="Scope_Zoom" xml:space="preserve">
    <value>확대/축소</value>
  </data>
  <data name="Scope_Sample_freq_1_300_ms" xml:space="preserve">
    <value>샘플링 주파수(1 - 300, 단위 ms):</value>
  </data>
  <data name="Scope_Channel" xml:space="preserve">
    <value>채널</value>
  </data>
  <data name="Scope_Sel_obj" xml:space="preserve">
    <value>대상 선택</value>
  </data>
  <data name="Scope_Please_select" xml:space="preserve">
    <value>선택해주세요</value>
  </data>
  <data name="Scope_Value" xml:space="preserve">
    <value>값</value>
  </data>
  <data name="Scope_Is_visible" xml:space="preserve">
    <value>표시 여부</value>
  </data>
  <data name="Scope_Offset" xml:space="preserve">
    <value>오프셋</value>
  </data>
  <data name="Scope_Magni" xml:space="preserve">
    <value>배율</value>
  </data>
  <data name="Scope_Color" xml:space="preserve">
    <value>색</value>
  </data>
  <data name="Scope_Debug" xml:space="preserve">
    <value>디버깅</value>
  </data>
  <data name="ServoSerialPort_Driver_conn" xml:space="preserve">
    <value>드라이버 연결</value>
  </data>
  <data name="ServoSerialPort_Serial_port" xml:space="preserve">
    <value>직렬 포트</value>
  </data>
  <data name="ServoSerialPort_Baud_rate" xml:space="preserve">
    <value>전송 속도</value>
  </data>
  <data name="ServoSerialPort_Data_bits" xml:space="preserve">
    <value>데이터 비트</value>
  </data>
  <data name="ServoSerialPort_Parity_bit" xml:space="preserve">
    <value>패리티 비트</value>
  </data>
  <data name="ServoSerialPort_Stop_bits" xml:space="preserve">
    <value>정지 비트</value>
  </data>
  <data name="ServoSerialPort_Connect" xml:space="preserve">
    <value>연결</value>
  </data>
  <data name="ServoSerialPort_Disconnect" xml:space="preserve">
    <value>연결 해제</value>
  </data>
  <data name="ServoSetting_Driver_params" xml:space="preserve">
    <value>드라이버 매개변수</value>
  </data>
  <data name="ServoSetting_Sel_op" xml:space="preserve">
    <value>조작 선택:</value>
  </data>
  <data name="ServoSetting_Sel_write" xml:space="preserve">
    <value>쓰기 선택</value>
  </data>
  <data name="ServoSetting_Write_all" xml:space="preserve">
    <value>모두 쓰기</value>
  </data>
  <data name="ServoSetting_Restore_def_params" xml:space="preserve">
    <value>기본 매개변수 복원</value>
  </data>
  <data name="ServoSetting_Err_reset" xml:space="preserve">
    <value>에러 리셋</value>
  </data>
  <data name="ServoSetting_Fault_rec_clear" xml:space="preserve">
    <value>고장 기록 삭제</value>
  </data>
  <data name="ServoSetting_Drive_mode_set" xml:space="preserve">
    <value>드라이브 모드 설정:</value>
  </data>
  <data name="ServoSetting_Ctrl_right" xml:space="preserve">
    <value>제어 권한:</value>
  </data>
  <data name="ServoSetting_Local_ctrl_mode" xml:space="preserve">
    <value>로컬 제어 모드:</value>
  </data>
  <data name="ServoSetting_Sub_mode" xml:space="preserve">
    <value>하위 모드:</value>
  </data>
  <data name="ServoSetting_Select" xml:space="preserve">
    <value>선택</value>
  </data>
  <data name="ServoSetting_Param_name" xml:space="preserve">
    <value>매개변수 이름</value>
  </data>
  <data name="ServoSetting_Set_type" xml:space="preserve">
    <value>설정 유형</value>
  </data>
  <data name="ServoSetting_Min_val" xml:space="preserve">
    <value>최소값</value>
  </data>
  <data name="ServoSetting_Max_val" xml:space="preserve">
    <value>최대값</value>
  </data>
  <data name="ServoSetting_Read_val" xml:space="preserve">
    <value>읽은 값</value>
  </data>
  <data name="ServoSetting_Set_val" xml:space="preserve">
    <value>설정 값</value>
  </data>
  <data name="ServoSetting_Perm" xml:space="preserve">
    <value>권한</value>
  </data>
  <data name="ServoSetting_Coeff" xml:space="preserve">
    <value>계수</value>
  </data>
  <data name="ServoSetting_Monitor" xml:space="preserve">
    <value>모니터링</value>
  </data>
  <data name="ServoSetting_Desc" xml:space="preserve">
    <value>설명</value>
  </data>
  <data name="BasePermAssign_Role" xml:space="preserve">
    <value>역할:</value>
  </data>
  <data name="BasePermAssign_Refresh" xml:space="preserve">
    <value>새로 고침</value>
  </data>
  <data name="BasePermAssign_Perm" xml:space="preserve">
    <value>권한:</value>
  </data>
  <data name="BasePermAssign_Save" xml:space="preserve">
    <value>저장</value>
  </data>
  <data name="BasePermission_Enter_keywords" xml:space="preserve">
    <value>키워드를 입력하세요</value>
  </data>
  <data name="BasePermission_New" xml:space="preserve">
    <value>새로 만들기</value>
  </data>
  <data name="BasePermission_Refresh" xml:space="preserve">
    <value>새로 고침</value>
  </data>
  <data name="BasePermission_Menu" xml:space="preserve">
    <value>메뉴</value>
  </data>
  <data name="BasePermission_Bind_code" xml:space="preserve">
    <value>바인딩 코드</value>
  </data>
  <data name="BasePermission_Is_button" xml:space="preserve">
    <value>버튼인가?</value>
  </data>
  <data name="BasePermission_Is_hidden" xml:space="preserve">
    <value>숨김 여부</value>
  </data>
  <data name="BasePermission_Btn_event" xml:space="preserve">
    <value>버튼 이벤트</value>
  </data>
  <data name="BasePermission_Desc" xml:space="preserve">
    <value>설명</value>
  </data>
  <data name="BasePermission_Level" xml:space="preserve">
    <value>레벨</value>
  </data>
  <data name="BasePermission_Enable" xml:space="preserve">
    <value>활성화</value>
  </data>
  <data name="BasePermission_Creator" xml:space="preserve">
    <value>생성자</value>
  </data>
  <data name="BasePermission_Create_time" xml:space="preserve">
    <value>생성 시간</value>
  </data>
  <data name="BasePermission_Modifier" xml:space="preserve">
    <value>수정자</value>
  </data>
  <data name="BasePermission_Mod_time" xml:space="preserve">
    <value>수정 시간</value>
  </data>
  <data name="BasePermission_Op" xml:space="preserve">
    <value>조작</value>
  </data>
  <data name="BasePermission_Edit" xml:space="preserve">
    <value>편집</value>
  </data>
  <data name="BasePermission_Delete" xml:space="preserve">
    <value>삭제</value>
  </data>
  <data name="BasePermission_Menu_name" xml:space="preserve">
    <value>메뉴 이름:</value>
  </data>
  <data name="BasePermission_Parent_menu" xml:space="preserve">
    <value>상위 메뉴:</value>
  </data>
  <data name="BasePermission_Save" xml:space="preserve">
    <value>저장</value>
  </data>
  <data name="BasePermission_Cancel" xml:space="preserve">
    <value>취소</value>
  </data>
  <data name="BaseRole_Enter_keywords" xml:space="preserve">
    <value>키워드를 입력하세요</value>
  </data>
  <data name="BaseRole_New" xml:space="preserve">
    <value>새로 만들기</value>
  </data>
  <data name="BaseRole_Refresh" xml:space="preserve">
    <value>새로 고침</value>
  </data>
  <data name="BaseRole_Role_name" xml:space="preserve">
    <value>역할 이름</value>
  </data>
  <data name="BaseRole_Desc" xml:space="preserve">
    <value>설명</value>
  </data>
  <data name="BaseRole_Level" xml:space="preserve">
    <value>레벨</value>
  </data>
  <data name="BaseRole_Creator" xml:space="preserve">
    <value>생성자</value>
  </data>
  <data name="BaseRole_Create_time" xml:space="preserve">
    <value>생성 시간</value>
  </data>
  <data name="BaseRole_Modifier" xml:space="preserve">
    <value>수정자</value>
  </data>
  <data name="BaseRole_Mod_time" xml:space="preserve">
    <value>수정 시간</value>
  </data>
  <data name="BaseRole_Is_enabled" xml:space="preserve">
    <value>활성화 여부</value>
  </data>
  <data name="BaseRole_Op" xml:space="preserve">
    <value>조작</value>
  </data>
  <data name="BaseRole_Edit" xml:space="preserve">
    <value>편집</value>
  </data>
  <data name="BaseRole_Delete" xml:space="preserve">
    <value>삭제</value>
  </data>
  <data name="BaseRole_Pri_smaller_perm_bigger" xml:space="preserve">
    <value>우선순위가 작을수록 권한이 큼</value>
  </data>
  <data name="BaseRole_Enable_curr_role" xml:space="preserve">
    <value>현재 역할을 활성화할까요?</value>
  </data>
  <data name="BaseRole_Save" xml:space="preserve">
    <value>저장</value>
  </data>
  <data name="BaseRole_Cancel" xml:space="preserve">
    <value>취소</value>
  </data>
  <data name="BaseUser_Enter_keywords" xml:space="preserve">
    <value>키워드를 입력하세요</value>
  </data>
  <data name="BaseUser_New" xml:space="preserve">
    <value>새로 만들기</value>
  </data>
  <data name="BaseUser_Refresh" xml:space="preserve">
    <value>새로 고침</value>
  </data>
  <data name="BaseUser_User_name" xml:space="preserve">
    <value>사용자 이름</value>
  </data>
  <data name="BaseUser_Real_name" xml:space="preserve">
    <value>실명</value>
  </data>
  <data name="BaseUser_Role" xml:space="preserve">
    <value>역할</value>
  </data>
  <data name="BaseUser_Status" xml:space="preserve">
    <value>상태</value>
  </data>
  <data name="BaseUser_Remark" xml:space="preserve">
    <value>비고</value>
  </data>
  <data name="BaseUser_Create_time" xml:space="preserve">
    <value>생성 시간</value>
  </data>
  <data name="BaseUser_Mod_time" xml:space="preserve">
    <value>수정 시간</value>
  </data>
  <data name="BaseUser_Last_login" xml:space="preserve">
    <value>최종 로그인</value>
  </data>
  <data name="BaseUser_Op" xml:space="preserve">
    <value>조작</value>
  </data>
  <data name="BaseUser_Edit" xml:space="preserve">
    <value>편집</value>
  </data>
  <data name="BaseUser_Delete" xml:space="preserve">
    <value>삭제</value>
  </data>
  <data name="BaseUser_Login_name" xml:space="preserve">
    <value>로그인 이름:</value>
  </data>
  <data name="BaseUser_Passwd" xml:space="preserve">
    <value>비밀번호:</value>
  </data>
  <data name="BaseUser_Change_passwd" xml:space="preserve">
    <value>비밀번호 변경</value>
  </data>
  <data name="BaseUser_Pending_enable" xml:space="preserve">
    <value>활성화 대기</value>
  </data>
  <data name="BaseUser_Save" xml:space="preserve">
    <value>저장</value>
  </data>
  <data name="BaseUser_Cancel" xml:space="preserve">
    <value>취소</value>
  </data>
  <data name="PromptUserControl_No_menu_perm" xml:space="preserve">
    <value>이 메뉴에 대한 권한이 없습니다</value>
  </data>
  <data name="App_xaml_Ui_thread" xml:space="preserve">
    <value>UI 스레드:</value>
  </data>
  <data name="App_xaml_Ui_thread_exception" xml:space="preserve">
    <value>UI 스레드 예외:</value>
  </data>
  <data name="App_xaml_Ui_thread_fatal_error" xml:space="preserve">
    <value>UI 스레드에서 치명적인 오류가 발생했습니다!</value>
  </data>
  <data name="App_xaml_Non_ui_thread_fatal_error" xml:space="preserve">
    <value>비 UI 스레드에서 치명적인 오류가 발생했습니다</value>
  </data>
  <data name="App_xaml_Non_ui_thread_exception" xml:space="preserve">
    <value>비 UI 스레드 예외:</value>
  </data>
  <data name="App_xaml_Task_thread" xml:space="preserve">
    <value>Task 스레드:</value>
  </data>
  <data name="App_xaml_Task_thread_exception" xml:space="preserve">
    <value>Task 스레드 예외:</value>
  </data>
  <data name="DesignerHelper_Main_thread" xml:space="preserve">
    <value>메인 스레드</value>
  </data>
  <data name="ImageAttached_Switch" xml:space="preserve">
    <value>스위치</value>
  </data>
  <data name="PermissionHelper_No_permission_operation" xml:space="preserve">
    <value>해당 작업에 대한 권한이 없습니다</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_enable_status" xml:space="preserve">
    <value>단축기 상태</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_running_status" xml:space="preserve">
    <value>단축기 실행 상태</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_alarm_status" xml:space="preserve">
    <value>단축기 경보 상태</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_error_status" xml:space="preserve">
    <value>단축기 오류 상태</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_left_collision" xml:space="preserve">
    <value>단축기 왼쪽 충돌</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_right_collision" xml:space="preserve">
    <value>단축기 오른쪽 충돌</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_positive_limit" xml:space="preserve">
    <value>단축기 정限位</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_negative_limit" xml:space="preserve">
    <value>단축기 부限位</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_on_workstation" xml:space="preserve">
    <value>단축기 위치에 있습니다</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_reached_target" xml:space="preserve">
    <value>단축기가 목표 위치에 도달했습니다</value>
  </data>
  <data name="SysFeedBackMapping_System_ready" xml:space="preserve">
    <value>시스템이 준비되었습니다</value>
  </data>
  <data name="SysFeedBackMapping_System_enable_status" xml:space="preserve">
    <value>시스템 활성화 상태</value>
  </data>
  <data name="SysFeedBackMapping_System_error_status" xml:space="preserve">
    <value>시스템 오류 상태</value>
  </data>
  <data name="SysFeedBackMapping_System_running_status" xml:space="preserve">
    <value>시스템 실행 상태</value>
  </data>
  <data name="SysFeedBackMapping_System_bus_status" xml:space="preserve">
    <value>시스템 버스 상태</value>
  </data>
  <data name="SysFeedBackMapping_System_platform_verification" xml:space="preserve">
    <value>시스템 플랫폼 검증 상태</value>
  </data>
  <data name="SysFeedBackMapping_Axis_config_completed" xml:space="preserve">
    <value>축 구성이 완료되었습니다, 축 시퀀스 초기화를 수행할 수 있습니다</value>
  </data>
  <data name="SysFeedBackMapping_Motion_param_config_completed" xml:space="preserve">
    <value>운동 매개변수 구성이 완료되었습니다, 시스템 이전 상태 복원을 수행할 수 있습니다</value>
  </data>
  <data name="SysFeedBackMapping_System_state_restored" xml:space="preserve">
    <value>시스템 이전 상태 복원이 완료되었습니다</value>
  </data>
  <data name="SysFeedBackMapping_Bit8_31_reserved" xml:space="preserve">
    <value>bit8-31: 예약\n</value>
  </data>
  <data name="SqlsugarSetup_Sql_statement" xml:space="preserve">
    <value>【SQL 명령어】:</value>
  </data>
  <data name="SqlsugarSetup_Sql_parameters" xml:space="preserve">
    <value>【SQL 매개변수】:</value>
  </data>
  <data name="InputConverter_Input_value_range" xml:space="preserve">
    <value>입력값은 지정된 범위에 있어야 합니다</value>
  </data>
  <data name="BasePermAssignViewModel_Root_node" xml:space="preserve">
    <value>루트 노드</value>
  </data>
  <data name="BasePermAssignViewModel_Get_success" xml:space="preserve">
    <value>수집 성공</value>
  </data>
  <data name="BasePermissionViewModel_Root_node" xml:space="preserve">
    <value>루트 노드</value>
  </data>
  <data name="BasePermissionViewModel_Get_success" xml:space="preserve">
    <value>수집 성공</value>
  </data>
  <data name="BasePermissionViewModel_Add_success" xml:space="preserve">
    <value>추가 성공</value>
  </data>
  <data name="BasePermissionViewModel_Update_success" xml:space="preserve">
    <value>업데이트 성공</value>
  </data>
  <data name="BasePermissionViewModel_Delete_success" xml:space="preserve">
    <value>삭제 성공</value>
  </data>
  <data name="BaseUserViewModel_Get_success" xml:space="preserve">
    <value>수집 성공</value>
  </data>
  <data name="BaseUserViewModel_Add_success" xml:space="preserve">
    <value>추가 성공</value>
  </data>
  <data name="BaseUserViewModel_Update_success" xml:space="preserve">
    <value>업데이트 성공</value>
  </data>
  <data name="BaseUserViewModel_Delete_success" xml:space="preserve">
    <value>삭제 성공</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_forward" xml:space="preserve">
    <value>Jog 정방향运动</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_reverse" xml:space="preserve">
    <value>Jog 역방향运动</value>
  </data>
  <data name="ControlerAxisViewModel_Absolute_movement" xml:space="preserve">
    <value>절대运动</value>
  </data>
  <data name="ControlerAxisViewModel_Relative_movement" xml:space="preserve">
    <value>相対运动</value>
  </data>
  <data name="ControlerAxisViewModel_Workstation_movement" xml:space="preserve">
    <value>工位运动</value>
  </data>
  <data name="ControlerAxisViewModel_Set_zero_point" xml:space="preserve">
    <value>零点設定</value>
  </data>
  <data name="ControlerAxisViewModel_Axis_reset" xml:space="preserve">
    <value>축 복원</value>
  </data>
  <data name="ControlerGenerateConfigViewModel_Config_file_generated" xml:space="preserve">
    <value>설정 파일 생성 성공</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Workstation_config_distributed" xml:space="preserve">
    <value>工位 구성 전송 성공</value>
  </data>
  <data name="ControlerTranStatusViewModel_Do_nothing" xml:space="preserve">
    <value>처리하지 않음</value>
  </data>
  <data name="DataViewModel_Controller_disconnected" xml:space="preserve">
    <value>컨트롤러 연결이断开되었습니다!</value>
  </data>
  <data name="DataViewModel_Controller_connected" xml:space="preserve">
    <value>컨트롤러 연결이 성공되었습니다!</value>
  </data>
  <data name="MainViewModel_Controller_feedback_zero" xml:space="preserve">
    <value>컨트롤러가 축 수를 0으로反馈했습니다, 이 동작을 수행할 수 없습니다!</value>
  </data>
  <data name="ServoSettingViewModel_No_control" xml:space="preserve">
    <value>비 제어</value>
  </data>
  <data name="ServoSettingViewModel_Dual_axis_position_control" xml:space="preserve">
    <value>이축 위치 제어</value>
  </data>
  <data name="ServoSettingViewModel_Axis0_electrical_angle" xml:space="preserve">
    <value>축 0 전기 각도 식별</value>
  </data>
  <data name="ServoSettingViewModel_Dc_sampling_test" xml:space="preserve">
    <value>직류 샘플링 테스트</value>
  </data>
  <data name="ServoSettingViewModel_Ac_sampling_test" xml:space="preserve">
    <value>교류 샘플링 테스트</value>
  </data>
  <data name="ScopeView_xaml_Csv_file_filter" xml:space="preserve">
    <value>CSV 파일 (*.csv)|*.csv|모든 파일 (*.*)|*.*</value>
  </data>
  <data name="ScopeView_xaml_Select_csv_file" xml:space="preserve">
    <value>CSV 파일을 선택하세요</value>
  </data>
  <data name="ScopeView_xaml_Select_save_path" xml:space="preserve">
    <value>저장 경로를 선택하세요</value>
  </data>
  <data name="ScopeView_xaml_Data_export_success" xml:space="preserve">
    <value>데이터 추출 성공</value>
  </data>
  <data name="ObjectUtil_Object_not_empty" xml:space="preserve">
    <value>전달 객체는 비어 있을 수 없습니다!</value>
  </data>
  <data name="FileHelper_Newly_appended_content" xml:space="preserve">
    <value>새로 추가된 내용</value>
  </data>
  <data name="FileHelper_What_i_wrote" xml:space="preserve">
    <value>이것은 제가 쓴 내용입니다</value>
  </data>
  <data name="FileHelper_Directory_not_exist" xml:space="preserve">
    <value>해당 디렉토리가 존재하지 않습니다</value>
  </data>
  <data name="RecursionHelper_Button" xml:space="preserve">
    <value>버튼</value>
  </data>
  <data name="ControlerTcpClient_Send_data" xml:space="preserve">
    <value>데이터 전송:</value>
  </data>
  <data name="ControlerTcpClient_Adapter_parsing_failed" xml:space="preserve">
    <value>어댑터가 데이터를 파싱하지 못했습니다!</value>
  </data>
  <data name="ControlerTcpClient_Controller_not_connected" xml:space="preserve">
    <value>컨트롤러가 연결되지 않았습니다!</value>
  </data>
  <data name="ControlerTcpClient_Controller_heartbeat_failed" xml:space="preserve">
    <value>컨트롤러가 휴心跳을 보내지 못했습니다</value>
  </data>
  <data name="ControllerConst_Upper_enable" xml:space="preserve">
    <value>상위 활성화</value>
  </data>
  <data name="ControllerConst_Lower_enable" xml:space="preserve">
    <value>하위 활성화</value>
  </data>
  <data name="ControllerConst_Stop" xml:space="preserve">
    <value>정지</value>
  </data>
  <data name="ControllerConst_Reset" xml:space="preserve">
    <value>리셋</value>
  </data>
  <data name="ControllerConst_Set_zero_point" xml:space="preserve">
    <value>零点 설정</value>
  </data>
  <data name="ControllerConst_Forward_jog" xml:space="preserve">
    <value>전진 점동</value>
  </data>
  <data name="ControllerConst_Backward_jog" xml:space="preserve">
    <value>후진 점동</value>
  </data>
  <data name="ControllerConst_Absolute_movement" xml:space="preserve">
    <value>절대运动</value>
  </data>
  <data name="ControllerConst_Relative_movement" xml:space="preserve">
    <value>相対运动</value>
  </data>
  <data name="ControllerConst_Workstation_movement" xml:space="preserve">
    <value>工位运动</value>
  </data>
  <data name="SysCtrlCmdEnum_Upper_enable" xml:space="preserve">
    <value>상위 활성화</value>
  </data>
  <data name="SysCtrlCmdEnum_Lower_enable" xml:space="preserve">
    <value>하위 활성화</value>
  </data>
  <data name="SysCtrlCmdEnum_Error_reset" xml:space="preserve">
    <value>에러 리셋</value>
  </data>
  <data name="SysCtrlCmdEnum_Run" xml:space="preserve">
    <value>실행</value>
  </data>
  <data name="SysCtrlCmdEnum_Pause" xml:space="preserve">
    <value>일시정지</value>
  </data>
  <data name="SysCtrlCmdEnum_Emergency_stop" xml:space="preserve">
    <value>紧急停止</value>
  </data>
  <data name="AxisCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>제어 객체가 프로토콜에서 제거되었습니다, 이 속성을 사용하지 마세요</value>
  </data>
  <data name="SysCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>제어 객체가 프로토콜에서 제거되었습니다, 이 속성을 사용하지 마세요</value>
  </data>
  <data name="ScopeConst_Position_parameter" xml:space="preserve">
    <value>위치 매개변수</value>
  </data>
  <data name="ScopeConst_Axis0_position_feedback" xml:space="preserve">
    <value>축 0 위치 피드백</value>
  </data>
  <data name="ScopeConst_Axis1_position_feedback" xml:space="preserve">
    <value>축 1 위치 피드백</value>
  </data>
  <data name="ScopeConst_Speed_parameter" xml:space="preserve">
    <value>속도 매개변수</value>
  </data>
  <data name="ScopeConst_Axis0_speed_instruction" xml:space="preserve">
    <value>축 0 속도 명령</value>
  </data>
  <data name="ScopeConst_Axis0_speed_feedback" xml:space="preserve">
    <value>축 0 속도 피드백</value>
  </data>
  <data name="ScopeConst_Axis1_speed_instruction" xml:space="preserve">
    <value>축 1 속도 명령</value>
  </data>
  <data name="ScopeConst_Axis1_speed_feedback" xml:space="preserve">
    <value>축 1 속도 피드백</value>
  </data>
  <data name="ScopeConst_Current_parameter" xml:space="preserve">
    <value>전류 매개변수</value>
  </data>
  <data name="ScopeConst_Axis0_current_instruction" xml:space="preserve">
    <value>축 0 전류 명령</value>
  </data>
  <data name="ScopeConst_Axis0_current_feedback" xml:space="preserve">
    <value>축 0 전류 피드백</value>
  </data>
  <data name="ScopeConst_Axis1_current_instruction" xml:space="preserve">
    <value>축 1 전류 명령</value>
  </data>
  <data name="ScopeConst_Axis1_current_feedback" xml:space="preserve">
    <value>축 1 전류 피드백</value>
  </data>
  <data name="ScopeConst_Voltage_parameter" xml:space="preserve">
    <value>전압 매개변수</value>
  </data>
  <data name="ScopeConst_Axis0_d_axis_voltage" xml:space="preserve">
    <value>축 0 D축 참조 전압</value>
  </data>
  <data name="ScopeConst_Axis1_d_axis_voltage" xml:space="preserve">
    <value>축 1 D축 참조 전압</value>
  </data>
  <data name="ScopeConst_Axis0_q_axis_voltage" xml:space="preserve">
    <value>축 0 Q축 참조 전압</value>
  </data>
  <data name="ScopeConst_Axis1_q_axis_voltage" xml:space="preserve">
    <value>축 1 Q축 참조 전압</value>
  </data>
  <data name="ScopeConst_Axis0_bus_voltage" xml:space="preserve">
    <value>축 0母線电压</value>
  </data>
  <data name="ScopeConst_Axis1_bus_voltage" xml:space="preserve">
    <value>축 1母線电压</value>
  </data>
  <data name="ScopeConst_Axis0_u_phase_current" xml:space="preserve">
    <value>축 0_U상 전류</value>
  </data>
  <data name="ScopeConst_Axis1_u_phase_current" xml:space="preserve">
    <value>축 1_U상 전류</value>
  </data>
  <data name="ScopeConst_Axis0_v_phase_current" xml:space="preserve">
    <value>축 0_V상 전류</value>
  </data>
  <data name="ScopeConst_Axis1_v_phase_current" xml:space="preserve">
    <value>축 1_V상 전류</value>
  </data>
  <data name="ScopeConst_Axis0_w_phase_current" xml:space="preserve">
    <value>축 0_W상 전류</value>
  </data>
  <data name="ScopeConst_Axis1_w_phase_current" xml:space="preserve">
    <value>축 1_W상 전류</value>
  </data>
  <data name="ScopeConst_Axis0_control_voltage" xml:space="preserve">
    <value>축 0_제어 전압</value>
  </data>
  <data name="ScopeConst_Axis1_control_voltage" xml:space="preserve">
    <value>축 1_제어 전압</value>
  </data>
  <data name="ServoContext_Motor_parameter" xml:space="preserve">
    <value>1-모터 매개변수</value>
  </data>
  <data name="ServoContext_System_parameter" xml:space="preserve">
    <value>2-시스템 매개변수</value>
  </data>
  <data name="ServoContext_Encoder_parameter" xml:space="preserve">
    <value>3-인코더 매개변수</value>
  </data>
  <data name="ServoContext_Protection_parameter" xml:space="preserve">
    <value>4-보호 매개변수</value>
  </data>
  <data name="ServoContext_Fault_record" xml:space="preserve">
    <value>5-고장 기록</value>
  </data>
  <data name="ServoContext_Control_status" xml:space="preserve">
    <value>6-제어 상태</value>
  </data>
  <data name="ServoContext_Position_parameter" xml:space="preserve">
    <value>7-위치 매개변수</value>
  </data>
  <data name="ServoContext_Speed_parameter" xml:space="preserve">
    <value>8-속도 매개변수</value>
  </data>
  <data name="ServoContext_Torque_parameter" xml:space="preserve">
    <value>9-토크 매개변수</value>
  </data>
  <data name="ServoContext_Get_from_drive_context_exception" xml:space="preserve">
    <value>GetFromDriveContext 예외</value>
  </data>
  <data name="ServoSerialPortClient_Servo_heartbeat_failed" xml:space="preserve">
    <value>서보 휴心跳 전송 실패</value>
  </data>
  <data name="ElectricParaPackage_Third_instruction_not_exist" xml:space="preserve">
    <value>세번째 명령이 존재하지 않습니다</value>
  </data>
  <data name="RoleDto_Role_name_not_empty" xml:space="preserve">
    <value>역할 이름은 비어 있을 수 없습니다</value>
  </data>
  <data name="ParameterModel_Input_value_exceed_limit" xml:space="preserve">
    <value>입력값이 제한을 초과했습니다!</value>
  </data>
  <data name="ParameterModel_Input_value_incorrect" xml:space="preserve">
    <value>입력값이 잘못되었습니다!</value>
  </data>
  <data name="LineConfigEnum_System" xml:space="preserve">
    <value>시스템</value>
  </data>
  <data name="LineConfigEnum_Motor" xml:space="preserve">
    <value>모터</value>
  </data>
  <data name="LineConfigEnum_Slave_node" xml:space="preserve">
    <value>서브站 노드</value>
  </data>
  <data name="LineConfigEnum_Line" xml:space="preserve">
    <value>라인</value>
  </data>
  <data name="LineConfigEnum_Workstation" xml:space="preserve">
    <value>工位</value>
  </data>
  <data name="LineConfigEnum_Axis" xml:space="preserve">
    <value>축</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence" xml:space="preserve">
    <value>축 시퀀스</value>
  </data>
  <data name="LineConfigEnum_Axis_pid" xml:space="preserve">
    <value>축 PID</value>
  </data>
  <data name="LineConfigEnum_Axis_offset" xml:space="preserve">
    <value>축 오프셋</value>
  </data>
  <data name="LineConfigEnum_Device_wiring_direction" xml:space="preserve">
    <value>장치 연결 방향</value>
  </data>
  <data name="LineConfigEnum_Workstation_offset" xml:space="preserve">
    <value>工位 오프셋</value>
  </data>
  <data name="LineConfigEnum_Ui_view" xml:space="preserve">
    <value>UI 뷰</value>
  </data>
  <data name="LineConfigEnum_Configuration_parameter" xml:space="preserve">
    <value>구성 매개변수</value>
  </data>
  <data name="LineConfigEnum_System_configuration_parameter" xml:space="preserve">
    <value>시스템 구성 매개변수</value>
  </data>
  <data name="LineConfigEnum_Motor_configuration_parameter" xml:space="preserve">
    <value>모터 구성 매개변수</value>
  </data>
  <data name="LineConfigEnum_Slave_node_configuration_parameter" xml:space="preserve">
    <value>서브站 노드 구성 매개변수</value>
  </data>
  <data name="LineConfigEnum_Line_segment_configuration_parameter" xml:space="preserve">
    <value>라인 구간 구성 매개변수</value>
  </data>
  <data name="LineConfigEnum_Workstation_running_configuration_parameter" xml:space="preserve">
    <value>工位 실행 구성 매개변수</value>
  </data>
  <data name="LineConfigEnum_Rotor_configuration_parameter" xml:space="preserve">
    <value>모터 구성 매개변수</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence_configuration_parameter" xml:space="preserve">
    <value>축 시퀀스 구성 매개변수</value>
  </data>
  <data name="LineConfigEnum_Axis_running_pid_configuration_parameter" xml:space="preserve">
    <value>축 실행 PID 구성 매개변수</value>
  </data>
  <data name="LineConfigEnum_Rotor_compensation_configuration_parameter" xml:space="preserve">
    <value>모터 보补偿 구성 매개변수</value>
  </data>
  <data name="LineConfigEnum_Line_wiring_direction_configuration_parameter" xml:space="preserve">
    <value>라인 연결 방향 구성 매개변수</value>
  </data>
  <data name="LineConfigEnum_Workstation_compensation_configuration_parameter" xml:space="preserve">
    <value>工位 보补偿 구성 매개변수</value>
  </data>
  <data name="LineConfigEnum_Line_view_configuration_parameter" xml:space="preserve">
    <value>라인 뷰 구성 매개변수</value>
  </data>
  <data name="ParamTableEnum_Motor_parameter" xml:space="preserve">
    <value>1-모터 매개변수</value>
  </data>
  <data name="ParamTableEnum_System_parameter" xml:space="preserve">
    <value>2-시스템 매개변수</value>
  </data>
  <data name="ParamTableEnum_Encoder_parameter" xml:space="preserve">
    <value>3-인코더 매개변수</value>
  </data>
  <data name="ParamTableEnum_Protection_parameter" xml:space="preserve">
    <value>4-보호 매개변수</value>
  </data>
  <data name="ParamTableEnum_Fault_record" xml:space="preserve">
    <value>5-고장 기록</value>
  </data>
  <data name="ParamTableEnum_Control_status" xml:space="preserve">
    <value>6-제어 상태</value>
  </data>
  <data name="ParamTableEnum_Position_parameter" xml:space="preserve">
    <value>7-위치 매개변수</value>
  </data>
  <data name="ParamTableEnum_Speed_parameter" xml:space="preserve">
    <value>8-속도 매개변수</value>
  </data>
  <data name="ParamTableEnum_Torque_parameter" xml:space="preserve">
    <value>9-토크 매개변수</value>
  </data>
  <data name="ParameterModelExtension_Parameter_model_extension_exception" xml:space="preserve">
    <value>ParameterModelExtension 예외</value>
  </data>
  <data name="LocalizationManager_Simplified_chinese" xml:space="preserve">
    <value>简体中文</value>
  </data>
  <data name="LocalizationManager_Traditional_chinese" xml:space="preserve">
    <value>繁體中文</value>
  </data>
  <data name="LocalizationManager_Japanese" xml:space="preserve">
    <value>日本語</value>
  </data>
  <data name="OnlineConfigService_Unknown" xml:space="preserve">
    <value>未知</value>
  </data>
  <data name="OnlineConfigService_No_description" xml:space="preserve">
    <value>설명이 없습니다</value>
  </data>
  <data name="OnlineConfigService_No_value" xml:space="preserve">
    <value>값이 없습니다</value>
  </data>
  <data name="SerialCore_Remote_terminal_closed" xml:space="preserve">
    <value>원격 터미널이 닫혔습니다</value>
  </data>
  <data name="SerialCore_New_serial_port_connected" xml:space="preserve">
    <value>새로운 SerialPort는 연결 상태여야 합니다</value>
  </data>
  <data name="SerialPortClient_Data_processing_error" xml:space="preserve">
    <value>데이터를 처리할 때 오류가 발생했습니다</value>
  </data>
  <data name="SerialPortClient_Config_file_not_empty" xml:space="preserve">
    <value>구성 파일은 비어 있을 수 없습니다</value>
  </data>
  <data name="SerialPortClient_Serial_port_config_not_empty" xml:space="preserve">
    <value>시리얼 포트 구성은 비어 있을 수 없습니다</value>
  </data>
  <data name="SerialPortClient_Adapter_not_support_send" xml:space="preserve">
    <value>현재 어댑터는 객체를 전송하지 않습니다</value>
  </data>
  <data name="ControlerOnlineConfig_View_configuration" xml:space="preserve">
    <value>뷰 구성</value>
  </data>
  <data name="ControlerOnlineConfig_Motor_configuration" xml:space="preserve">
    <value>모터 구성</value>
  </data>
  <data name="ControlerOnlineConfig_Slave_node" xml:space="preserve">
    <value>슬레이브 노드</value>
  </data>
  <data name="ControlerOnlineConfig_Line_body_configuration" xml:space="preserve">
    <value>라인 구성</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_operation_configuration" xml:space="preserve">
    <value>작업장 운행 구성</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_configuration" xml:space="preserve">
    <value>축 구성</value>
  </data>
  <data name="ControlerOnlineConfig_Sequence_configuration" xml:space="preserve">
    <value>시퀀스 구성</value>
  </data>
  <data name="ControlerOnlineConfig_Pid_configuration" xml:space="preserve">
    <value>PID 구성</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_compensation_configuration" xml:space="preserve">
    <value>축 보상 구성</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_compensation_configuration" xml:space="preserve">
    <value>작업장 보상 구성</value>
  </data>
  <data name="ControlerOnlineConfig_Upload_to_controller_with_one_click" xml:space="preserve">
    <value>일회 클릭으로 컨트롤러에 업로드</value>
  </data>
  <data name="ControlerOnlineConfig_Download_to_local_with_one_click" xml:space="preserve">
    <value>일회 클릭으로 로컬로 다운로드</value>
  </data>
  <data name="ControlerOnlineConfig_Load_configuration" xml:space="preserve">
    <value>구성 로드</value>
  </data>
  <data name="ControlerOnlineConfig_Save_configuration_as" xml:space="preserve">
    <value>구성을 다른 이름으로 저장</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Send_success" xml:space="preserve">
    <value>전송 성공</value>
  </data>
  <data name="RoleDto_Role_name_cannot_be_empty" xml:space="preserve">
    <value>역할 이름은 비워둘 수 없습니다</value>
  </data>
  <data name="Main_Online_demonstration" xml:space="preserve">
    <value>온라인 데모</value>
  </data>
  <data name="Main_Alarm" xml:space="preserve">
    <value>알람</value>
  </data>
  <data name="NoticeListControl_Feedback_information" xml:space="preserve">
    <value>피드백 정보</value>
  </data>
  <data name="NoticeListControl_Clear_all_notifications" xml:space="preserve">
    <value>모든 알림 지우기</value>
  </data>
  <data name="NoticeListControl_Type" xml:space="preserve">
    <value>유형</value>
  </data>
  <data name="NoticeListControl_Source" xml:space="preserve">
    <value>소스</value>
  </data>
  <data name="NoticeListControl_Message_content" xml:space="preserve">
    <value>메시지 내용</value>
  </data>
  <data name="ControlerClient_Global_data_reset" xml:space="preserve">
    <value>전역 데이터 리셋</value>
  </data>
  <data name="ControlerClient_Platform_verification" xml:space="preserve">
    <value>플랫폼 검증</value>
  </data>
  <data name="ControlerClient_System_parameter_configuration_initialization" xml:space="preserve">
    <value>시스템 매개 변수 구성 초기화</value>
  </data>
  <data name="ControlerClient_Slave_station_information_acquisition" xml:space="preserve">
    <value>슬레이브 스테이션 정보 가져오기</value>
  </data>
  <data name="ControlerClient_Mapping_of_slave_station_address_to_control_address" xml:space="preserve">
    <value>슬레이브 스테이션 주소를 제어 주소로 매핑</value>
  </data>
  <data name="ControlerClient_Master_slave_station_status_verification" xml:space="preserve">
    <value>마스터-슬레이브 스테이션 상태 검증</value>
  </data>
  <data name="ControlerClient_Completion_of_status_initialization_of_bus_system_etc" xml:space="preserve">
    <value>버스-시스템 등 상태 초기화 완료</value>
  </data>
  <data name="ControlerClient_Initialization_of_movement_related_parameters" xml:space="preserve">
    <value>운동 관련 매개 변수 초기화</value>
  </data>
  <data name="ControlerClient_Successful_initialization_of_magnetic_drive" xml:space="preserve">
    <value>자기 구동 초기화 성공</value>
  </data>
  <data name="ControlerSys_System_drive_error" xml:space="preserve">
    <value>시스템 드라이브 오류</value>
  </data>
  <data name="FtpClient_Host" xml:space="preserve">
    <value>호스트:</value>
  </data>
  <data name="FtpClient_Port" xml:space="preserve">
    <value>포트:</value>
  </data>
  <data name="FtpClient_Username" xml:space="preserve">
    <value>사용자 이름:</value>
  </data>
  <data name="FtpClient_Password" xml:space="preserve">
    <value>비밀번호:</value>
  </data>
  <data name="FtpClient_Connect" xml:space="preserve">
    <value>연결</value>
  </data>
  <data name="FtpClient_Disconnect" xml:space="preserve">
    <value>연결 끊기</value>
  </data>
  <data name="FtpClient_Remote_directory" xml:space="preserve">
    <value>원격 디렉터리: </value>
  </data>
  <data name="FtpClient_Back" xml:space="preserve">
    <value>뒤로 가기</value>
  </data>
  <data name="FtpClient_Forward" xml:space="preserve">
    <value>앞으로 가기</value>
  </data>
  <data name="FtpClient_Up" xml:space="preserve">
    <value>위</value>
  </data>
  <data name="FtpClient_Refresh" xml:space="preserve">
    <value>새로 고침</value>
  </data>
  <data name="FtpClient_Create_folder" xml:space="preserve">
    <value>폴더 만들기</value>
  </data>
  <data name="FtpClient_Delete" xml:space="preserve">
    <value>삭제</value>
  </data>
  <data name="FtpClient_Download_to_local" xml:space="preserve">
    <value>로컬로 다운로드</value>
  </data>
  <data name="FtpClient_Local_directory" xml:space="preserve">
    <value>로컬 디렉터리: </value>
  </data>
  <data name="FtpClient_Upload_to_server" xml:space="preserve">
    <value>서버로 업로드</value>
  </data>
  <data name="FtpClient_Transmission_log" xml:space="preserve">
    <value>전송 로그:</value>
  </data>
  <data name="ServoSetting_System_soft_reset" xml:space="preserve">
    <value>시스템 소프트 리셋</value>
  </data>
  <data name="FtpClientViewModel_Connecting_to_ftp_server" xml:space="preserve">
    <value>FTP 서버에 연결 중...</value>
  </data>
  <data name="FtpClientViewModel_Connected_to_ftp_server" xml:space="preserve">
    <value>FTP 서버에 연결됨</value>
  </data>
  <data name="FtpClientViewModel_Connect" xml:space="preserve">
    <value>연결</value>
  </data>
  <data name="FtpClientViewModel_Disconnected" xml:space="preserve">
    <value>연결이 끊김</value>
  </data>
  <data name="FtpClientViewModel_Disconnect" xml:space="preserve">
    <value>연결 끊기</value>
  </data>
  <data name="FtpClientViewModel_Loading_remote_directory" xml:space="preserve">
    <value>원격 디렉터리 로드 중: </value>
  </data>
  <data name="FtpClientViewModel_Remote_directory_loaded" xml:space="preserve">
    <value>원격 디렉터리가 로드됨: </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_remote_directory" xml:space="preserve">
    <value>원격 디렉터리 로드 실패: </value>
  </data>
  <data name="FtpClientViewModel_Browse" xml:space="preserve">
    <value>탐색</value>
  </data>
  <data name="FtpClientViewModel_Loading_local_directory" xml:space="preserve">
    <value>로컬 디렉터리 로드 중: </value>
  </data>
  <data name="FtpClientViewModel_Local_directory_loaded" xml:space="preserve">
    <value>로컬 디렉터리가 로드됨: </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_local_directory" xml:space="preserve">
    <value>로컬 디렉터리 로드 실패: </value>
  </data>
  <data name="FtpClientViewModel_Downloading" xml:space="preserve">
    <value>다운로드 중: </value>
  </data>
  <data name="FtpClientViewModel_Download_completed" xml:space="preserve">
    <value>다운로드 완료: </value>
  </data>
  <data name="FtpClientViewModel_Download" xml:space="preserve">
    <value>다운로드</value>
  </data>
  <data name="FtpClientViewModel_Download_failed" xml:space="preserve">
    <value>다운로드 실패: </value>
  </data>
  <data name="FtpClientViewModel_Uploading" xml:space="preserve">
    <value>업로드 중: </value>
  </data>
  <data name="FtpClientViewModel_Upload_completed" xml:space="preserve">
    <value>업로드 완료: </value>
  </data>
  <data name="FtpClientViewModel_Upload" xml:space="preserve">
    <value>업로드</value>
  </data>
  <data name="FtpClientViewModel_Upload_failed" xml:space="preserve">
    <value>업로드 실패: </value>
  </data>
  <data name="FtpClientViewModel_Directory_created" xml:space="preserve">
    <value>디렉터리가 생성됨: </value>
  </data>
  <data name="FtpClientViewModel_Create_directory" xml:space="preserve">
    <value>디렉터리 만들기</value>
  </data>
  <data name="FtpClientViewModel_Failed_to_create_directory" xml:space="preserve">
    <value>디렉터리 생성 실패: </value>
  </data>
  <data name="FtpClientViewModel_Directory_deleted" xml:space="preserve">
    <value>디렉터리가 삭제됨: </value>
  </data>
  <data name="FtpClientViewModel_Delete" xml:space="preserve">
    <value>삭제</value>
  </data>
  <data name="FtpClientViewModel_File_deleted" xml:space="preserve">
    <value>파일이 삭제됨: </value>
  </data>
  <data name="FtpClientViewModel_Open" xml:space="preserve">
    <value>열기</value>
  </data>
  <data name="ControllerHelper_System_is_running" xml:space="preserve">
    <value>시스템이 실행 중</value>
  </data>
  <data name="ControllerHelper_System_is_ready" xml:space="preserve">
    <value>시스템이 준비됨</value>
  </data>
  <data name="ControllerHelper_System_is_enabled" xml:space="preserve">
    <value>시스템이 활성화됨</value>
  </data>
  <data name="ControllerHelper_System_bus_is_connected" xml:space="preserve">
    <value>시스템 버스가 연결됨</value>
  </data>
  <data name="ControllerHelper_System_is_in_error_state" xml:space="preserve">
    <value>시스템이 오류 상태</value>
  </data>
  <data name="ControllerHelper_Axis_driver_error" xml:space="preserve">
    <value>축 드라이브 오류</value>
  </data>
  <data name="ControllerHelper_Axis_movement_error" xml:space="preserve">
    <value>축 운동 오류</value>
  </data>
  <data name="ControllerHelper_Axis_error_status" xml:space="preserve">
    <value>축 오류 상태</value>
  </data>
  <data name="ControllerHelper_Axis_alarm" xml:space="preserve">
    <value>축 알람</value>
  </data>
  <data name="ControllerHelper_Positive_limit_of_axis" xml:space="preserve">
    <value>축 양 극 제한</value>
  </data>
  <data name="ControllerHelper_Negative_limit_of_axis" xml:space="preserve">
    <value>축 음 극 제한</value>
  </data>
  <data name="ControllerHelper_Axis_warning" xml:space="preserve">
    <value>축 경고</value>
  </data>
  <data name="ControllerHelper_Axis_in_left_position" xml:space="preserve">
    <value>축 왼쪽 도착</value>
  </data>
  <data name="ControllerHelper_Axis_in_right_position" xml:space="preserve">
    <value>축 오른쪽 도착</value>
  </data>
  <data name="ControllerHelper_Axis_has_reached_the_target_position" xml:space="preserve">
    <value>축이 목표 위치에 도달</value>
  </data>
  <data name="ControllerHelper_Axis_is_at_the_workstation" xml:space="preserve">
    <value>축이 작업소에 있음</value>
  </data>
  <data name="ControllerHelper_Axis_notification" xml:space="preserve">
    <value>축 알림</value>
  </data>
  <data name="ControllerHelper_Axis_is_running" xml:space="preserve">
    <value>축이 실행 중</value>
  </data>
  <data name="ControllerHelper_Axis_is_enabled" xml:space="preserve">
    <value>축이 활성화됨</value>
  </data>
  <data name="ControllerHelper_Axis_status" xml:space="preserve">
    <value>축 상태</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_running" xml:space="preserve">
    <value>회전축이 실행 중</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_homing_completed" xml:space="preserve">
    <value>회전축 홈 복귀 완료</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_enabled" xml:space="preserve">
    <value>회전축이 활성화됨</value>
  </data>
  <data name="ControllerHelper_Rotary_axis" xml:space="preserve">
    <value>회전축</value>
  </data>
  <data name="ServoSerialPortClient_Driver_connected_successfully" xml:space="preserve">
    <value>드라이브 연결 성공!</value>
  </data>
  <data name="ServoSerialPortClient_Driver_disconnected" xml:space="preserve">
    <value>드라이브 연결 해제!</value>
  </data>
  <data name="ServoSerialPortClient_Driver_parameter_recovery_successful" xml:space="preserve">
    <value>드라이브 매개 변수 복구 성공!</value>
  </data>
  <data name="Scope_Error_log" xml:space="preserve">
    <value>오류 기록</value>
  </data>
  <data name="Scope_Process_log" xml:space="preserve">
    <value>과정 기록</value>
  </data>
  <data name="Scope_Plc_signal" xml:space="preserve">
    <value>PLC 신호</value>
  </data>
  <data name="Scope_Plc_control_signal_count" xml:space="preserve">
    <value>PLC 제어 신호 카운트</value>
  </data>
  <data name="Scope_Acquire_count" xml:space="preserve">
    <value>카운트 가져오기</value>
  </data>
  <data name="Scope_Axis_0" xml:space="preserve">
    <value>축 0:</value>
  </data>
  <data name="Scope_Brake" xml:space="preserve">
    <value>브레이크:</value>
  </data>
  <data name="Scope_Acceleration" xml:space="preserve">
    <value>가속:</value>
  </data>
  <data name="Scope_Axis_1" xml:space="preserve">
    <value>축 1:</value>
  </data>
  <data name="ServoSetting_Cdm_driver_parameter_settings" xml:space="preserve">
    <value>CDM 드라이버 매개변수 설정:</value>
  </data>
  <data name="ServoSetting_Target_stop_position" xml:space="preserve">
    <value>목표 정지 위치:</value>
  </data>
  <data name="ServoSetting_Send" xml:space="preserve">
    <value>전송</value>
  </data>
  <data name="ServoSetting_Rotor_detection_position" xml:space="preserve">
    <value>이동자 감지 위치:</value>
  </data>
  <data name="ServoSetting_Enable_motor_position" xml:space="preserve">
    <value>모터 위치 활성화:</value>
  </data>
  <data name="ServoSetting_Current_user_no_permission_modify_this_parameter" xml:space="preserve">
    <value>현재 사용자는 이 매개변수를 수정할 권한이 없습니다</value>
  </data>
  <data name="ServoSettingViewModel_Csv_file_csv" xml:space="preserve">
    <value>CSV 파일|*.csv</value>
  </data>
  <data name="ServoSettingViewModel_Select_import_file" xml:space="preserve">
    <value>가져올 파일 선택</value>
  </data>
  <data name="ServoSettingViewModel_Select_export_path" xml:space="preserve">
    <value>내보낼 경로 선택</value>
  </data>
  <data name="ServoSettingViewModel_Driver_parameters_exported_successfully" xml:space="preserve">
    <value>드라이버 매개변수 내보내기 성공</value>
  </data>
  <data name="ServoSettingViewModel_Invalid_parameters_exist_check_input_values_retry" xml:space="preserve">
    <value>유효하지 않은 매개변수가 존재합니다. 입력값을 확인한 후 다시 시도하세요</value>
  </data>
  <data name="ServoSettingViewModel_Invalid_parameters_exist_check_all_input_values_retry" xml:space="preserve">
    <value>유효하지 않은 매개변수가 존재합니다. 모든 입력값을 확인한 후 다시 시도하세요</value>
  </data>
  <data name="ServoSettingViewModel_All_parameters_set_complete" xml:space="preserve">
    <value>모든 매개변수 설정 완료</value>
  </data>
  <data name="ScopeView_xaml_Serial_port_disconnected_oscilloscope_auto_stopped" xml:space="preserve">
    <value>직렬 포트가 연결 해제되었습니다. 오실로스코프가 자동으로 중지되었습니다</value>
  </data>
  <data name="JsonHelper_Object_to_serialize_cannot_be_null" xml:space="preserve">
    <value>직렬화할 개체는 null일 수 없습니다</value>
  </data>
  <data name="JsonHelper_File_path_cannot_be_empty" xml:space="preserve">
    <value>파일 경로는 비워둘 수 없습니다</value>
  </data>
  <data name="JsonHelper_Target_type_cannot_be_null" xml:space="preserve">
    <value>대상 유형은 null일 수 없습니다</value>
  </data>
  <data name="AxisCtrlCmdPackage_Control_object_removed_from_protocol_do_not_use_this_property" xml:space="preserve">
    <value>제어 개체가 프로토콜에서 제거되었습니다. 이 속성을 사용하지 마세요</value>
  </data>
  <data name="SysCtrlCmdPackage_Control_object_removed_from_protocol_do_not_use_this_property" xml:space="preserve">
    <value>제어 개체가 프로토콜에서 제거되었습니다. 이 속성을 사용하지 마세요</value>
  </data>
  <data name="ServoSerialPortClient_Serial_port_device_not_supported_no_authorization_response" xml:space="preserve">
    <value>직렬 포트 장치를 지원하지 않습니다. 권한 부여 응답을 받지 못했습니다</value>
  </data>
  <data name="ServoSerialPortClient_Error_sending_authorization_packet" xml:space="preserve">
    <value>권한 부여 패키지 전송 중 오류 발생</value>
  </data>
  <data name="ServoSerialPortClient_Serial_port_device_not_supported_authorization_failed" xml:space="preserve">
    <value>직렬 포트 장치를 지원하지 않습니다. 권한 부여 실패</value>
  </data>
  <data name="ParameterModel_Value_cannot_be_empty" xml:space="preserve">
    <value>값은 비워둘 수 없습니다</value>
  </data>
  <data name="ParameterModel_Input_value_cannot_contain_spaces_tabs" xml:space="preserve">
    <value>입력값에는 공백 또는 탭을 포함할 수 없습니다</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_number_format" xml:space="preserve">
    <value>유효한 숫자 형식을 입력하세요</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_16bit_unsigned_integer" xml:space="preserve">
    <value>유효한 16비트 부호 없는 정수(0-65535)를 입력하세요</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_32bit_unsigned_integer" xml:space="preserve">
    <value>유효한 32비트 부호 없는 정수(0-4294967295)를 입력하세요</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_16bit_signed_integer" xml:space="preserve">
    <value>유효한 16비트 부호 있는 정수(-32768～32767)를 입력하세요</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_32bit_signed_integer" xml:space="preserve">
    <value>유효한 32비트 부호 있는 정수(-2147483648～2147483647)를 입력하세요</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_floating_point_number" xml:space="preserve">
    <value>유효한 부동 소수점 수를 입력하세요</value>
  </data>
  <data name="ParameterModel_Input_value_cannot_be_nan_or_infinity" xml:space="preserve">
    <value>입력값은 NaN 또는 무한대일 수 없습니다</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_double_precision_floating_point_number" xml:space="preserve">
    <value>유효한 배정밀도 부동 소수점 수를 입력하세요</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_number" xml:space="preserve">
    <value>유효한 숫자를 입력하세요</value>
  </data>
  <data name="SerialCore_New_serialport_must_be_in_connected_state" xml:space="preserve">
    <value>새로운 SerialPort는 연결 상태여야 합니다.</value>
  </data>
  <data name="SerialPortClient_Error_processing_data" xml:space="preserve">
    <value>데이터 처리 중 오류 발생</value>
  </data>
  <data name="SerialPortClient_Configuration_file_cannot_be_empty" xml:space="preserve">
    <value>구성 파일은 비워둘 수 없습니다.</value>
  </data>
  <data name="SerialPortClient_Serial_port_configuration_cannot_be_empty" xml:space="preserve">
    <value>직렬 포트 구성은 비워둘 수 없습니다.</value>
  </data>
  <data name="SerialPortClient_Current_adapter_not_support_object_sending" xml:space="preserve">
    <value>현재 어댑터는 개체 전송을 지원하지 않습니다.</value>
  </data>
</root>