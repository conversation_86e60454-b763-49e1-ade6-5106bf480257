<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Language" xml:space="preserve">
    <value>Ngôn ngữ</value>
  </data>
  <data name="Main_Conn_disconnected" xml:space="preserve">
    <value>Kết nối bị ngắt</value>
  </data>
  <data name="Main_Conn_successful" xml:space="preserve">
    <value>Kết nối thành công</value>
  </data>
  <data name="Main_Auto" xml:space="preserve">
    <value>Tự động</value>
  </data>
  <data name="Main_Manual" xml:space="preserve">
    <value>Bằng tay</value>
  </data>
  <data name="Main_Init" xml:space="preserve">
    <value>Khởi tạo</value>
  </data>
  <data name="Main_Start" xml:space="preserve">
    <value>Bắt đầu</value>
  </data>
  <data name="Main_Stop" xml:space="preserve">
    <value>Dừng</value>
  </data>
  <data name="Main_Emergency_stop" xml:space="preserve">
    <value>Dừngkhẩncấp</value>
  </data>
  <data name="Main_Reset" xml:space="preserve">
    <value>Đặt lại</value>
  </data>
  <data name="Main_Enable" xml:space="preserve">
    <value>Kích hoạt</value>
  </data>
  <data name="Main_Axis_err_reset" xml:space="preserve">
    <value>Đặt lại lỗi trục</value>
  </data>
  <data name="Main_Sys_restart" xml:space="preserve">
    <value>Khởi động lại hệ thống</value>
  </data>
  <data name="Main_Save" xml:space="preserve">
    <value>Lưu</value>
  </data>
  <data name="Main_Station_init" xml:space="preserve">
    <value>Khởi tạo công việc</value>
  </data>
  <data name="Main_Station_enable" xml:space="preserve">
    <value>Kích hoạt công việc</value>
  </data>
  <data name="Main_Station_mask" xml:space="preserve">
    <value>Ẩn công việc</value>
  </data>
  <data name="Main_Fault" xml:space="preserve">
    <value>Lỗi</value>
  </data>
  <data name="Main_Maint" xml:space="preserve">
    <value>Bảo trì</value>
  </data>
  <data name="Main_Running" xml:space="preserve">
    <value>Đang chạy</value>
  </data>
  <data name="Main_Equip_conn" xml:space="preserve">
    <value>Kết nối thiết bị</value>
  </data>
  <data name="Main_Driver" xml:space="preserve">
    <value>Động cơ điều khiển</value>
  </data>
  <data name="Main_Ctrl" xml:space="preserve">
    <value>Bộ điều khiển</value>
  </data>
  <data name="Main_Plaintext_msg" xml:space="preserve">
    <value>Thông báo văn bản rõ</value>
  </data>
  <data name="Main_Fw_upgrade" xml:space="preserve">
    <value>Nâng cấp 펌웨어</value>
  </data>
  <data name="Main_Offline_conf" xml:space="preserve">
    <value>Cấu hình offline</value>
  </data>
  <data name="Main_Sys_assembly" xml:space="preserve">
    <value>Tổng thành phần hệ thống</value>
  </data>
  <data name="Main_Axis_ctrl" xml:space="preserve">
    <value>Điều khiển trục</value>
  </data>
  <data name="Main_Conn_stat" xml:space="preserve">
    <value>Trạng thái kết nối</value>
  </data>
  <data name="Main_Station_ctrl" xml:space="preserve">
    <value>Điều khiển công việc</value>
  </data>
  <data name="Main_Sys_ctrl" xml:space="preserve">
    <value>Điều khiển hệ thống</value>
  </data>
  <data name="Main_Feedback_info" xml:space="preserve">
    <value>Thông tin phản hồi</value>
  </data>
  <data name="Main_Err_fault" xml:space="preserve">
    <value>Lỗi và sự cố</value>
  </data>
  <data name="Main_Online_conf" xml:space="preserve">
    <value>Cấu hình online</value>
  </data>
  <data name="Main_Dev_comp" xml:space="preserve">
    <value>Bù đắp sai lệch</value>
  </data>
  <data name="Main_Curve_recip" xml:space="preserve">
    <value>Lặp lại sức mạnh cong</value>
  </data>
  <data name="Main_Conf_gen" xml:space="preserve">
    <value>Tạo cấu hình</value>
  </data>
  <data name="Main_Digital_io" xml:space="preserve">
    <value>IO số</value>
  </data>
  <data name="Main_Servo_conf" xml:space="preserve">
    <value>Cấu hình servo</value>
  </data>
  <data name="Main_Oscillo" xml:space="preserve">
    <value>Điện tử quét</value>
  </data>
  <data name="Main_Basic_sett" xml:space="preserve">
    <value>Cài đặt cơ bản</value>
  </data>
  <data name="Main_Role_mgmt" xml:space="preserve">
    <value>Quản lý vai trò</value>
  </data>
  <data name="Main_User_mgmt" xml:space="preserve">
    <value>Quản lý người dùng</value>
  </data>
  <data name="Main_Func_list" xml:space="preserve">
    <value>Danh sách chức năng</value>
  </data>
  <data name="Main_Perm_assign" xml:space="preserve">
    <value>Phân quyền</value>
  </data>
  <data name="Main_Data_trace" xml:space="preserve">
    <value>Truy vết dữ liệu</value>
  </data>
  <data name="Main_Op_log" xml:space="preserve">
    <value>Lịch sử thao tác</value>
  </data>
  <data name="Main_Sel_axis_sn" xml:space="preserve">
    <value>Chọn số thứ tự trục:</value>
  </data>
  <data name="Main_Driver_conn" xml:space="preserve">
    <value>Kết nối động cơ điều khiển:</value>
  </data>
  <data name="Main_Ctrl_conn" xml:space="preserve">
    <value>Kết nối bộ điều khiển:</value>
  </data>
  <data name="ControlerAxis_Mover_axis_ctrl" xml:space="preserve">
    <value>Điều khiển trục động tử</value>
  </data>
  <data name="ControlerAxis_Axis_mot_mode" xml:space="preserve">
    <value>Chế độ chuyển động trục:</value>
  </data>
  <data name="ControlerAxis_Jog_mot" xml:space="preserve">
    <value>Chuyển động Jog</value>
  </data>
  <data name="ControlerAxis_Abs_mot" xml:space="preserve">
    <value>Chuyển động tuyệt đối</value>
  </data>
  <data name="ControlerAxis_Rel_mot" xml:space="preserve">
    <value>Chuyển động tương đối</value>
  </data>
  <data name="ControlerAxis_Station_mot" xml:space="preserve">
    <value>Chuyển động công việc</value>
  </data>
  <data name="ControlerAxis_Axis_id" xml:space="preserve">
    <value>ID trục:</value>
  </data>
  <data name="ControlerAxis_Axis_type" xml:space="preserve">
    <value>Kiểu trục:</value>
  </data>
  <data name="ControlerAxis_Mover" xml:space="preserve">
    <value>Động tử</value>
  </data>
  <data name="ControlerAxis_Rotary_motor" xml:space="preserve">
    <value>Động cơ quay</value>
  </data>
  <data name="ControlerAxis_Linear_motor" xml:space="preserve">
    <value>Động cơ thẳng</value>
  </data>
  <data name="ControlerAxis_Speed_mode" xml:space="preserve">
    <value>Chế độ tốc độ:</value>
  </data>
  <data name="ControlerAxis_Axis_ctrl_mode" xml:space="preserve">
    <value>Chế độ điều khiển trục:</value>
  </data>
  <data name="ControlerAxis_Target_line_id" xml:space="preserve">
    <value>ID tuyến mục tiêu:</value>
  </data>
  <data name="ControlerAxis_Target_station_id" xml:space="preserve">
    <value>ID công việc mục tiêu:</value>
  </data>
  <data name="ControlerAxis_Speed" xml:space="preserve">
    <value>Tốc độ:</value>
  </data>
  <data name="ControlerAxis_Accel" xml:space="preserve">
    <value>Tăng tốc:</value>
  </data>
  <data name="ControlerAxis_Decel" xml:space="preserve">
    <value>Từ chối tốc độ:</value>
  </data>
  <data name="ControlerAxis_Jerk" xml:space="preserve">
    <value>Tăng tốc gia tốc:</value>
  </data>
  <data name="ControlerAxis_Pos_accu" xml:space="preserve">
    <value>Độ chính xác định vị:</value>
  </data>
  <data name="ControlerAxis_Anti_coll_accu" xml:space="preserve">
    <value>Độ chính xác phòng va chạm:</value>
  </data>
  <data name="ControlerAxis_Target_pos" xml:space="preserve">
    <value>Vị trí mục tiêu:</value>
  </data>
  <data name="ControlerAxis_Sel_op" xml:space="preserve">
    <value>Chọn thao tác:</value>
  </data>
  <data name="ControlerAxis_Exec" xml:space="preserve">
    <value>Thực hiện</value>
  </data>
  <data name="ControlerAxis_Read" xml:space="preserve">
    <value>Đọc</value>
  </data>
  <data name="ControlerAxis_Stop" xml:space="preserve">
    <value>Dừng</value>
  </data>
  <data name="ControlerAxis_Axis_obj" xml:space="preserve">
    <value>Đối tượng trục nằm trong</value>
  </data>
  <data name="ControlerAxis_Axis_line" xml:space="preserve">
    <value>Dòng trục nằm trong</value>
  </data>
  <data name="ControlerAxis_Driver_err" xml:space="preserve">
    <value>Lỗi động cơ điều khiển</value>
  </data>
  <data name="ControlerAxis_Axis_err" xml:space="preserve">
    <value>Lỗi trục</value>
  </data>
  <data name="ControlerAxis_Axis_curr_pos_mm" xml:space="preserve">
    <value>Vị trí hiện tại của trục (mm)</value>
  </data>
  <data name="ControlerAxis_Axis_curr_speed" xml:space="preserve">
    <value>Tốc độ hiện tại của trục</value>
  </data>
  <data name="ControlerAxis_Axis_curr_stat" xml:space="preserve">
    <value>Trạng thái hiện tại của trục</value>
  </data>
  <data name="ControlerClient_Ctrl_conn" xml:space="preserve">
    <value>Kết nối bộ điều khiển</value>
  </data>
  <data name="ControlerClient_Port" xml:space="preserve">
    <value>Cổng</value>
  </data>
  <data name="ControlerClient_Connect" xml:space="preserve">
    <value>Kết nối</value>
  </data>
  <data name="ControlerClient_Disconnect" xml:space="preserve">
    <value>Ngắt kết nối</value>
  </data>
  <data name="ControlerClient_Save" xml:space="preserve">
    <value>Lưu</value>
  </data>
  <data name="ControlerDebug_Send" xml:space="preserve">
    <value>Gửi:</value>
  </data>
  <data name="ControlerDebug_Log" xml:space="preserve">
    <value>Lịch sử:</value>
  </data>
  <data name="ControlerDebug_Clear" xml:space="preserve">
    <value>Xóa</value>
  </data>
  <data name="ControlerGenerateConfig_Conf_gen" xml:space="preserve">
    <value>Tạo cấu hình</value>
  </data>
  <data name="ControlerGenerateConfig_Sys_conf_num" xml:space="preserve">
    <value>Số lượng cấu hình hệ thống:</value>
  </data>
  <data name="ControlerGenerateConfig_Motor_conf_num" xml:space="preserve">
    <value>Số lượng cấu hình động cơ:</value>
  </data>
  <data name="ControlerGenerateConfig_Slave_node_conf_num" xml:space="preserve">
    <value>Số lượng cấu hình nút từ động:</value>
  </data>
  <data name="ControlerGenerateConfig_Line_seg_conf_num" xml:space="preserve">
    <value>Số lượng cấu hình đoạn tuyến:</value>
  </data>
  <data name="ControlerGenerateConfig_Station_conf_num" xml:space="preserve">
    <value>Số lượng cấu hình công việc:</value>
  </data>
  <data name="ControlerGenerateConfig_Mover_conf_num" xml:space="preserve">
    <value>Số lượng cấu hình động tử:</value>
  </data>
  <data name="ControlerGenerateConfig_Rot_axis_conf_num" xml:space="preserve">
    <value>Số lượng cấu hình trục quay:</value>
  </data>
  <data name="ControlerGenerateConfig_Io_conf_num" xml:space="preserve">
    <value>Số lượng cấu hình IO:</value>
  </data>
  <data name="ControlerGenerateConfig_Gen_conf_file" xml:space="preserve">
    <value>Tạo tệp cấu hình</value>
  </data>
  <data name="ControlerOnlineConfig_Online_conf" xml:space="preserve">
    <value>Cấu hình online</value>
  </data>
  <data name="ControlerOnlineConfig_Sel_conf" xml:space="preserve">
    <value>Chọn cấu hình:</value>
  </data>
  <data name="ControlerOnlineConfig_Sys_conf" xml:space="preserve">
    <value>Cấu hình hệ thống</value>
  </data>
  <data name="ControlerOnlineConfig_Station_conf" xml:space="preserve">
    <value>Cấu hình công việc</value>
  </data>
  <data name="ControlerOnlineConfig_Write" xml:space="preserve">
    <value>Ghi</value>
  </data>
  <data name="ControlerOnlineConfig_Param_name" xml:space="preserve">
    <value>Tên tham số</value>
  </data>
  <data name="ControlerOnlineConfig_Set_type" xml:space="preserve">
    <value>Kiểu thiết lập</value>
  </data>
  <data name="ControlerOnlineConfig_Read_val" xml:space="preserve">
    <value>Giá trị đọc</value>
  </data>
  <data name="ControlerOnlineConfig_Set_val" xml:space="preserve">
    <value>Giá trị thiết lập</value>
  </data>
  <data name="ControlerOnlineConfig_Desc" xml:space="preserve">
    <value>Mô tả</value>
  </data>
  <data name="ControlerSys_Sys_ctrl" xml:space="preserve">
    <value>Điều khiển hệ thống</value>
  </data>
  <data name="ControlerSys_Ctrl_obj" xml:space="preserve">
    <value>Đối tượng điều khiển:</value>
  </data>
  <data name="ControlerSys_Mover" xml:space="preserve">
    <value>Động tử</value>
  </data>
  <data name="ControlerSys_Rotary_motor" xml:space="preserve">
    <value>Động cơ quay</value>
  </data>
  <data name="ControlerSys_Linear_motor" xml:space="preserve">
    <value>Động cơ thẳng</value>
  </data>
  <data name="ControlerSys_Sys_op_mode" xml:space="preserve">
    <value>Chế độ hoạt động hệ thống:</value>
  </data>
  <data name="ControlerSys_Axis_teach" xml:space="preserve">
    <value>Học trục</value>
  </data>
  <data name="ControlerSys_Conn_teach" xml:space="preserve">
    <value>Học kết nối</value>
  </data>
  <data name="ControlerSys_Auto_op" xml:space="preserve">
    <value>Hoạt động tự động</value>
  </data>
  <data name="ControlerSys_Auto_op_mode" xml:space="preserve">
    <value>Chế độ hoạt động tự động:</value>
  </data>
  <data name="ControlerSys_Sync" xml:space="preserve">
    <value>Đồng bộ</value>
  </data>
  <data name="ControlerSys_Async" xml:space="preserve">
    <value>Bất đồng bộ</value>
  </data>
  <data name="ControlerSys_Speed_perc" xml:space="preserve">
    <value>Tỷ lệ tốc độ:</value>
  </data>
  <data name="ControlerSys_Slave_node_id" xml:space="preserve">
    <value>ID nút từ động:</value>
  </data>
  <data name="ControlerSys_Ctrl_mode" xml:space="preserve">
    <value>Chế độ điều khiển:</value>
  </data>
  <data name="ControlerSys_Sel_op" xml:space="preserve">
    <value>Chọn thao tác:</value>
  </data>
  <data name="ControlerSys_Exec" xml:space="preserve">
    <value>Thực hiện</value>
  </data>
  <data name="ControlerSys_Read" xml:space="preserve">
    <value>Đọc</value>
  </data>
  <data name="ControlerSys_Sys_err_axis_id" xml:space="preserve">
    <value>ID trục lỗi hệ thống</value>
  </data>
  <data name="ControlerSys_Sys_err_driver" xml:space="preserve">
    <value>Động cơ điều khiển lỗi hệ thống</value>
  </data>
  <data name="ControlerSys_Sys_err_code" xml:space="preserve">
    <value>Mã lỗi hệ thống</value>
  </data>
  <data name="ControlerSys_Sys_err_num" xml:space="preserve">
    <value>Mã lỗi hệ thống</value>
  </data>
  <data name="ControlerSys_Sys_stat" xml:space="preserve">
    <value>Trạng thái hệ thống</value>
  </data>
  <data name="ControlerTranStatus_Conn_ctrl" xml:space="preserve">
    <value>Điều khiển kết nối</value>
  </data>
  <data name="ControlerTranStatus_Conn_conf" xml:space="preserve">
    <value>Cấu hình kết nối:</value>
  </data>
  <data name="ControlerTranStatus_Curr_obj_id" xml:space="preserve">
    <value>ID đối tượng hiện tại:</value>
  </data>
  <data name="ControlerTranStatus_Left_obj_id" xml:space="preserve">
    <value>ID đối tượng bên trái:</value>
  </data>
  <data name="ControlerTranStatus_Conn_stat" xml:space="preserve">
    <value>Trạng thái kết nối:</value>
  </data>
  <data name="ControlerTranStatus_Disconnect" xml:space="preserve">
    <value>Ngắt kết nối</value>
  </data>
  <data name="ControlerTranStatus_Est_conn" xml:space="preserve">
    <value>Thiết lập kết nối</value>
  </data>
  <data name="ControlerTranStatus_Right_obj_id" xml:space="preserve">
    <value>ID đối tượng bên phải:</value>
  </data>
  <data name="ControlerTranStatus_Sel_op" xml:space="preserve">
    <value>Chọn thao tác:</value>
  </data>
  <data name="ControlerTranStatus_Exec" xml:space="preserve">
    <value>Thực hiện</value>
  </data>
  <data name="ControlerTranStatus_Read" xml:space="preserve">
    <value>Đọc</value>
  </data>
  <data name="ControlerTranStatus_Conn_id" xml:space="preserve">
    <value>ID kết nối:</value>
  </data>
  <data name="ControlerTranStatus_Target_station_id" xml:space="preserve">
    <value>ID công việc mục tiêu:</value>
  </data>
  <data name="ControlerTranStatus_Speed" xml:space="preserve">
    <value>Tốc độ:</value>
  </data>
  <data name="ControlerTranStatus_Accel" xml:space="preserve">
    <value>Tăng tốc:</value>
  </data>
  <data name="ControlerTranStatus_Decel" xml:space="preserve">
    <value>Từ chối tốc độ:</value>
  </data>
  <data name="ControlerTranStatus_Target_pos" xml:space="preserve">
    <value>Vị trí mục tiêu:</value>
  </data>
  <data name="ControlerTranStatus_Ctrl_cmd" xml:space="preserve">
    <value>Lệnh điều khiển:</value>
  </data>
  <data name="ControlerTranStatus_Line_id" xml:space="preserve">
    <value>ID tuyến</value>
  </data>
  <data name="ControlerTranStatus_Line_left_conn_obj_id" xml:space="preserve">
    <value>ID đối tượng kết nối bên trái của tuyến</value>
  </data>
  <data name="ControlerTranStatus_Line_right_conn_obj_id" xml:space="preserve">
    <value>ID đối tượng kết nối bên phải của tuyến</value>
  </data>
  <data name="ControlerTranStatus_Enable_stat" xml:space="preserve">
    <value>Trạng thái kích hoạt</value>
  </data>
  <data name="ControlerTranStatus_Run_stat" xml:space="preserve">
    <value>Trạng thái chạy</value>
  </data>
  <data name="ControlerTranStatus_Homing_done" xml:space="preserve">
    <value>Hoàn thành quay về gốc</value>
  </data>
  <data name="ControlerTranStatus_Err_code" xml:space="preserve">
    <value>Mã lỗi</value>
  </data>
  <data name="ControlerTranStatus_Act_speed" xml:space="preserve">
    <value>Tốc độ thực tế</value>
  </data>
  <data name="ControlerTranStatus_Act_pos" xml:space="preserve">
    <value>Vị trí thực tế</value>
  </data>
  <data name="Login_User_name" xml:space="preserve">
    <value>Tên người dùng</value>
  </data>
  <data name="Login_Passwd" xml:space="preserve">
    <value>Mật khẩu</value>
  </data>
  <data name="Login_Rem_passwd" xml:space="preserve">
    <value>Ghi nhớ mật khẩu</value>
  </data>
  <data name="Login_Login" xml:space="preserve">
    <value>Đăng nhập</value>
  </data>
  <data name="OperateLog_Enter_keywords" xml:space="preserve">
    <value>Vui lòng nhập từ khóa</value>
  </data>
  <data name="OperateLog_Refresh" xml:space="preserve">
    <value>Làm mới</value>
  </data>
  <data name="OperateLog_Start_time" xml:space="preserve">
    <value>Thời gian bắt đầu: </value>
  </data>
  <data name="OperateLog_Time" xml:space="preserve">
    <value>Thời gian</value>
  </data>
  <data name="OperateLog_Module" xml:space="preserve">
    <value>Mô-đun</value>
  </data>
  <data name="OperateLog_Op" xml:space="preserve">
    <value>Thao tác</value>
  </data>
  <data name="OperateLog_Behav" xml:space="preserve">
    <value>Hành vi</value>
  </data>
  <data name="OperateLog_Desc" xml:space="preserve">
    <value>Mô tả</value>
  </data>
  <data name="OperateLog_Operator" xml:space="preserve">
    <value>Người thao tác</value>
  </data>
  <data name="OperateLog_View" xml:space="preserve">
    <value>Xem</value>
  </data>
  <data name="OperateLog_Details" xml:space="preserve">
    <value>Chi tiết</value>
  </data>
  <data name="OperateLog_Detailed_desc" xml:space="preserve">
    <value>Mô tả chi tiết:</value>
  </data>
  <data name="OperateLog_Cancel" xml:space="preserve">
    <value>Hủy</value>
  </data>
  <data name="Scope_Stop" xml:space="preserve">
    <value>Dừng</value>
  </data>
  <data name="Scope_Collect" xml:space="preserve">
    <value>Thu thập</value>
  </data>
  <data name="Scope_Reset" xml:space="preserve">
    <value>Đặt lại</value>
  </data>
  <data name="Scope_Cross_star" xml:space="preserve">
    <value>Sao chữ thập</value>
  </data>
  <data name="Scope_X_axis_scale" xml:space="preserve">
    <value>Thước thẳng X</value>
  </data>
  <data name="Scope_Y_axis_scale" xml:space="preserve">
    <value>Thước thẳng Y</value>
  </data>
  <data name="Scope_Import" xml:space="preserve">
    <value>Nhập</value>
  </data>
  <data name="Scope_Export" xml:space="preserve">
    <value>Xuất</value>
  </data>
  <data name="Scope_Check_err" xml:space="preserve">
    <value>Kiểm tra lỗi</value>
  </data>
  <data name="Scope_Zoom" xml:space="preserve">
    <value>Phóng to/Thu nhỏ</value>
  </data>
  <data name="Scope_Sample_freq_1_300_ms" xml:space="preserve">
    <value>Tần số lấy mẫu (1 - 300, đơn vị ms):</value>
  </data>
  <data name="Scope_Channel" xml:space="preserve">
    <value>Kênh</value>
  </data>
  <data name="Scope_Sel_obj" xml:space="preserve">
    <value>Chọn đối tượng</value>
  </data>
  <data name="Scope_Please_select" xml:space="preserve">
    <value>Vui lòng chọn</value>
  </data>
  <data name="Scope_Value" xml:space="preserve">
    <value>Giá trị</value>
  </data>
  <data name="Scope_Is_visible" xml:space="preserve">
    <value>Có hiển thị không</value>
  </data>
  <data name="Scope_Offset" xml:space="preserve">
    <value>Lệch</value>
  </data>
  <data name="Scope_Magni" xml:space="preserve">
    <value>Tỉ lệ phóng to</value>
  </data>
  <data name="Scope_Color" xml:space="preserve">
    <value>Màu sắc</value>
  </data>
  <data name="Scope_Debug" xml:space="preserve">
    <value>Gỡ lỗi</value>
  </data>
  <data name="ServoSerialPort_Driver_conn" xml:space="preserve">
    <value>Kết nối động cơ điều khiển</value>
  </data>
  <data name="ServoSerialPort_Serial_port" xml:space="preserve">
    <value>Cổng nối tiếp</value>
  </data>
  <data name="ServoSerialPort_Baud_rate" xml:space="preserve">
    <value>Tốc độ truyền</value>
  </data>
  <data name="ServoSerialPort_Data_bits" xml:space="preserve">
    <value>Độ dài dữ liệu</value>
  </data>
  <data name="ServoSerialPort_Parity_bit" xml:space="preserve">
    <value>Độ kiểm tra</value>
  </data>
  <data name="ServoSerialPort_Stop_bits" xml:space="preserve">
    <value>Độ dừng</value>
  </data>
  <data name="ServoSerialPort_Connect" xml:space="preserve">
    <value>Kết nối</value>
  </data>
  <data name="ServoSerialPort_Disconnect" xml:space="preserve">
    <value>Ngắt kết nối</value>
  </data>
  <data name="ServoSetting_Driver_params" xml:space="preserve">
    <value>Tham số động cơ điều khiển</value>
  </data>
  <data name="ServoSetting_Sel_op" xml:space="preserve">
    <value>Chọn thao tác:</value>
  </data>
  <data name="ServoSetting_Sel_write" xml:space="preserve">
    <value>Chọn ghi</value>
  </data>
  <data name="ServoSetting_Write_all" xml:space="preserve">
    <value>Ghi tất cả</value>
  </data>
  <data name="ServoSetting_Restore_def_params" xml:space="preserve">
    <value>Khôi phục tham số mặc định</value>
  </data>
  <data name="ServoSetting_Err_reset" xml:space="preserve">
    <value>Đặt lại lỗi</value>
  </data>
  <data name="ServoSetting_Fault_rec_clear" xml:space="preserve">
    <value>Xóa bản ghi sự cố</value>
  </data>
  <data name="ServoSetting_Drive_mode_set" xml:space="preserve">
    <value>Đặt chế độ điều khiển:</value>
  </data>
  <data name="ServoSetting_Ctrl_right" xml:space="preserve">
    <value>Quyền kiểm soát:</value>
  </data>
  <data name="ServoSetting_Local_ctrl_mode" xml:space="preserve">
    <value>Chế độ điều khiển cục bộ:</value>
  </data>
  <data name="ServoSetting_Sub_mode" xml:space="preserve">
    <value>Kiểu con:</value>
  </data>
  <data name="ServoSetting_Select" xml:space="preserve">
    <value>Chọn</value>
  </data>
  <data name="ServoSetting_Param_name" xml:space="preserve">
    <value>Tên tham số</value>
  </data>
  <data name="ServoSetting_Set_type" xml:space="preserve">
    <value>Kiểu thiết lập</value>
  </data>
  <data name="ServoSetting_Min_val" xml:space="preserve">
    <value>Giá trị nhỏ nhất</value>
  </data>
  <data name="ServoSetting_Max_val" xml:space="preserve">
    <value>Giá trị lớn nhất</value>
  </data>
  <data name="ServoSetting_Read_val" xml:space="preserve">
    <value>Giá trị đọc</value>
  </data>
  <data name="ServoSetting_Set_val" xml:space="preserve">
    <value>Giá trị thiết lập</value>
  </data>
  <data name="ServoSetting_Perm" xml:space="preserve">
    <value>Quyền hạn</value>
  </data>
  <data name="ServoSetting_Coeff" xml:space="preserve">
    <value>Hệ số</value>
  </data>
  <data name="ServoSetting_Monitor" xml:space="preserve">
    <value>Theo dõi</value>
  </data>
  <data name="ServoSetting_Desc" xml:space="preserve">
    <value>Mô tả</value>
  </data>
  <data name="BasePermAssign_Role" xml:space="preserve">
    <value>Vai trò:</value>
  </data>
  <data name="BasePermAssign_Refresh" xml:space="preserve">
    <value>Làm mới</value>
  </data>
  <data name="BasePermAssign_Perm" xml:space="preserve">
    <value>Quyền hạn:</value>
  </data>
  <data name="BasePermAssign_Save" xml:space="preserve">
    <value>Lưu</value>
  </data>
  <data name="BasePermission_Enter_keywords" xml:space="preserve">
    <value>Vui lòng nhập từ khóa</value>
  </data>
  <data name="BasePermission_New" xml:space="preserve">
    <value>Tạo mới</value>
  </data>
  <data name="BasePermission_Refresh" xml:space="preserve">
    <value>Làm mới</value>
  </data>
  <data name="BasePermission_Menu" xml:space="preserve">
    <value>Menu</value>
  </data>
  <data name="BasePermission_Bind_code" xml:space="preserve">
    <value>Mã ràng buộc</value>
  </data>
  <data name="BasePermission_Is_button" xml:space="preserve">
    <value>Có phải là nút không</value>
  </data>
  <data name="BasePermission_Is_hidden" xml:space="preserve">
    <value>Có ẩn không</value>
  </data>
  <data name="BasePermission_Btn_event" xml:space="preserve">
    <value>Sự kiện nút</value>
  </data>
  <data name="BasePermission_Desc" xml:space="preserve">
    <value>Mô tả</value>
  </data>
  <data name="BasePermission_Level" xml:space="preserve">
    <value>Cấp độ</value>
  </data>
  <data name="BasePermission_Enable" xml:space="preserve">
    <value>Kích hoạt</value>
  </data>
  <data name="BasePermission_Creator" xml:space="preserve">
    <value>Người tạo</value>
  </data>
  <data name="BasePermission_Create_time" xml:space="preserve">
    <value>Thời gian tạo</value>
  </data>
  <data name="BasePermission_Modifier" xml:space="preserve">
    <value>Người sửa</value>
  </data>
  <data name="BasePermission_Mod_time" xml:space="preserve">
    <value>Thời gian sửa</value>
  </data>
  <data name="BasePermission_Op" xml:space="preserve">
    <value>Thao tác</value>
  </data>
  <data name="BasePermission_Edit" xml:space="preserve">
    <value>Chỉnh sửa</value>
  </data>
  <data name="BasePermission_Delete" xml:space="preserve">
    <value>Xóa</value>
  </data>
  <data name="BasePermission_Menu_name" xml:space="preserve">
    <value>Tên menu:</value>
  </data>
  <data name="BasePermission_Parent_menu" xml:space="preserve">
    <value>Menu cha:</value>
  </data>
  <data name="BasePermission_Save" xml:space="preserve">
    <value>Lưu</value>
  </data>
  <data name="BasePermission_Cancel" xml:space="preserve">
    <value>Hủy</value>
  </data>
  <data name="BaseRole_Enter_keywords" xml:space="preserve">
    <value>Vui lòng nhập từ khóa</value>
  </data>
  <data name="BaseRole_New" xml:space="preserve">
    <value>Tạo mới</value>
  </data>
  <data name="BaseRole_Refresh" xml:space="preserve">
    <value>Làm mới</value>
  </data>
  <data name="BaseRole_Role_name" xml:space="preserve">
    <value>Tên vai trò</value>
  </data>
  <data name="BaseRole_Desc" xml:space="preserve">
    <value>Mô tả</value>
  </data>
  <data name="BaseRole_Level" xml:space="preserve">
    <value>Cấp độ</value>
  </data>
  <data name="BaseRole_Creator" xml:space="preserve">
    <value>Người tạo</value>
  </data>
  <data name="BaseRole_Create_time" xml:space="preserve">
    <value>Thời gian tạo</value>
  </data>
  <data name="BaseRole_Modifier" xml:space="preserve">
    <value>Người sửa</value>
  </data>
  <data name="BaseRole_Mod_time" xml:space="preserve">
    <value>Thời gian sửa</value>
  </data>
  <data name="BaseRole_Is_enabled" xml:space="preserve">
    <value>Có kích hoạt không</value>
  </data>
  <data name="BaseRole_Op" xml:space="preserve">
    <value>Thao tác</value>
  </data>
  <data name="BaseRole_Edit" xml:space="preserve">
    <value>Chỉnh sửa</value>
  </data>
  <data name="BaseRole_Delete" xml:space="preserve">
    <value>Xóa</value>
  </data>
  <data name="BaseRole_Pri_smaller_perm_bigger" xml:space="preserve">
    <value>Điểm ưu tiên càng nhỏ, quyền hạn càng lớn</value>
  </data>
  <data name="BaseRole_Enable_curr_role" xml:space="preserve">
    <value>Có kích hoạt vai trò hiện tại không:</value>
  </data>
  <data name="BaseRole_Save" xml:space="preserve">
    <value>Lưu</value>
  </data>
  <data name="BaseRole_Cancel" xml:space="preserve">
    <value>Hủy</value>
  </data>
  <data name="BaseUser_Enter_keywords" xml:space="preserve">
    <value>Vui lòng nhập từ khóa</value>
  </data>
  <data name="BaseUser_New" xml:space="preserve">
    <value>Tạo mới</value>
  </data>
  <data name="BaseUser_Refresh" xml:space="preserve">
    <value>Làm mới</value>
  </data>
  <data name="BaseUser_User_name" xml:space="preserve">
    <value>Tên người dùng</value>
  </data>
  <data name="BaseUser_Real_name" xml:space="preserve">
    <value>Tên thật</value>
  </data>
  <data name="BaseUser_Role" xml:space="preserve">
    <value>Vai trò</value>
  </data>
  <data name="BaseUser_Status" xml:space="preserve">
    <value>Trạng thái</value>
  </data>
  <data name="BaseUser_Remark" xml:space="preserve">
    <value>Ghi chú</value>
  </data>
  <data name="BaseUser_Create_time" xml:space="preserve">
    <value>Thời gian tạo</value>
  </data>
  <data name="BaseUser_Mod_time" xml:space="preserve">
    <value>Thời gian sửa</value>
  </data>
  <data name="BaseUser_Last_login" xml:space="preserve">
    <value>Lần đăng nhập cuối</value>
  </data>
  <data name="BaseUser_Op" xml:space="preserve">
    <value>Thao tác</value>
  </data>
  <data name="BaseUser_Edit" xml:space="preserve">
    <value>Chỉnh sửa</value>
  </data>
  <data name="BaseUser_Delete" xml:space="preserve">
    <value>Xóa</value>
  </data>
  <data name="BaseUser_Login_name" xml:space="preserve">
    <value>Tên đăng nhập:</value>
  </data>
  <data name="BaseUser_Passwd" xml:space="preserve">
    <value>Mật khẩu:</value>
  </data>
  <data name="BaseUser_Change_passwd" xml:space="preserve">
    <value>Đổi mật khẩu</value>
  </data>
  <data name="BaseUser_Pending_enable" xml:space="preserve">
    <value>Chờ kích hoạt</value>
  </data>
  <data name="BaseUser_Save" xml:space="preserve">
    <value>Lưu</value>
  </data>
  <data name="BaseUser_Cancel" xml:space="preserve">
    <value>Hủy</value>
  </data>
  <data name="PromptUserControl_No_menu_perm" xml:space="preserve">
    <value>Bạn không có quyền sử dụng menu này</value>
  </data>
  <data name="App_xaml_Ui_thread" xml:space="preserve">
    <value>UI luồng:</value>
  </data>
  <data name="App_xaml_Ui_thread_exception" xml:space="preserve">
    <value>Lỗi UI luồng:</value>
  </data>
  <data name="App_xaml_Ui_thread_fatal_error" xml:space="preserve">
    <value>Lỗi chết mạng nghiêm trọng xảy ra trong luồng UI!</value>
  </data>
  <data name="App_xaml_Non_ui_thread_fatal_error" xml:space="preserve">
    <value>Lỗi chết mạng nghiêm trọng xảy ra ngoài luồng UI</value>
  </data>
  <data name="App_xaml_Non_ui_thread_exception" xml:space="preserve">
    <value>Lỗi ngoại lệ của luồng không phải UI:</value>
  </data>
  <data name="App_xaml_Task_thread" xml:space="preserve">
    <value>Luồng Task:</value>
  </data>
  <data name="App_xaml_Task_thread_exception" xml:space="preserve">
    <value>Lỗi ngoại lệ của luồng Task:</value>
  </data>
  <data name="DesignerHelper_Main_thread" xml:space="preserve">
    <value>Luồng chính</value>
  </data>
  <data name="ImageAttached_Switch" xml:space="preserve">
    <value>B flipped</value>
  </data>
  <data name="PermissionHelper_No_permission_operation" xml:space="preserve">
    <value>Bạn không có quyền thao tác này</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_enable_status" xml:space="preserve">
    <value>Trạng thái kích hoạt trục đơn</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_running_status" xml:space="preserve">
    <value>Trạng thái hoạt động trục đơn</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_alarm_status" xml:space="preserve">
    <value>Trạng thái cảnh báo trục đơn</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_error_status" xml:space="preserve">
    <value>Trạng thái lỗi trục đơn</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_left_collision" xml:space="preserve">
    <value>Đụng chạm trái trục đơn</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_right_collision" xml:space="preserve">
    <value>Đụng chạm phải trục đơn</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_positive_limit" xml:space="preserve">
    <value>Giới hạn dương trục đơn</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_negative_limit" xml:space="preserve">
    <value>Giới hạn âm trục đơn</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_on_workstation" xml:space="preserve">
    <value>Trục đơn nằm trên vị trí công</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_reached_target" xml:space="preserve">
    <value>Trục đơn đã đạt vị trí mục tiêu</value>
  </data>
  <data name="SysFeedBackMapping_System_ready" xml:space="preserve">
    <value>Hệ thống đã sẵn sàng</value>
  </data>
  <data name="SysFeedBackMapping_System_enable_status" xml:space="preserve">
    <value>Trạng thái kích hoạt hệ thống</value>
  </data>
  <data name="SysFeedBackMapping_System_error_status" xml:space="preserve">
    <value>Trạng thái lỗi hệ thống</value>
  </data>
  <data name="SysFeedBackMapping_System_running_status" xml:space="preserve">
    <value>Trạng thái hoạt động hệ thống</value>
  </data>
  <data name="SysFeedBackMapping_System_bus_status" xml:space="preserve">
    <value>Trạng thái busses hệ thống</value>
  </data>
  <data name="SysFeedBackMapping_System_platform_verification" xml:space="preserve">
    <value>Trạng thái kiểm tra đế平台 hệ thống</value>
  </data>
  <data name="SysFeedBackMapping_Axis_config_completed" xml:space="preserve">
    <value>Hoàn thành cấu hình trục, có thể khởi tạo dãy trục</value>
  </data>
  <data name="SysFeedBackMapping_Motion_param_config_completed" xml:space="preserve">
    <value>Hoàn thành cấu hình tham số di chuyển, có thể khôi phục trạng thái cũ của hệ thống</value>
  </data>
  <data name="SysFeedBackMapping_System_state_restored" xml:space="preserve">
    <value>Hoàn thành khôi phục trạng thái cũ của hệ thống</value>
  </data>
  <data name="SysFeedBackMapping_Bit8_31_reserved" xml:space="preserve">
    <value>bit8-31: Để trống\n</value>
  </data>
  <data name="SqlsugarSetup_Sql_statement" xml:space="preserve">
    <value>【Câu lệnh SQL】:</value>
  </data>
  <data name="SqlsugarSetup_Sql_parameters" xml:space="preserve">
    <value>【Tham số SQL】:</value>
  </data>
  <data name="InputConverter_Input_value_range" xml:space="preserve">
    <value>Giá trị nhập phải nằm trong phạm vi chỉ định</value>
  </data>
  <data name="BasePermAssignViewModel_Root_node" xml:space="preserve">
    <value>Nút gốc</value>
  </data>
  <data name="BasePermAssignViewModel_Get_success" xml:space="preserve">
    <value>Lấy được thành công</value>
  </data>
  <data name="BasePermissionViewModel_Root_node" xml:space="preserve">
    <value>Nút gốc</value>
  </data>
  <data name="BasePermissionViewModel_Get_success" xml:space="preserve">
    <value>Lấy được thành công</value>
  </data>
  <data name="BasePermissionViewModel_Add_success" xml:space="preserve">
    <value>Thêm thành công</value>
  </data>
  <data name="BasePermissionViewModel_Update_success" xml:space="preserve">
    <value>Cập nhật thành công</value>
  </data>
  <data name="BasePermissionViewModel_Delete_success" xml:space="preserve">
    <value>Xóa thành công</value>
  </data>
  <data name="BaseUserViewModel_Get_success" xml:space="preserve">
    <value>Lấy được thành công</value>
  </data>
  <data name="BaseUserViewModel_Add_success" xml:space="preserve">
    <value>Thêm thành công</value>
  </data>
  <data name="BaseUserViewModel_Update_success" xml:space="preserve">
    <value>Cập nhật thành công</value>
  </data>
  <data name="BaseUserViewModel_Delete_success" xml:space="preserve">
    <value>Xóa thành công</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_forward" xml:space="preserve">
    <value>Di chuyển Jog sang phía trước</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_reverse" xml:space="preserve">
    <value>Di chuyển Jog sang phía sau</value>
  </data>
  <data name="ControlerAxisViewModel_Absolute_movement" xml:space="preserve">
    <value>Di chuyển tuyệt đối</value>
  </data>
  <data name="ControlerAxisViewModel_Relative_movement" xml:space="preserve">
    <value>Di chuyển tương đối</value>
  </data>
  <data name="ControlerAxisViewModel_Workstation_movement" xml:space="preserve">
    <value>Di chuyển vị trí công</value>
  </data>
  <data name="ControlerAxisViewModel_Set_zero_point" xml:space="preserve">
    <value>Thiết lập điểm zero</value>
  </data>
  <data name="ControlerAxisViewModel_Axis_reset" xml:space="preserve">
    <value>Đặt lại trục</value>
  </data>
  <data name="ControlerGenerateConfigViewModel_Config_file_generated" xml:space="preserve">
    <value>Tạo file cấu hình thành công</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Workstation_config_distributed" xml:space="preserve">
    <value>Gửi cấu hình vị trí công thành công</value>
  </data>
  <data name="ControlerTranStatusViewModel_Do_nothing" xml:space="preserve">
    <value>Không xử lý</value>
  </data>
  <data name="DataViewModel_Controller_disconnected" xml:space="preserve">
    <value>Kết nối ctrl bị tắt!</value>
  </data>
  <data name="DataViewModel_Controller_connected" xml:space="preserve">
    <value>Kết nối ctrl thành công!</value>
  </data>
  <data name="MainViewModel_Controller_feedback_zero" xml:space="preserve">
    <value>Ctrl feedback số trục là 0, không thể thực hiện thao tác này!</value>
  </data>
  <data name="ServoSettingViewModel_No_control" xml:space="preserve">
    <value>Không kiểm soát</value>
  </data>
  <data name="ServoSettingViewModel_Dual_axis_position_control" xml:space="preserve">
    <value>Kiểm soát vị trí trục kép</value>
  </data>
  <data name="ServoSettingViewModel_Axis0_electrical_angle" xml:space="preserve">
    <value>Phân tích góc điện trục 0</value>
  </data>
  <data name="ServoSettingViewModel_Dc_sampling_test" xml:space="preserve">
    <value>Thử nghiệm lấy mẫu tia direct</value>
  </data>
  <data name="ServoSettingViewModel_Ac_sampling_test" xml:space="preserve">
    <value>Thử nghiệm lấy mẫu tia trao đổi</value>
  </data>
  <data name="ScopeView_xaml_Csv_file_filter" xml:space="preserve">
    <value>Tệp CSV (*.csv)|*.csv|Tất cả tệp (*.*)|*.*</value>
  </data>
  <data name="ScopeView_xaml_Select_csv_file" xml:space="preserve">
    <value>Vui lòng chọn một tệp CSV</value>
  </data>
  <data name="ScopeView_xaml_Select_save_path" xml:space="preserve">
    <value>Vui lòng chọn đường dẫn lưu</value>
  </data>
  <data name="ScopeView_xaml_Data_export_success" xml:space="preserve">
    <value>Xuất dữ liệu thành công</value>
  </data>
  <data name="ObjectUtil_Object_not_empty" xml:space="preserve">
    <value>Đối tượng nhập không thể rỗng!</value>
  </data>
  <data name="FileHelper_Newly_appended_content" xml:space="preserve">
    <value>Nội dung bổ sung mới</value>
  </data>
  <data name="FileHelper_What_i_wrote" xml:space="preserve">
    <value>Đây là nội dung tôi viết ah</value>
  </data>
  <data name="FileHelper_Directory_not_exist" xml:space="preserve">
    <value>Không có thư mục tương ứng</value>
  </data>
  <data name="RecursionHelper_Button" xml:space="preserve">
    <value>Nút b flipped</value>
  </data>
  <data name="ControlerTcpClient_Send_data" xml:space="preserve">
    <value>Gửi dữ liệu:</value>
  </data>
  <data name="ControlerTcpClient_Adapter_parsing_failed" xml:space="preserve">
    <value>Lỗi phân tích dữ liệu của adapter!</value>
  </data>
  <data name="ControlerTcpClient_Controller_not_connected" xml:space="preserve">
    <value>Ctrl chưa kết nối!</value>
  </data>
  <data name="ControlerTcpClient_Controller_heartbeat_failed" xml:space="preserve">
    <value>Gửi tín hiệu tim心跳 ctrl thất bại</value>
  </data>
  <data name="ControllerConst_Upper_enable" xml:space="preserve">
    <value>Điều chỉnh enabling trên</value>
  </data>
  <data name="ControllerConst_Lower_enable" xml:space="preserve">
    <value>Điều chỉnh enabling dưới</value>
  </data>
  <data name="ControllerConst_Stop" xml:space="preserve">
    <value>Dừng</value>
  </data>
  <data name="ControllerConst_Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="ControllerConst_Set_zero_point" xml:space="preserve">
    <value>Thiết lập điểm zero</value>
  </data>
  <data name="ControllerConst_Forward_jog" xml:space="preserve">
    <value>Di chuyển một chút sang trước</value>
  </data>
  <data name="ControllerConst_Backward_jog" xml:space="preserve">
    <value>Di chuyển một chút sang sau</value>
  </data>
  <data name="ControllerConst_Absolute_movement" xml:space="preserve">
    <value>Di chuyển tuyệt đối</value>
  </data>
  <data name="ControllerConst_Relative_movement" xml:space="preserve">
    <value>Di chuyển tương đối</value>
  </data>
  <data name="ControllerConst_Workstation_movement" xml:space="preserve">
    <value>Di chuyển vị trí công</value>
  </data>
  <data name="SysCtrlCmdEnum_Upper_enable" xml:space="preserve">
    <value>Điều chỉnh enabling trên</value>
  </data>
  <data name="SysCtrlCmdEnum_Lower_enable" xml:space="preserve">
    <value>Điều chỉnh enabling dưới</value>
  </data>
  <data name="SysCtrlCmdEnum_Error_reset" xml:space="preserve">
    <value>Lỗi reset</value>
  </data>
  <data name="SysCtrlCmdEnum_Run" xml:space="preserve">
    <value>Chạy</value>
  </data>
  <data name="SysCtrlCmdEnum_Pause" xml:space="preserve">
    <value>Tạm dừng</value>
  </data>
  <data name="SysCtrlCmdEnum_Emergency_stop" xml:space="preserve">
    <value>Dừng cấp cứu緊急</value>
  </data>
  <data name="AxisCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>Đối tượng kiểm soát đã bị xóa từ giao thức, vui lòng không sử dụng thuộc tính này</value>
  </data>
  <data name="SysCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>Đối tượng kiểm soát đã bị xóa từ giao thức, vui lòng không sử dụng thuộc tính này</value>
  </data>
  <data name="ScopeConst_Position_parameter" xml:space="preserve">
    <value>Tham số vị trí</value>
  </data>
  <data name="ScopeConst_Axis0_position_feedback" xml:space="preserve">
    <value>Ph反馈osition trục 0</value>
  </data>
  <data name="ScopeConst_Axis1_position_feedback" xml:space="preserve">
    <value>Ph反馈osition trục 1</value>
  </data>
  <data name="ScopeConst_Speed_parameter" xml:space="preserve">
    <value>Tham số vận tốc</value>
  </data>
  <data name="ScopeConst_Axis0_speed_instruction" xml:space="preserve">
    <value>G指令Velocity trục 0</value>
  </data>
  <data name="ScopeConst_Axis0_speed_feedback" xml:space="preserve">
    <value>Ph反馈osition vận tốc trục 0</value>
  </data>
  <data name="ScopeConst_Axis1_speed_instruction" xml:space="preserve">
    <value>G指令Velocity trục 1</value>
  </data>
  <data name="ScopeConst_Axis1_speed_feedback" xml:space="preserve">
    <value>Ph反馈osition vận tốc trục 1</value>
  </data>
  <data name="ScopeConst_Current_parameter" xml:space="preserve">
    <value>Tham số dòng điện</value>
  </data>
  <data name="ScopeConst_Axis0_current_instruction" xml:space="preserve">
    <value>G指令Current trục 0</value>
  </data>
  <data name="ScopeConst_Axis0_current_feedback" xml:space="preserve">
    <value>Ph反馈osition dòng điện trục 0</value>
  </data>
  <data name="ScopeConst_Axis1_current_instruction" xml:space="preserve">
    <value>G指令Current trục 1</value>
  </data>
  <data name="ScopeConst_Axis1_current_feedback" xml:space="preserve">
    <value>Ph反馈osition dòng điện trục 1</value>
  </data>
  <data name="ScopeConst_Voltage_parameter" xml:space="preserve">
    <value>Tham số điện áp</value>
  </data>
  <data name="ScopeConst_Axis0_d_axis_voltage" xml:space="preserve">
    <value>G reference voltage D trục 0</value>
  </data>
  <data name="ScopeConst_Axis1_d_axis_voltage" xml:space="preserve">
    <value>G reference voltage D trục 1</value>
  </data>
  <data name="ScopeConst_Axis0_q_axis_voltage" xml:space="preserve">
    <value>G reference voltage Q trục 0</value>
  </data>
  <data name="ScopeConst_Axis1_q_axis_voltage" xml:space="preserve">
    <value>G reference voltage Q trục 1</value>
  </data>
  <data name="ScopeConst_Axis0_bus_voltage" xml:space="preserve">
    <value>G母线Voltage trục 0</value>
  </data>
  <data name="ScopeConst_Axis1_bus_voltage" xml:space="preserve">
    <value>G母线Voltage trục 1</value>
  </data>
  <data name="ScopeConst_Axis0_u_phase_current" xml:space="preserve">
    <value>U相Current trục 0</value>
  </data>
  <data name="ScopeConst_Axis1_u_phase_current" xml:space="preserve">
    <value>U相Current trục 1</value>
  </data>
  <data name="ScopeConst_Axis0_v_phase_current" xml:space="preserve">
    <value>V相Current trục 0</value>
  </data>
  <data name="ScopeConst_Axis1_v_phase_current" xml:space="preserve">
    <value>V相Current trục 1</value>
  </data>
  <data name="ScopeConst_Axis0_w_phase_current" xml:space="preserve">
    <value>W相Current trục 0</value>
  </data>
  <data name="ScopeConst_Axis1_w_phase_current" xml:space="preserve">
    <value>W相Current trục 1</value>
  </data>
  <data name="ScopeConst_Axis0_control_voltage" xml:space="preserve">
    <value>G控制Voltage trục 0</value>
  </data>
  <data name="ScopeConst_Axis1_control_voltage" xml:space="preserve">
    <value>G控制Voltage trục 1</value>
  </data>
  <data name="ServoContext_Motor_parameter" xml:space="preserve">
    <value>1-参數电机</value>
  </data>
  <data name="ServoContext_System_parameter" xml:space="preserve">
    <value>2-参數系统</value>
  </data>
  <data name="ServoContext_Encoder_parameter" xml:space="preserve">
    <value>3-参數编码器</value>
  </data>
  <data name="ServoContext_Protection_parameter" xml:space="preserve">
    <value>4-参數保护</value>
  </data>
  <data name="ServoContext_Fault_record" xml:space="preserve">
    <value>5-故障记录</value>
  </data>
  <data name="ServoContext_Control_status" xml:space="preserve">
    <value>6-控制状态</value>
  </data>
  <data name="ServoContext_Position_parameter" xml:space="preserve">
    <value>7-位置参数</value>
  </data>
  <data name="ServoContext_Speed_parameter" xml:space="preserve">
    <value>8-速度参数</value>
  </data>
  <data name="ServoContext_Torque_parameter" xml:space="preserve">
    <value>9-转矩参数</value>
  </data>
  <data name="ServoContext_Get_from_drive_context_exception" xml:space="preserve">
    <value>GetFromDriveContext lỗi</value>
  </data>
  <data name="ServoSerialPortClient_Servo_heartbeat_failed" xml:space="preserve">
    <value>Gửi tín hiệu tim心跳 ctrl thất bại</value>
  </data>
  <data name="ElectricParaPackage_Third_instruction_not_exist" xml:space="preserve">
    <value>Thứ ba lệnh không tồn tại</value>
  </data>
  <data name="RoleDto_Role_name_not_empty" xml:space="preserve">
    <value>Tên vai trò không thể rỗng</value>
  </data>
  <data name="ParameterModel_Input_value_exceed_limit" xml:space="preserve">
    <value>Giá trị nhập vượt quá giới hạn!</value>
  </data>
  <data name="ParameterModel_Input_value_incorrect" xml:space="preserve">
    <value>Giá trị nhập sai!</value>
  </data>
  <data name="LineConfigEnum_System" xml:space="preserve">
    <value>Hệ thống</value>
  </data>
  <data name="LineConfigEnum_Motor" xml:space="preserve">
    <value>Điện máy</value>
  </data>
  <data name="LineConfigEnum_Slave_node" xml:space="preserve">
    <value>Nút从站</value>
  </data>
  <data name="LineConfigEnum_Line" xml:space="preserve">
    <value>Dây chuyền</value>
  </data>
  <data name="LineConfigEnum_Workstation" xml:space="preserve">
    <value>Vị trí công</value>
  </data>
  <data name="LineConfigEnum_Axis" xml:space="preserve">
    <value>Trục</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence" xml:space="preserve">
    <value>Dãy trục</value>
  </data>
  <data name="LineConfigEnum_Axis_pid" xml:space="preserve">
    <value>PID trục</value>
  </data>
  <data name="LineConfigEnum_Axis_offset" xml:space="preserve">
    <value>Offset trục</value>
  </data>
  <data name="LineConfigEnum_Device_wiring_direction" xml:space="preserve">
    <value>Hướng接线 thiết bị</value>
  </data>
  <data name="LineConfigEnum_Workstation_offset" xml:space="preserve">
    <value>Offset vị trí công</value>
  </data>
  <data name="LineConfigEnum_Ui_view" xml:space="preserve">
    <value>Hình quan UI</value>
  </data>
  <data name="LineConfigEnum_Configuration_parameter" xml:space="preserve">
    <value>Cấu hình参數</value>
  </data>
  <data name="LineConfigEnum_System_configuration_parameter" xml:space="preserve">
    <value>Cấu hình参數 hệ thống</value>
  </data>
  <data name="LineConfigEnum_Motor_configuration_parameter" xml:space="preserve">
    <value>Cấu hình参數电机</value>
  </data>
  <data name="LineConfigEnum_Slave_node_configuration_parameter" xml:space="preserve">
    <value>Cấu hình参數从站</value>
  </data>
  <data name="LineConfigEnum_Line_segment_configuration_parameter" xml:space="preserve">
    <value>Cấu hình参數 dãy chuyền</value>
  </data>
  <data name="LineConfigEnum_Workstation_running_configuration_parameter" xml:space="preserve">
    <value>Cấu hình参數运行 vị trí công</value>
  </data>
  <data name="LineConfigEnum_Rotor_configuration_parameter" xml:space="preserve">
    <value>Cấu hình参數 động tử</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence_configuration_parameter" xml:space="preserve">
    <value>Cấu hình参數 dãy trục</value>
  </data>
  <data name="LineConfigEnum_Axis_running_pid_configuration_parameter" xml:space="preserve">
    <value>Cấu hình参數运行 PID trục</value>
  </data>
  <data name="LineConfigEnum_Rotor_compensation_configuration_parameter" xml:space="preserve">
    <value>Cấu hình参數补偿 động tử</value>
  </data>
  <data name="LineConfigEnum_Line_wiring_direction_configuration_parameter" xml:space="preserve">
    <value>Cấu hình参數接线 hướng dãy chuyền</value>
  </data>
  <data name="LineConfigEnum_Workstation_compensation_configuration_parameter" xml:space="preserve">
    <value>Cấu hình参數补偿 vị trí công</value>
  </data>
  <data name="LineConfigEnum_Line_view_configuration_parameter" xml:space="preserve">
    <value>Cấu hình参數接线 hướng dãy chuyền</value>
  </data>
  <data name="ParamTableEnum_Motor_parameter" xml:space="preserve">
    <value>1-参數电机</value>
  </data>
  <data name="ParamTableEnum_System_parameter" xml:space="preserve">
    <value>2-参數系统</value>
  </data>
  <data name="ParamTableEnum_Encoder_parameter" xml:space="preserve">
    <value>3-参數编码器</value>
  </data>
  <data name="ParamTableEnum_Protection_parameter" xml:space="preserve">
    <value>4-参數保护</value>
  </data>
  <data name="ParamTableEnum_Fault_record" xml:space="preserve">
    <value>5-故障记录</value>
  </data>
  <data name="ParamTableEnum_Control_status" xml:space="preserve">
    <value>6-控制状态</value>
  </data>
  <data name="ParamTableEnum_Position_parameter" xml:space="preserve">
    <value>7-位置参数</value>
  </data>
  <data name="ParamTableEnum_Speed_parameter" xml:space="preserve">
    <value>8-速度参数</value>
  </data>
  <data name="ParamTableEnum_Torque_parameter" xml:space="preserve">
    <value>9-转矩参数</value>
  </data>
  <data name="ParameterModelExtension_Parameter_model_extension_exception" xml:space="preserve">
    <value>ParameterModelExtension lỗi</value>
  </data>
  <data name="LocalizationManager_Simplified_chinese" xml:space="preserve">
    <value>Tiếng Trung giản thể</value>
  </data>
  <data name="LocalizationManager_Traditional_chinese" xml:space="preserve">
    <value>Tiếng Trung phồn thể</value>
  </data>
  <data name="LocalizationManager_Japanese" xml:space="preserve">
    <value>Nihongo</value>
  </data>
  <data name="OnlineConfigService_Unknown" xml:space="preserve">
    <value>Không biết</value>
  </data>
  <data name="OnlineConfigService_No_description" xml:space="preserve">
    <value>Không có mô tả</value>
  </data>
  <data name="OnlineConfigService_No_value" xml:space="preserve">
    <value>Không có giá trị</value>
  </data>
  <data name="SerialCore_Remote_terminal_closed" xml:space="preserve">
    <value>Terminal từ xa đã đóng</value>
  </data>
  <data name="SerialCore_New_serial_port_connected" xml:space="preserve">
    <value>Mới SerialPort phải ở trạng thái kết nối.</value>
  </data>
  <data name="SerialPortClient_Data_processing_error" xml:space="preserve">
    <value>Lỗi xảy ra trong quá trình xử lý dữ liệu</value>
  </data>
  <data name="SerialPortClient_Config_file_not_empty" xml:space="preserve">
    <value>File cấu hình không thể rỗng.</value>
  </data>
  <data name="SerialPortClient_Serial_port_config_not_empty" xml:space="preserve">
    <value>File cấu hình串口 không thể rỗng.</value>
  </data>
  <data name="SerialPortClient_Adapter_not_support_send" xml:space="preserve">
    <value>Adapter hiện tại không hỗ trợ gửi đối tượng.</value>
  </data>
  <data name="ControlerOnlineConfig_View_configuration" xml:space="preserve">
    <value>Cấu hình Thể hiện</value>
  </data>
  <data name="ControlerOnlineConfig_Motor_configuration" xml:space="preserve">
    <value>Cấu hình Động cơ</value>
  </data>
  <data name="ControlerOnlineConfig_Slave_node" xml:space="preserve">
    <value>Đọc Nút Bant</value>
  </data>
  <data name="ControlerOnlineConfig_Line_body_configuration" xml:space="preserve">
    <value>Cấu hình Dòng sản xuất</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_operation_configuration" xml:space="preserve">
    <value>Cấu hình Chạy Trạm sản xuất</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_configuration" xml:space="preserve">
    <value>Cấu hình Trục</value>
  </data>
  <data name="ControlerOnlineConfig_Sequence_configuration" xml:space="preserve">
    <value>Cấu hình Thứ tự</value>
  </data>
  <data name="ControlerOnlineConfig_Pid_configuration" xml:space="preserve">
    <value>Cấu hình PID</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_compensation_configuration" xml:space="preserve">
    <value>Cấu hình Bổ sung Trục</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_compensation_configuration" xml:space="preserve">
    <value>Cấu hình Bổ sung Trạm sản xuất</value>
  </data>
  <data name="ControlerOnlineConfig_Upload_to_controller_with_one_click" xml:space="preserve">
    <value>Tải lên bộ điều khiển một cú click</value>
  </data>
  <data name="ControlerOnlineConfig_Download_to_local_with_one_click" xml:space="preserve">
    <value>Tải xuống cục bộ một cú click</value>
  </data>
  <data name="ControlerOnlineConfig_Load_configuration" xml:space="preserve">
    <value>Tải cấu hình</value>
  </data>
  <data name="ControlerOnlineConfig_Save_configuration_as" xml:space="preserve">
    <value>Lưu cấu hình khác</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Send_success" xml:space="preserve">
    <value>Gửi thành công</value>
  </data>
  <data name="RoleDto_Role_name_cannot_be_empty" xml:space="preserve">
    <value>Tên vai trò không thể để trống</value>
  </data>
  <data name="Main_Online_demonstration" xml:space="preserve">
    <value>Demo trực tuyến</value>
  </data>
  <data name="Main_Alarm" xml:space="preserve">
    <value>Cảnh báo</value>
  </data>
  <data name="NoticeListControl_Feedback_information" xml:space="preserve">
    <value>Thông tin phản hồi</value>
  </data>
  <data name="NoticeListControl_Clear_all_notifications" xml:space="preserve">
    <value>Xóa tất cả các thông báo</value>
  </data>
  <data name="NoticeListControl_Type" xml:space="preserve">
    <value>Loại</value>
  </data>
  <data name="NoticeListControl_Source" xml:space="preserve">
    <value>Nguồn</value>
  </data>
  <data name="NoticeListControl_Message_content" xml:space="preserve">
    <value>Nội dung tin nhắn</value>
  </data>
  <data name="ControlerClient_Global_data_reset" xml:space="preserve">
    <value>Đặt lại dữ liệu toàn cục</value>
  </data>
  <data name="ControlerClient_Platform_verification" xml:space="preserve">
    <value>Kiểm tra nền tảng</value>
  </data>
  <data name="ControlerClient_System_parameter_configuration_initialization" xml:space="preserve">
    <value>Khởi tạo cấu hình tham số hệ thống</value>
  </data>
  <data name="ControlerClient_Slave_station_information_acquisition" xml:space="preserve">
    <value>Lấy thông tin từ trạm phụ</value>
  </data>
  <data name="ControlerClient_Mapping_of_slave_station_address_to_control_address" xml:space="preserve">
    <value>Áp xạ địa chỉ trạm phụ thành địa chỉ điều khiển</value>
  </data>
  <data name="ControlerClient_Master_slave_station_status_verification" xml:space="preserve">
    <value>Kiểm tra trạng thái trạm chủ - trạm phụ</value>
  </data>
  <data name="ControlerClient_Completion_of_status_initialization_of_bus_system_etc" xml:space="preserve">
    <value>Hoàn thành khởi tạo trạng thái của bus - hệ thống, v.v.</value>
  </data>
  <data name="ControlerClient_Initialization_of_movement_related_parameters" xml:space="preserve">
    <value>Khởi tạo các tham số liên quan đến chuyển động</value>
  </data>
  <data name="ControlerClient_Successful_initialization_of_magnetic_drive" xml:space="preserve">
    <value>Khởi tạo ổ đĩa từ thành công</value>
  </data>
  <data name="ControlerSys_System_drive_error" xml:space="preserve">
    <value>Lỗi điều khiển hệ thống</value>
  </data>
  <data name="FtpClient_Host" xml:space="preserve">
    <value>Máy chủ:</value>
  </data>
  <data name="FtpClient_Port" xml:space="preserve">
    <value>Cổng:</value>
  </data>
  <data name="FtpClient_Username" xml:space="preserve">
    <value>Tên người dùng:</value>
  </data>
  <data name="FtpClient_Password" xml:space="preserve">
    <value>Mật khẩu:</value>
  </data>
  <data name="FtpClient_Connect" xml:space="preserve">
    <value>Kết nối</value>
  </data>
  <data name="FtpClient_Disconnect" xml:space="preserve">
    <value>Ngắt kết nối</value>
  </data>
  <data name="FtpClient_Remote_directory" xml:space="preserve">
    <value>Thư mục từ xa: </value>
  </data>
  <data name="FtpClient_Back" xml:space="preserve">
    <value>Quay lại</value>
  </data>
  <data name="FtpClient_Forward" xml:space="preserve">
    <value>Tiếp tục</value>
  </data>
  <data name="FtpClient_Up" xml:space="preserve">
    <value>Lên trên</value>
  </data>
  <data name="FtpClient_Refresh" xml:space="preserve">
    <value>Làm mới</value>
  </data>
  <data name="FtpClient_Create_folder" xml:space="preserve">
    <value>Tạo thư mục</value>
  </data>
  <data name="FtpClient_Delete" xml:space="preserve">
    <value>Xóa</value>
  </data>
  <data name="FtpClient_Download_to_local" xml:space="preserve">
    <value>Tải xuống cục bộ</value>
  </data>
  <data name="FtpClient_Local_directory" xml:space="preserve">
    <value>Thư mục cục bộ: </value>
  </data>
  <data name="FtpClient_Upload_to_server" xml:space="preserve">
    <value>Tải lên máy chủ</value>
  </data>
  <data name="FtpClient_Transmission_log" xml:space="preserve">
    <value>Lịch sử truyền tải:</value>
  </data>
  <data name="ServoSetting_System_soft_reset" xml:space="preserve">
    <value>Đặt lại phần mềm hệ thống</value>
  </data>
  <data name="FtpClientViewModel_Connecting_to_ftp_server" xml:space="preserve">
    <value>Đang kết nối đến máy chủ FTP...</value>
  </data>
  <data name="FtpClientViewModel_Connected_to_ftp_server" xml:space="preserve">
    <value>Đã kết nối đến máy chủ FTP</value>
  </data>
  <data name="FtpClientViewModel_Connect" xml:space="preserve">
    <value>Kết nối</value>
  </data>
  <data name="FtpClientViewModel_Disconnected" xml:space="preserve">
    <value>Đã ngắt kết nối</value>
  </data>
  <data name="FtpClientViewModel_Disconnect" xml:space="preserve">
    <value>Ngắt kết nối</value>
  </data>
  <data name="FtpClientViewModel_Loading_remote_directory" xml:space="preserve">
    <value>Đang tải thư mục từ xa: </value>
  </data>
  <data name="FtpClientViewModel_Remote_directory_loaded" xml:space="preserve">
    <value>Đã tải thư mục từ xa: </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_remote_directory" xml:space="preserve">
    <value>Không thể tải thư mục từ xa: </value>
  </data>
  <data name="FtpClientViewModel_Browse" xml:space="preserve">
    <value>Duyệt</value>
  </data>
  <data name="FtpClientViewModel_Loading_local_directory" xml:space="preserve">
    <value>Đang tải thư mục cục bộ: </value>
  </data>
  <data name="FtpClientViewModel_Local_directory_loaded" xml:space="preserve">
    <value>Đã tải thư mục cục bộ: </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_local_directory" xml:space="preserve">
    <value>Không thể tải thư mục cục bộ: </value>
  </data>
  <data name="FtpClientViewModel_Downloading" xml:space="preserve">
    <value>Đang tải xuống: </value>
  </data>
  <data name="FtpClientViewModel_Download_completed" xml:space="preserve">
    <value>Đã tải xuống: </value>
  </data>
  <data name="FtpClientViewModel_Download" xml:space="preserve">
    <value>Tải xuống</value>
  </data>
  <data name="FtpClientViewModel_Download_failed" xml:space="preserve">
    <value>Tải xuống thất bại: </value>
  </data>
  <data name="FtpClientViewModel_Uploading" xml:space="preserve">
    <value>Đang tải lên: </value>
  </data>
  <data name="FtpClientViewModel_Upload_completed" xml:space="preserve">
    <value>Đã tải lên: </value>
  </data>
  <data name="FtpClientViewModel_Upload" xml:space="preserve">
    <value>Tải lên</value>
  </data>
  <data name="FtpClientViewModel_Upload_failed" xml:space="preserve">
    <value>Tải lên thất bại: </value>
  </data>
  <data name="FtpClientViewModel_Directory_created" xml:space="preserve">
    <value>Đã tạo thư mục: </value>
  </data>
  <data name="FtpClientViewModel_Create_directory" xml:space="preserve">
    <value>Tạo thư mục</value>
  </data>
  <data name="FtpClientViewModel_Failed_to_create_directory" xml:space="preserve">
    <value>Không thể tạo thư mục: </value>
  </data>
  <data name="FtpClientViewModel_Directory_deleted" xml:space="preserve">
    <value>Đã xóa thư mục: </value>
  </data>
  <data name="FtpClientViewModel_Delete" xml:space="preserve">
    <value>Xóa</value>
  </data>
  <data name="FtpClientViewModel_File_deleted" xml:space="preserve">
    <value>Đã xóa tệp: </value>
  </data>
  <data name="FtpClientViewModel_Open" xml:space="preserve">
    <value>Mở</value>
  </data>
  <data name="ControllerHelper_System_is_running" xml:space="preserve">
    <value>Hệ thống đang chạy</value>
  </data>
  <data name="ControllerHelper_System_is_ready" xml:space="preserve">
    <value>Hệ thống đã sẵn sàng</value>
  </data>
  <data name="ControllerHelper_System_is_enabled" xml:space="preserve">
    <value>Hệ thống đã được kích hoạt</value>
  </data>
  <data name="ControllerHelper_System_bus_is_connected" xml:space="preserve">
    <value>Bus của hệ thống đã được kết nối</value>
  </data>
  <data name="ControllerHelper_System_is_in_error_state" xml:space="preserve">
    <value>Hệ thống đang處於狀態 lỗi</value>
  </data>
  <data name="ControllerHelper_Axis_driver_error" xml:space="preserve">
    <value>Lỗi điều khiển trục</value>
  </data>
  <data name="ControllerHelper_Axis_movement_error" xml:space="preserve">
    <value>Lỗi chuyển động trục</value>
  </data>
  <data name="ControllerHelper_Axis_error_status" xml:space="preserve">
    <value>Trạng thái lỗi trục</value>
  </data>
  <data name="ControllerHelper_Axis_alarm" xml:space="preserve">
    <value>Cảnh báo trục</value>
  </data>
  <data name="ControllerHelper_Positive_limit_of_axis" xml:space="preserve">
    <value>Giới hạn dương của trục</value>
  </data>
  <data name="ControllerHelper_Negative_limit_of_axis" xml:space="preserve">
    <value>Giới hạn âm của trục</value>
  </data>
  <data name="ControllerHelper_Axis_warning" xml:space="preserve">
    <value>Cảnh báo trục</value>
  </data>
  <data name="ControllerHelper_Axis_in_left_position" xml:space="preserve">
    <value>Trục đã đến vị trí bên trái</value>
  </data>
  <data name="ControllerHelper_Axis_in_right_position" xml:space="preserve">
    <value>Trục đã đến vị trí bên phải</value>
  </data>
  <data name="ControllerHelper_Axis_has_reached_the_target_position" xml:space="preserve">
    <value>Trục đã đến vị trí mục tiêu</value>
  </data>
  <data name="ControllerHelper_Axis_is_at_the_workstation" xml:space="preserve">
    <value>Trục đang ở trên các thiết bị làm việc</value>
  </data>
  <data name="ControllerHelper_Axis_notification" xml:space="preserve">
    <value>Thông báo trục</value>
  </data>
  <data name="ControllerHelper_Axis_is_running" xml:space="preserve">
    <value>Trục đang chạy</value>
  </data>
  <data name="ControllerHelper_Axis_is_enabled" xml:space="preserve">
    <value>Trục đã được kích hoạt</value>
  </data>
  <data name="ControllerHelper_Axis_status" xml:space="preserve">
    <value>Trạng thái trục</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_running" xml:space="preserve">
    <value>Trục quay đang chạy</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_homing_completed" xml:space="preserve">
    <value>Hoàn thành việc về vị trí ban đầu của trục quay</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_enabled" xml:space="preserve">
    <value>Trục quay đã được kích hoạt</value>
  </data>
  <data name="ControllerHelper_Rotary_axis" xml:space="preserve">
    <value>Trục quay</value>
  </data>
  <data name="ServoSerialPortClient_Driver_connected_successfully" xml:space="preserve">
    <value>Kết nối bộ điều khiển thành công!</value>
  </data>
  <data name="ServoSerialPortClient_Driver_disconnected" xml:space="preserve">
    <value>Ngắt kết nối bộ điều khiển!</value>
  </data>
  <data name="ServoSerialPortClient_Driver_parameter_recovery_successful" xml:space="preserve">
    <value>Phục hồi tham số bộ điều khiển thành công!</value>
  </data>
  <data name="Scope_Error_log" xml:space="preserve">
    <value>Ghi lại Lỗi</value>
  </data>
  <data name="Scope_Process_log" xml:space="preserve">
    <value>Ghi lại Quy trình</value>
  </data>
  <data name="Scope_Plc_signal" xml:space="preserve">
    <value>Tín hiệu PLC</value>
  </data>
  <data name="Scope_Plc_control_signal_count" xml:space="preserve">
    <value>Đếm Tín hiệu Kiểm soát PLC</value>
  </data>
  <data name="Scope_Acquire_count" xml:space="preserve">
    <value>Lấy Đếm số</value>
  </data>
  <data name="Scope_Axis_0" xml:space="preserve">
    <value>Trục 0:</value>
  </data>
  <data name="Scope_Brake" xml:space="preserve">
    <value>Phanh:</value>
  </data>
  <data name="Scope_Acceleration" xml:space="preserve">
    <value>Tăng tốc:</value>
  </data>
  <data name="Scope_Axis_1" xml:space="preserve">
    <value>Trục 1:</value>
  </data>
  <data name="ServoSetting_Cdm_driver_parameter_settings" xml:space="preserve">
    <value>Cài đặt Tham số Bộ điều khiển CDM:</value>
  </data>
  <data name="ServoSetting_Target_stop_position" xml:space="preserve">
    <value>Vị trí Dừng Mục tiêu:</value>
  </data>
  <data name="ServoSetting_Send" xml:space="preserve">
    <value>Gửi</value>
  </data>
  <data name="ServoSetting_Rotor_detection_position" xml:space="preserve">
    <value>Vị trí Phát hiện Con trượt:</value>
  </data>
  <data name="ServoSetting_Enable_motor_position" xml:space="preserve">
    <value>Vị trí Kích hoạt Động cơ:</value>
  </data>
  <data name="ServoSetting_Current_user_no_permission_modify_this_parameter" xml:space="preserve">
    <value>Người dùng hiện tại không có quyền sửa đổi tham số này</value>
  </data>
  <data name="ServoSettingViewModel_Csv_file_csv" xml:space="preserve">
    <value>Tệp CSV|*.csv</value>
  </data>
  <data name="ServoSettingViewModel_Select_import_file" xml:space="preserve">
    <value>Chọn Tệp Nhập</value>
  </data>
  <data name="ServoSettingViewModel_Select_export_path" xml:space="preserve">
    <value>Chọn Đường dẫn Xuất</value>
  </data>
  <data name="ServoSettingViewModel_Driver_parameters_exported_successfully" xml:space="preserve">
    <value>Xuất tham số Bộ điều khiển thành công</value>
  </data>
  <data name="ServoSettingViewModel_Invalid_parameters_exist_check_input_values_retry" xml:space="preserve">
    <value>Có tham số không hợp lệ, vui lòng kiểm tra giá trị nhập vào và thử lại</value>
  </data>
  <data name="ServoSettingViewModel_Invalid_parameters_exist_check_all_input_values_retry" xml:space="preserve">
    <value>Có tham số không hợp lệ, vui lòng kiểm tra tất cả giá trị nhập vào và thử lại</value>
  </data>
  <data name="ServoSettingViewModel_All_parameters_set_complete" xml:space="preserve">
    <value>Tất cả tham số đã cài đặt xong</value>
  </data>
  <data name="ScopeView_xaml_Serial_port_disconnected_oscilloscope_auto_stopped" xml:space="preserve">
    <value>Cổng nối tiếp bị ngắt, máy dao động đã tự động dừng</value>
  </data>
  <data name="JsonHelper_Object_to_serialize_cannot_be_null" xml:space="preserve">
    <value>Đối tượng cần tuần tự hóa không thể là null</value>
  </data>
  <data name="JsonHelper_File_path_cannot_be_empty" xml:space="preserve">
    <value>Đường dẫn tệp không thể trống</value>
  </data>
  <data name="JsonHelper_Target_type_cannot_be_null" xml:space="preserve">
    <value>Kiểu mục tiêu không thể là null</value>
  </data>
  <data name="AxisCtrlCmdPackage_Control_object_removed_from_protocol_do_not_use_this_property" xml:space="preserve">
    <value>Đối tượng kiểm soát đã được xóa khỏi giao thức, vui lòng không sử dụng thuộc tính này</value>
  </data>
  <data name="SysCtrlCmdPackage_Control_object_removed_from_protocol_do_not_use_this_property" xml:space="preserve">
    <value>Đối tượng kiểm soát đã được xóa khỏi giao thức, vui lòng không sử dụng thuộc tính này</value>
  </data>
  <data name="ServoSerialPortClient_Serial_port_device_not_supported_no_authorization_response" xml:space="preserve">
    <value>Thiết bị cổng nối tiếp không hỗ trợ, không nhận được phản hồi ủy quyền</value>
  </data>
  <data name="ServoSerialPortClient_Error_sending_authorization_packet" xml:space="preserve">
    <value>Lỗi xảy ra khi gửi gói ủy quyền</value>
  </data>
  <data name="ServoSerialPortClient_Serial_port_device_not_supported_authorization_failed" xml:space="preserve">
    <value>Thiết bị cổng nối tiếp không hỗ trợ, ủy quyền thất bại</value>
  </data>
  <data name="ParameterModel_Value_cannot_be_empty" xml:space="preserve">
    <value>Giá trị không thể trống</value>
  </data>
  <data name="ParameterModel_Input_value_cannot_contain_spaces_tabs" xml:space="preserve">
    <value>Giá trị nhập vào không thể chứa khoảng trắng hoặc tab</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_number_format" xml:space="preserve">
    <value>Vui lòng nhập định dạng số hợp lệ</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_16bit_unsigned_integer" xml:space="preserve">
    <value>Vui lòng nhập số nguyên không dấu 16 bit hợp lệ (0-65535)</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_32bit_unsigned_integer" xml:space="preserve">
    <value>Vui lòng nhập số nguyên không dấu 32 bit hợp lệ (0-4294967295)</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_16bit_signed_integer" xml:space="preserve">
    <value>Vui lòng nhập số nguyên có dấu 16 bit hợp lệ (-32768 đến 32767)</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_32bit_signed_integer" xml:space="preserve">
    <value>Vui lòng nhập số nguyên có dấu 32 bit hợp lệ (-2147483648 đến 2147483647)</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_floating_point_number" xml:space="preserve">
    <value>Vui lòng nhập số dấu phẩy động hợp lệ</value>
  </data>
  <data name="ParameterModel_Input_value_cannot_be_nan_or_infinity" xml:space="preserve">
    <value>Giá trị nhập vào không thể là NaN hoặc vô cùng</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_double_precision_floating_point_number" xml:space="preserve">
    <value>Vui lòng nhập số dấu phẩy động kép độ chính xác hợp lệ</value>
  </data>
  <data name="ParameterModel_Please_enter_valid_number" xml:space="preserve">
    <value>Vui lòng nhập số hợp lệ</value>
  </data>
  <data name="SerialCore_New_serialport_must_be_in_connected_state" xml:space="preserve">
    <value>SerialPort mới phải ở trạng thái kết nối.</value>
  </data>
  <data name="SerialPortClient_Error_processing_data" xml:space="preserve">
    <value>Lỗi xảy ra khi xử lý dữ liệu</value>
  </data>
  <data name="SerialPortClient_Configuration_file_cannot_be_empty" xml:space="preserve">
    <value>Tệp cấu hình không thể trống.</value>
  </data>
  <data name="SerialPortClient_Serial_port_configuration_cannot_be_empty" xml:space="preserve">
    <value>Cấu hình cổng nối tiếp không thể trống.</value>
  </data>
  <data name="SerialPortClient_Current_adapter_not_support_object_sending" xml:space="preserve">
    <value>Bộ điều hợp hiện tại không hỗ trợ gửi đối tượng.</value>
  </data>
</root>