using System;
using System.Text;
using System.Text.Json;

namespace ModularAT.Shared
{
    /// <summary>
    /// 配置帮助类
    /// </summary>
    public static class ConfigurationHelper
    {
        /// <summary>
        /// 从命令行参数解析启动配置
        /// </summary>
        /// <param name="args">命令行参数</param>
        /// <returns>启动配置</returns>
        public static ProcessStartupConfig? ParseStartupConfig(string[] args)
        {
            for (int i = 0; i < args.Length; i++)
            {
                if (args[i] == "--config" && i + 1 < args.Length)
                {
                    try
                    {
                        var configBase64 = args[i + 1];
                        var configJson = Encoding.UTF8.GetString(Convert.FromBase64String(configBase64));
                        return JsonSerializer.Deserialize<ProcessStartupConfig>(configJson);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"解析启动配置失败: {ex.Message}");
                        return null;
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// 序列化配置为Base64字符串
        /// </summary>
        /// <param name="config">配置对象</param>
        /// <returns>Base64字符串</returns>
        public static string SerializeConfigToBase64(ProcessStartupConfig config)
        {
            var configJson = JsonSerializer.Serialize(config);
            return Convert.ToBase64String(Encoding.UTF8.GetBytes(configJson));
        }

        /// <summary>
        /// 创建默认的启动配置
        /// </summary>
        /// <param name="processType">进程类型</param>
        /// <param name="userInfo">用户信息</param>
        /// <returns>启动配置</returns>
        public static ProcessStartupConfig CreateDefaultConfig(ProcessType processType, UserAuthenticationInfo userInfo)
        {
            return new ProcessStartupConfig
            {
                ProcessType = processType,
                UserInfo = userInfo,
                DatabaseConnectionString = GetDefaultConnectionString(),
                Language = "zh-CN"
            };
        }

        /// <summary>
        /// 获取默认数据库连接字符串
        /// </summary>
        /// <returns>连接字符串</returns>
        private static string GetDefaultConnectionString()
        {
            // 这里应该从配置文件或环境变量中读取
            return "Data Source=ModularAT.db;";
        }
    }
}
