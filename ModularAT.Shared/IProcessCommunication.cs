using System;
using System.Threading.Tasks;

namespace ModularAT.Shared
{
    /// <summary>
    /// 进程间通信接口
    /// </summary>
    public interface IProcessCommunication
    {
        /// <summary>
        /// 启动通信服务
        /// </summary>
        /// <param name="processName">进程名称</param>
        Task StartAsync(string processName);

        /// <summary>
        /// 停止通信服务
        /// </summary>
        Task StopAsync();

        /// <summary>
        /// 发送消息
        /// </summary>
        /// <param name="targetProcess">目标进程</param>
        /// <param name="message">消息内容</param>
        Task SendMessageAsync(string targetProcess, ProcessMessage message);

        /// <summary>
        /// 消息接收事件
        /// </summary>
        event EventHandler<ProcessMessageEventArgs> MessageReceived;

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event EventHandler<ProcessConnectionEventArgs> ConnectionStatusChanged;
    }

    /// <summary>
    /// 进程消息
    /// </summary>
    public class ProcessMessage
    {
        public string MessageType { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public string SourceProcess { get; set; } = string.Empty;
        public string TargetProcess { get; set; } = string.Empty;
    }

    /// <summary>
    /// 进程消息事件参数
    /// </summary>
    public class ProcessMessageEventArgs : EventArgs
    {
        public ProcessMessage Message { get; set; } = new ProcessMessage();
    }

    /// <summary>
    /// 进程连接事件参数
    /// </summary>
    public class ProcessConnectionEventArgs : EventArgs
    {
        public string ProcessName { get; set; } = string.Empty;
        public bool IsConnected { get; set; }
    }
}
