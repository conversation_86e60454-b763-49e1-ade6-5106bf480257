using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace ModularAT.Shared
{
    /// <summary>
    /// 进程管理器接口
    /// </summary>
    public interface IProcessManager
    {
        /// <summary>
        /// 启动子进程
        /// </summary>
        /// <param name="processType">进程类型</param>
        /// <param name="config">启动配置</param>
        /// <returns>进程实例</returns>
        Task<Process> StartProcessAsync(ProcessType processType, ProcessStartupConfig config);

        /// <summary>
        /// 停止进程
        /// </summary>
        /// <param name="processType">进程类型</param>
        Task StopProcessAsync(ProcessType processType);

        /// <summary>
        /// 获取进程状态
        /// </summary>
        /// <param name="processType">进程类型</param>
        /// <returns>是否运行中</returns>
        bool IsProcessRunning(ProcessType processType);

        /// <summary>
        /// 进程状态变化事件
        /// </summary>
        event EventHandler<ProcessStatusChangedEventArgs> ProcessStatusChanged;
    }

    /// <summary>
    /// 进程状态变化事件参数
    /// </summary>
    public class ProcessStatusChangedEventArgs : EventArgs
    {
        public ProcessType ProcessType { get; set; }
        public bool IsRunning { get; set; }
        public int? ExitCode { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 进程管理器实现
    /// </summary>
    public class ProcessManager : IProcessManager
    {
        private readonly System.Collections.Concurrent.ConcurrentDictionary<ProcessType, Process> _processes;

        public event EventHandler<ProcessStatusChangedEventArgs>? ProcessStatusChanged;

        public ProcessManager()
        {
            _processes = new System.Collections.Concurrent.ConcurrentDictionary<ProcessType, Process>();
        }

        /// <summary>
        /// 启动子进程
        /// </summary>
        public async Task<Process> StartProcessAsync(ProcessType processType, ProcessStartupConfig config)
        {
            try
            {
                // 如果进程已经在运行，先停止它
                if (IsProcessRunning(processType))
                {
                    await StopProcessAsync(processType);
                }

                var executablePath = GetExecutablePath(processType);
                var arguments = SerializeConfig(config);

                var processStartInfo = new ProcessStartInfo
                {
                    FileName = executablePath,
                    Arguments = arguments,
                    UseShellExecute = false,
                    CreateNoWindow = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                var process = new Process { StartInfo = processStartInfo };
                
                // 订阅进程退出事件
                process.EnableRaisingEvents = true;
                process.Exited += (sender, e) => OnProcessExited(processType, process);

                process.Start();
                _processes.AddOrUpdate(processType, process, (key, oldValue) => process);

                OnProcessStatusChanged(processType, true, null, null);

                return process;
            }
            catch (Exception ex)
            {
                OnProcessStatusChanged(processType, false, null, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 停止进程
        /// </summary>
        public async Task StopProcessAsync(ProcessType processType)
        {
            if (_processes.TryRemove(processType, out var process))
            {
                try
                {
                    if (!process.HasExited)
                    {
                        process.CloseMainWindow();
                        
                        // 等待进程优雅退出
                        if (!process.WaitForExit(5000))
                        {
                            process.Kill();
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"停止进程失败: {ex.Message}");
                }
                finally
                {
                    process.Dispose();
                    OnProcessStatusChanged(processType, false, null, null);
                }
            }
        }

        /// <summary>
        /// 获取进程状态
        /// </summary>
        public bool IsProcessRunning(ProcessType processType)
        {
            return _processes.TryGetValue(processType, out var process) && 
                   process != null && 
                   !process.HasExited;
        }

        /// <summary>
        /// 获取可执行文件路径
        /// </summary>
        private string GetExecutablePath(ProcessType processType)
        {
            return processType switch
            {
                ProcessType.Driver => "ModularAT.Driver.Process.exe",
                ProcessType.Controller => "ModularAT.Controller.Process.exe",
                _ => throw new ArgumentException($"未知的进程类型: {processType}")
            };
        }

        /// <summary>
        /// 序列化配置为命令行参数
        /// </summary>
        private string SerializeConfig(ProcessStartupConfig config)
        {
            var configJson = System.Text.Json.JsonSerializer.Serialize(config);
            return $"--config \"{Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(configJson))}\"";
        }

        /// <summary>
        /// 处理进程退出事件
        /// </summary>
        private void OnProcessExited(ProcessType processType, Process process)
        {
            _processes.TryRemove(processType, out _);
            OnProcessStatusChanged(processType, false, process.ExitCode, null);
        }

        protected virtual void OnProcessStatusChanged(ProcessType processType, bool isRunning, int? exitCode, string? errorMessage)
        {
            ProcessStatusChanged?.Invoke(this, new ProcessStatusChangedEventArgs
            {
                ProcessType = processType,
                IsRunning = isRunning,
                ExitCode = exitCode,
                ErrorMessage = errorMessage
            });
        }
    }
}
