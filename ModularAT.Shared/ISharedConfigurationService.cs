using System;
using System.Threading.Tasks;

namespace ModularAT.Shared
{
    /// <summary>
    /// 共享配置服务接口
    /// </summary>
    public interface ISharedConfigurationService
    {
        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="key">配置键</param>
        /// <returns>配置值</returns>
        Task<T> GetConfigurationAsync<T>(string key);

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        Task SetConfigurationAsync<T>(string key, T value);

        /// <summary>
        /// 配置变化事件
        /// </summary>
        event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;
    }

    /// <summary>
    /// 配置变化事件参数
    /// </summary>
    public class ConfigurationChangedEventArgs : EventArgs
    {
        public string Key { get; set; } = string.Empty;
        public object Value { get; set; } = new object();
        public Type ValueType { get; set; } = typeof(object);
    }

    /// <summary>
    /// 用户认证信息
    /// </summary>
    public class UserAuthenticationInfo
    {
        public string UserName { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string[] Roles { get; set; } = Array.Empty<string>();
        public string[] Permissions { get; set; } = Array.Empty<string>();
        public DateTime LoginTime { get; set; }
        public ProcessType SelectedProcessType { get; set; }
    }

    /// <summary>
    /// 进程启动配置
    /// </summary>
    public class ProcessStartupConfig
    {
        public ProcessType ProcessType { get; set; }
        public UserAuthenticationInfo UserInfo { get; set; } = new UserAuthenticationInfo();
        public string DatabaseConnectionString { get; set; } = string.Empty;
        public string Language { get; set; } = "zh-CN";
        public object? AdditionalConfig { get; set; }
    }
}
