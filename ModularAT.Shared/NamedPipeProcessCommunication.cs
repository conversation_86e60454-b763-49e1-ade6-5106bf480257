using System;
using System.IO;
using System.IO.Pipes;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace ModularAT.Shared
{
    /// <summary>
    /// 基于命名管道的进程间通信实现
    /// </summary>
    public class NamedPipeProcessCommunication : IProcessCommunication, IDisposable
    {
        private NamedPipeServerStream? _serverStream;
        private NamedPipeClientStream? _clientStream;
        private string _processName = string.Empty;
        private CancellationTokenSource? _cancellationTokenSource;
        private bool _isServer;

        public event EventHandler<ProcessMessageEventArgs>? MessageReceived;
        public event EventHandler<ProcessConnectionEventArgs>? ConnectionStatusChanged;

        /// <summary>
        /// 启动通信服务
        /// </summary>
        /// <param name="processName">进程名称</param>
        public async Task StartAsync(string processName)
        {
            _processName = processName;
            _cancellationTokenSource = new CancellationTokenSource();

            // 主进程作为服务器，子进程作为客户端
            _isServer = processName == ProcessNames.Host;

            if (_isServer)
            {
                await StartServerAsync();
            }
            else
            {
                await StartClientAsync();
            }
        }

        /// <summary>
        /// 启动服务器端
        /// </summary>
        private async Task StartServerAsync()
        {
            try
            {
                _serverStream = new NamedPipeServerStream(
                    "ModularAT_IPC",
                    PipeDirection.InOut,
                    NamedPipeServerStream.MaxAllowedServerInstances,
                    PipeTransmissionMode.Message,
                    PipeOptions.Asynchronous);

                await _serverStream.WaitForConnectionAsync(_cancellationTokenSource.Token);
                
                OnConnectionStatusChanged(_processName, true);
                
                // 开始监听消息
                _ = Task.Run(ListenForMessagesAsync, _cancellationTokenSource.Token);
            }
            catch (Exception ex)
            {
                // 记录错误日志
                Console.WriteLine($"启动服务器失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动客户端
        /// </summary>
        private async Task StartClientAsync()
        {
            try
            {
                _clientStream = new NamedPipeClientStream(
                    ".",
                    "ModularAT_IPC",
                    PipeDirection.InOut,
                    PipeOptions.Asynchronous);

                await _clientStream.ConnectAsync(5000, _cancellationTokenSource.Token);
                
                OnConnectionStatusChanged(_processName, true);
                
                // 开始监听消息
                _ = Task.Run(ListenForMessagesAsync, _cancellationTokenSource.Token);
            }
            catch (Exception ex)
            {
                // 记录错误日志
                Console.WriteLine($"连接服务器失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 监听消息
        /// </summary>
        private async Task ListenForMessagesAsync()
        {
            var stream = _isServer ? (Stream)_serverStream : _clientStream;
            var buffer = new byte[4096];

            try
            {
                while (!_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length, _cancellationTokenSource.Token);
                    
                    if (bytesRead > 0)
                    {
                        var messageJson = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                        var message = JsonSerializer.Deserialize<ProcessMessage>(messageJson);
                        
                        OnMessageReceived(message);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"监听消息失败: {ex.Message}");
                OnConnectionStatusChanged(_processName, false);
            }
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        public async Task SendMessageAsync(string targetProcess, ProcessMessage message)
        {
            try
            {
                message.SourceProcess = _processName;
                message.TargetProcess = targetProcess;
                message.Timestamp = DateTime.Now;

                var messageJson = JsonSerializer.Serialize(message);
                var buffer = Encoding.UTF8.GetBytes(messageJson);

                var stream = _isServer ? (Stream)_serverStream : _clientStream;
                await stream.WriteAsync(buffer, 0, buffer.Length);
                await stream.FlushAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发送消息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止通信服务
        /// </summary>
        public async Task StopAsync()
        {
            _cancellationTokenSource?.Cancel();
            
            _serverStream?.Close();
            _clientStream?.Close();
            
            OnConnectionStatusChanged(_processName, false);
            
            await Task.CompletedTask;
        }

        protected virtual void OnMessageReceived(ProcessMessage message)
        {
            MessageReceived?.Invoke(this, new ProcessMessageEventArgs { Message = message });
        }

        protected virtual void OnConnectionStatusChanged(string processName, bool isConnected)
        {
            ConnectionStatusChanged?.Invoke(this, new ProcessConnectionEventArgs 
            { 
                ProcessName = processName, 
                IsConnected = isConnected 
            });
        }

        public void Dispose()
        {
            StopAsync().Wait();
            _cancellationTokenSource?.Dispose();
            _serverStream?.Dispose();
            _clientStream?.Dispose();
        }
    }
}
