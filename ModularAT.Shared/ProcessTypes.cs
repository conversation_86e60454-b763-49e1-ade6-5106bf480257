namespace ModularAT.Shared
{
    /// <summary>
    /// 进程类型枚举
    /// </summary>
    public enum ProcessType
    {
        /// <summary>
        /// 主进程 - 配置管理中心
        /// </summary>
        Host,

        /// <summary>
        /// 驱动器进程
        /// </summary>
        Driver,

        /// <summary>
        /// 控制器进程
        /// </summary>
        Controller
    }

    /// <summary>
    /// 消息类型常量
    /// </summary>
    public static class MessageTypes
    {
        /// <summary>
        /// 配置更新消息
        /// </summary>
        public const string ConfigUpdate = "ConfigUpdate";

        /// <summary>
        /// 用户认证信息
        /// </summary>
        public const string UserAuthentication = "UserAuthentication";

        /// <summary>
        /// 权限信息
        /// </summary>
        public const string PermissionInfo = "PermissionInfo";

        /// <summary>
        /// 状态同步
        /// </summary>
        public const string StatusSync = "StatusSync";

        /// <summary>
        /// 进程关闭通知
        /// </summary>
        public const string ProcessShutdown = "ProcessShutdown";

        /// <summary>
        /// 心跳消息
        /// </summary>
        public const string Heartbeat = "Heartbeat";
    }

    /// <summary>
    /// 进程名称常量
    /// </summary>
    public static class ProcessNames
    {
        public const string Host = "ModularAT.Host";
        public const string Driver = "ModularAT.Driver.Process";
        public const string Controller = "ModularAT.Controller.Process";
    }
}
