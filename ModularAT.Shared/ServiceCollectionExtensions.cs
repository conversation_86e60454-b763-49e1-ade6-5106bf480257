using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace ModularAT.Shared
{
    /// <summary>
    /// 服务集合扩展方法
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 添加共享基础设施服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddSharedInfrastructure(this IServiceCollection services)
        {
            // 注册进程间通信服务
            services.AddSingleton<IProcessCommunication, NamedPipeProcessCommunication>();
            
            // 注册共享配置服务
            services.AddSingleton<ISharedConfigurationService, SharedConfigurationService>();
            
            // 注册进程管理器（仅主进程需要）
            services.AddSingleton<IProcessManager, ProcessManager>();
            
            return services;
        }

        /// <summary>
        /// 添加主进程服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddHostProcessServices(this IServiceCollection services)
        {
            services.AddSharedInfrastructure();
            
            // 主进程特有的服务可以在这里添加
            
            return services;
        }

        /// <summary>
        /// 添加子进程服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="processType">进程类型</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddChildProcessServices(this IServiceCollection services, ProcessType processType)
        {
            services.AddSharedInfrastructure();
            
            // 根据进程类型添加特定服务
            switch (processType)
            {
                case ProcessType.Driver:
                    // 驱动器进程特有的服务
                    break;
                case ProcessType.Controller:
                    // 控制器进程特有的服务
                    break;
            }
            
            return services;
        }
    }

    /// <summary>
    /// 主机构建器扩展方法
    /// </summary>
    public static class HostBuilderExtensions
    {
        /// <summary>
        /// 配置共享基础设施
        /// </summary>
        /// <param name="hostBuilder">主机构建器</param>
        /// <param name="processType">进程类型</param>
        /// <returns>主机构建器</returns>
        public static IHostBuilder ConfigureSharedInfrastructure(this IHostBuilder hostBuilder, ProcessType processType)
        {
            return hostBuilder.ConfigureServices((context, services) =>
            {
                if (processType == ProcessType.Host)
                {
                    services.AddHostProcessServices();
                }
                else
                {
                    services.AddChildProcessServices(processType);
                }
            });
        }
    }
}
