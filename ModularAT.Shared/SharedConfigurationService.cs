using System;
using System.Collections.Concurrent;
using System.Text.Json;
using System.Threading.Tasks;

namespace ModularAT.Shared
{
    /// <summary>
    /// 共享配置服务实现
    /// </summary>
    public class SharedConfigurationService : ISharedConfigurationService
    {
        private readonly ConcurrentDictionary<string, object> _configurations;
        private readonly IProcessCommunication _processCommunication;

        public event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;

        public SharedConfigurationService(IProcessCommunication processCommunication)
        {
            _configurations = new ConcurrentDictionary<string, object>();
            _processCommunication = processCommunication;
            
            // 订阅进程间通信消息
            _processCommunication.MessageReceived += OnMessageReceived;
        }

        /// <summary>
        /// 获取配置值
        /// </summary>
        public async Task<T> GetConfigurationAsync<T>(string key)
        {
            if (_configurations.TryGetValue(key, out var value))
            {
                if (value is T directValue)
                {
                    return directValue;
                }
                
                // 尝试反序列化
                if (value is string jsonValue)
                {
                    try
                    {
                        return JsonSerializer.Deserialize<T>(jsonValue);
                    }
                    catch
                    {
                        // 反序列化失败，返回默认值
                    }
                }
            }

            return default(T);
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        public async Task SetConfigurationAsync<T>(string key, T value)
        {
            var serializedValue = JsonSerializer.Serialize(value);
            _configurations.AddOrUpdate(key, serializedValue, (k, v) => serializedValue);

            // 通知配置变化
            OnConfigurationChanged(key, value, typeof(T));

            // 通过进程间通信广播配置变化
            var message = new ProcessMessage
            {
                MessageType = MessageTypes.ConfigUpdate,
                Content = JsonSerializer.Serialize(new
                {
                    Key = key,
                    Value = serializedValue,
                    ValueType = typeof(T).FullName
                })
            };

            // 广播给所有进程（这里简化处理，实际可能需要更复杂的路由逻辑）
            await _processCommunication.SendMessageAsync("*", message);
        }

        /// <summary>
        /// 处理接收到的消息
        /// </summary>
        private void OnMessageReceived(object sender, ProcessMessageEventArgs e)
        {
            if (e.Message.MessageType == MessageTypes.ConfigUpdate)
            {
                try
                {
                    using var document = JsonDocument.Parse(e.Message.Content);
                    var root = document.RootElement;
                    var key = root.GetProperty("Key").GetString();
                    var value = root.GetProperty("Value").GetString();
                    var valueTypeName = root.GetProperty("ValueType").GetString();

                    if (key != null && value != null)
                    {
                        _configurations.AddOrUpdate(key, value, (k, v) => value);

                        // 触发配置变化事件
                        var valueType = Type.GetType(valueTypeName ?? "System.Object") ?? typeof(object);
                        OnConfigurationChanged(key, value, valueType);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"处理配置更新消息失败: {ex.Message}");
                }
            }
        }

        protected virtual void OnConfigurationChanged(string key, object value, Type valueType)
        {
            ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs
            {
                Key = key,
                Value = value,
                ValueType = valueType
            });
        }
    }
}
