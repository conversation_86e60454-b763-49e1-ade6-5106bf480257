<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <LangVersion>preview</LangVersion>
        <OutputType>WinExe</OutputType>
        <TargetFramework>netframework48</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UseWPF>true</UseWPF>
        <ApplicationIcon>Resources\Images\modular.ico</ApplicationIcon>
        <Version>********</Version>
        <AssemblyName>IMTS-Studio</AssemblyName>
        <Title>IMTS-Studio（智能磁悬浮输送系统配置软件）</Title>
        <Authors>Modular</Authors>
        <Description>智能磁悬浮输送系统配置软件</Description>
    </PropertyGroup>

    <ItemGroup>
        <Compile Remove="ViewModels\ViewModelLocatorLazy.cs" />
        <Compile Update="Views\FtpClientView.xaml.cs">
          <SubType>Code</SubType>
          <DependentUpon>FtpClientView.xaml</DependentUpon>
        </Compile>
    </ItemGroup>

    <ItemGroup>
        <None Remove="appsettings.json" />
        <None Remove="modular.gif" />
        <None Remove="ModularAT.png" />
        <None Remove="Resources\Images\loginBackGround.png" />
        <None Remove="Resources\modular.gif" />
        <None Remove="Resources\ModularAT.png" />
        <None Update="viewLineConfig.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <Content Include="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Include="Resources\Images\modular.ico" />
    </ItemGroup>

    <ItemGroup>
        <Resource Include="Resources\Images\loginBackGround.png" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Autofac" Version="8.1.1" />
        <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
        <PackageReference Include="AutoMapper" Version="10.1.1" />
        <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="8.1.1" />
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
        <PackageReference Include="CompiledBindings.WPF" Version="1.0.18" />
        <PackageReference Include="FluentFTP" Version="52.1.0" />
        <PackageReference Include="HandyControl" Version="3.5.1" />
        <PackageReference Include="MahApps.Metro" Version="2.4.10" />
        <PackageReference Include="MahApps.Metro.IconPacks.Material" Version="5.1.0" />
        <PackageReference Include="MahApps.Metro.IconPacks.Modern" Version="5.1.0" />
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.0" />
        <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.135" />
        <PackageReference Include="MiniExcel" Version="1.41.3" />
        <PackageReference Include="ScottPlot.WPF" Version="5.0.55" />
        <PackageReference Include="SqlSugar" Version="*********-preview11" />
        <PackageReference Include="System.Data.SQLite" Version="1.0.119" />
        <PackageReference Include="ValueConverters" Version="3.1.22" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\ModularAT.Driver.Controller\ModularAT.Driver.Controller.csproj" />
        <ProjectReference Include="..\ModularAT.Driver.Servo\ModularAT.Driver.Servo.csproj" />
        <ProjectReference Include="..\ModularAT.Entity\ModularAT.Entity.csproj" />
        <ProjectReference Include="..\ModularAT.Common\ModularAT.Common.csproj" />
        <ProjectReference Include="..\ModularAT.Driver.Servo\ModularAT.Driver.Servo.csproj" />
        <ProjectReference Include="..\ModularAT.Entity\ModularAT.Entity.csproj" />
        <ProjectReference Include="..\ModularAT.Localization\ModularAT.Localization.csproj" />
        <ProjectReference Include="..\ModularAT.Repository\ModularAT.Repository.csproj" />
        <ProjectReference Include="..\ModularAT.Service.Setting\ModularAT.Service.Setting.csproj" />
        <ProjectReference Include="..\ModularAT.Service.Simulation\ModularAT.Service.Simulation.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Reference Include="MahApps.Metro.IconPacks.Material">
            <HintPath>C:\Users\<USER>\.nuget\packages\mahapps.metro.iconpacks.material\5.1.0\lib\net47\MahApps.Metro.IconPacks.Material.dll</HintPath>
        </Reference>
        <Reference Include="netstandard" />
        <Reference Include="System.ComponentModel.Composition" />
        <Reference Include="System.Windows.Forms" />
        <Reference Include="WindowsFormsIntegration" />
    </ItemGroup>

    <ItemGroup>
        <Resource Include="Resources\Images\modular.gif" />
        <Resource Include="Resources\Images\ModularAT.png" />
    </ItemGroup>

    <ItemGroup>
        <Compile Update="Views\ControlerOnlineConfigView.xaml.cs">
            <SubType>Code</SubType>
        </Compile>
        <Compile Update="Views\ControlerTranStatusView.xaml.cs">
            <SubType>Code</SubType>
        </Compile>
        <Compile Update="Views\ControlerSysView.xaml.cs">
            <SubType>Code</SubType>
        </Compile>
    </ItemGroup>

    <ItemGroup>
        <None Update="clear.bat">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Modular.db">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <Page Update="Views\BaseManage\BasePermAssignView.xaml">
            <SubType>Designer</SubType>
        </Page>
        <Page Update="Resources\Styles\CommonViewStyles.xaml">
          <Generator>MSBuild:Compile</Generator>
          <XamlRuntime>Wpf</XamlRuntime>
          <SubType>Designer</SubType>
        </Page>
        <Page Update="Views\FtpClientView.xaml">
          <Generator>MSBuild:Compile</Generator>
          <XamlRuntime>Wpf</XamlRuntime>
          <SubType>Designer</SubType>
        </Page>
    </ItemGroup>

</Project>
