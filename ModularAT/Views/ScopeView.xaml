<UserControl
    x:Class="ModularAT.Views.ScopeView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:local="clr-namespace:ModularAT.Views"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:scottPlot="clr-namespace:ScottPlot.WPF;assembly=ScottPlot.WPF"
    xmlns:vc="clr-namespace:ModularAT.ValueConverter"
    xmlns:conv="clr-namespace:ValueConverters;assembly=ValueConverters"
    d:DataContext="{d:DesignInstance Type=local:ScopeView}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    Unloaded="UserControl_Unloaded"
    mc:Ignorable="d">
    <UserControl.Resources>
        <vc:DrawingColorToBrushConverter x:Key="DrawingColorToBrushConverter" />
        <Thickness x:Key="ControlMargin">10 5 0 0</Thickness>
        <conv:BoolInverter x:Key="InverseBoolConverter"></conv:BoolInverter>
        <conv:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter" />
        <conv:BoolToVisibilityConverter
            x:Key="Bool2VisibilityInverseConverter"
            FalseValue="Visible"
            TrueValue="Hidden" />
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <DockPanel Grid.Row="0" LastChildFill="False">
            <DockPanel.Resources>
                <Style BasedOn="{StaticResource MahApps.Styles.CheckBox}" TargetType="{x:Type CheckBox}">
                    <Setter Property="Margin" Value="{StaticResource ControlMargin}" />
                </Style>
            </DockPanel.Resources>
            <!--  左侧的按钮  -->
            <Button
                Command="{Binding EndRunCommand}"
                Content="{Binding LanResources.Scope_Stop, Source={x:Static localization:LocalizationService.Current}}"
                DockPanel.Dock="Left">
                <Button.Style>
                    <Style BasedOn="{StaticResource DefaultButton}" TargetType="{x:Type TypeName=Button}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Path=IsRuning}" Value="False">
                                <Setter Property="IsEnabled" Value="False" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Path=IsDataImported}" Value="True">
                                <Setter Property="IsEnabled" Value="False" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
            <Button
                Name="startBtn"
                Command="{Binding StartRunCommand}"
                Content="{Binding LanResources.Scope_Collect, Source={x:Static localization:LocalizationService.Current}}"
                DockPanel.Dock="Left">
                <Button.Style>
                    <Style BasedOn="{StaticResource DefaultButton}" TargetType="{x:Type TypeName=Button}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Path=IsRuning}" Value="True">
                                <Setter Property="IsEnabled" Value="False" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Path=IsDataImported}" Value="True">
                                <Setter Property="IsEnabled" Value="False" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
            <Button
                Name="resetBtn"
                Command="{Binding ClearDataCommand}"
                Content="{Binding LanResources.Scope_Reset, Source={x:Static localization:LocalizationService.Current}}"
                DockPanel.Dock="Left">
                <Button.Style>
                    <Style BasedOn="{StaticResource DefaultButton}" TargetType="{x:Type TypeName=Button}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Path=IsRuning}" Value="True">
                                <Setter Property="IsEnabled" Value="False" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
                <i:Interaction.Triggers>
                    <i:EventTrigger EventName="Click">
                        <i:ChangePropertyAction
                            PropertyName="Visibility"
                            TargetObject="{Binding ElementName=realvalColn}"
                            Value="Visible" />
                        <i:ChangePropertyAction
                            PropertyName="Visibility"
                            TargetObject="{Binding ElementName=selectObjColn}"
                            Value="Visible" />
                        <i:ChangePropertyAction
                            PropertyName="IsEnabled"
                            TargetObject="{Binding ElementName=startBtn}"
                            Value="True" />
                    </i:EventTrigger>
                </i:Interaction.Triggers>
            </Button>

            <CheckBox Command="{Binding ShowSpansCommand}"
                      IsEnabled="{Binding Path=IsRuning, Mode=OneWay,Converter={StaticResource InverseBoolConverter}}"
                      IsChecked="{Binding IsShowCrosshair, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                <TextBlock
                    Text="{Binding LanResources.Scope_Cross_star, Source={x:Static localization:LocalizationService.Current}}" />
            </CheckBox>
            <CheckBox Command="{Binding ShowSpansCommand}"
                      IsEnabled="{Binding Path=IsRuning, Mode=OneWay,Converter={StaticResource InverseBoolConverter}}"
                      IsChecked="{Binding IsShowHorizontalSpan, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                <TextBlock
                    Text="{Binding LanResources.Scope_X_axis_scale, Source={x:Static localization:LocalizationService.Current}}" />
            </CheckBox>
            <CheckBox Command="{Binding ShowSpansCommand}"
                      IsEnabled="{Binding Path=IsRuning, Mode=OneWay,Converter={StaticResource InverseBoolConverter}}"
                      IsChecked="{Binding IsShowVerticalSpan, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                <TextBlock
                    Text="{Binding LanResources.Scope_Y_axis_scale, Source={x:Static localization:LocalizationService.Current}}" />
            </CheckBox>

            <!--  右侧的元素  -->
            <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                <Button
                    IsEnabled="{Binding Path=IsRuning, Mode=OneWay,Converter={StaticResource InverseBoolConverter}}"
                    Command="{Binding ImportDataCommand}"
                    Content="{Binding LanResources.Scope_Import, Source={x:Static localization:LocalizationService.Current}}" />
                <Button
                    IsEnabled="{Binding Path=IsRuning, Mode=OneWay,Converter={StaticResource InverseBoolConverter}}"
                    Command="{Binding ExportDataCommand}"
                    Content="{Binding LanResources.Scope_Export, Source={x:Static localization:LocalizationService.Current}}" />
                <Button Command="{Binding AutoScaleCommand}"
                        Content="{Binding LanResources.Scope_Zoom, Source={x:Static localization:LocalizationService.Current}}">
                </Button>
                <Button
                    IsEnabled="{Binding Path=IsRuning, Mode=OneWay,Converter={StaticResource InverseBoolConverter}}"
                    Command="{Binding LoadErrRecordCommand}" Content="{Binding LanResources.Scope_Error_log, Source={x:Static localization:LocalizationService.Current}}">
                    <i:Interaction.Triggers>
                        <i:EventTrigger EventName="Click">
                            <i:ChangePropertyAction
                                PropertyName="Visibility"
                                TargetObject="{Binding ElementName=realvalColn}"
                                Value="Hidden" />
                            <i:ChangePropertyAction
                                PropertyName="Visibility"
                                TargetObject="{Binding ElementName=selectObjColn}"
                                Value="Hidden" />
                            <i:ChangePropertyAction
                                PropertyName="IsEnabled"
                                TargetObject="{Binding ElementName=startBtn}"
                                Value="False" />
                        </i:EventTrigger>
                    </i:Interaction.Triggers>
                </Button>
                <Button
                    IsEnabled="{Binding Path=IsRuning, Mode=OneWay,Converter={StaticResource InverseBoolConverter}}"
                    Command="{Binding LoadProcessRecordCommand}" Content="{Binding LanResources.Scope_Process_log, Source={x:Static localization:LocalizationService.Current}}">
                    <i:Interaction.Triggers>
                        <i:EventTrigger EventName="Click">
                            <i:ChangePropertyAction
                                PropertyName="Visibility"
                                TargetObject="{Binding ElementName=realvalColn}"
                                Value="Hidden" />
                            <i:ChangePropertyAction
                                PropertyName="Visibility"
                                TargetObject="{Binding ElementName=selectObjColn}"
                                Value="Hidden" />
                            <i:ChangePropertyAction
                                PropertyName="IsEnabled"
                                TargetObject="{Binding ElementName=startBtn}"
                                Value="False" />
                        </i:EventTrigger>
                    </i:Interaction.Triggers>
                </Button>
                <Button
                    IsEnabled="{Binding Path=IsRuning, Mode=OneWay,Converter={StaticResource InverseBoolConverter}}"
                    Command="{Binding LoadPlcCtrlSignalRecordCommand}" Content="{Binding LanResources.Scope_Plc_signal, Source={x:Static localization:LocalizationService.Current}}">
                    <i:Interaction.Triggers>
                        <i:EventTrigger EventName="Click">
                            <i:ChangePropertyAction
                                PropertyName="Visibility"
                                TargetObject="{Binding ElementName=realvalColn}"
                                Value="Hidden" />
                            <i:ChangePropertyAction
                                PropertyName="IsEnabled"
                                TargetObject="{Binding ElementName=startBtn}"
                                Value="False" />
                        </i:EventTrigger>
                    </i:Interaction.Triggers>
                </Button>
                <TextBlock Margin="0,10,10,0"
                           Text="{Binding LanResources.Scope_Sample_freq_1_300_ms, Source={x:Static localization:LocalizationService.Current}}" />
                <TextBox Width="100" Margin="0,5,13,5" Text="2"   IsEnabled="{Binding Path=IsRuning, Mode=OneWay,Converter={StaticResource InverseBoolConverter}}">
                    <i:Interaction.Triggers>
                        <i:EventTrigger EventName="TextChanged">
                            <i:InvokeCommandAction Command="{Binding Path=SetTickCommand}"
                                                   CommandParameter="{Binding Text, RelativeSource={RelativeSource AncestorType={x:Type TextBox}}}" />
                        </i:EventTrigger>
                    </i:Interaction.Triggers>
                </TextBox>
            </StackPanel>
        </DockPanel>
        <scottPlot:WpfPlot x:Name="ScottPlot" Grid.Row="1">
            <i:Interaction.Triggers>
                <i:EventTrigger EventName="MouseEnter">
                    <i:CallMethodAction MethodName="CheckCanTagging" TargetObject="{Binding}" />
                </i:EventTrigger>
            </i:Interaction.Triggers>
        </scottPlot:WpfPlot>
        <TabControl
            Grid.Row="2"
            Height="230"
            Grid.ColumnSpan="2"
            Style="{StaticResource ResourceKey=DefaultTabControl}">
            <TabItem
                Header="{Binding LanResources.Scope_Channel, Source={x:Static localization:LocalizationService.Current}}">
                <DataGrid
                    IsEnabled="{Binding Path=IsRuning, Mode=OneWay,Converter={StaticResource InverseBoolConverter}}"
                    CanUserAddRows="False"
                    ItemsSource="{Binding ScopeList}"
                    SelectionMode="Single"
                    Style="{StaticResource DefaultDataGrid}">
                    <i:Interaction.Triggers>
                        <i:EventTrigger EventName="SelectionChanged">
                            <i:InvokeCommandAction Command="{Binding Path=SelectedCommand}"
                                                   CommandParameter="{Binding SelectedItem, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}" />
                        </i:EventTrigger>
                    </i:Interaction.Triggers>
                    <DataGrid.ColumnHeaderStyle>
                        <Style TargetType="{x:Type DataGridColumnHeader}">
                            <Setter Property="HorizontalContentAlignment" Value="Center" />
                        </Style>
                    </DataGrid.ColumnHeaderStyle>
                    <DataGrid.Columns>
                        <!--  ID列  -->
                        <DataGridTextColumn Binding="{Binding Id, Mode=OneWay}" Header="ID" />

                        <!--  通道名列  -->
                        <DataGridTextColumn Binding="{Binding Name, Mode=OneWay}"
                                            Header="{Binding LanResources.Scope_Channel, Source={x:Static localization:LocalizationService.Current}}" />

                        <DataGridTemplateColumn x:Name="selectObjColn"
                                                Header="{Binding LanResources.Scope_Sel_obj, Source={x:Static localization:LocalizationService.Current}}"
                                                Visibility="{Binding IsDataImported,Converter={StaticResource Bool2VisibilityInverseConverter }}">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <ComboBox
                                        x:Name="groupingComboBox"
                                        Width="180"
                                        mah:TextBoxHelper.Watermark="{Binding LanResources.Scope_Please_select, Source={x:Static localization:LocalizationService.Current}}"
                                        DisplayMemberPath="Item2"
                                        IsEditable="True"
                                        IsEnabled="True"
                                        ItemsSource="{Binding ChannelParams, RelativeSource={RelativeSource AncestorType=local:ScopeView}}"
                                        SelectedValue="{Binding ParamIndex, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        SelectedValuePath="Item3"
                                        Style="{DynamicResource MahApps.Styles.ComboBox.Virtualized}">
                                        <i:Interaction.Triggers>
                                            <i:EventTrigger EventName="SelectionChanged">
                                                <i:InvokeCommandAction
                                                    Command="{Binding RefreshPlotCommand, RelativeSource={RelativeSource AncestorType=local:ScopeView}}" />
                                            </i:EventTrigger>
                                        </i:Interaction.Triggers>
                                        <ComboBox.GroupStyle>
                                            <GroupStyle>
                                                <GroupStyle.HeaderTemplate>
                                                    <DataTemplate>
                                                        <TextBlock Text="{Binding Item1}" />
                                                    </DataTemplate>
                                                </GroupStyle.HeaderTemplate>
                                            </GroupStyle>
                                        </ComboBox.GroupStyle>
                                    </ComboBox>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  数值  -->
                        <DataGridTextColumn
                            x:Name="realvalColn"
                            Binding="{Binding Value, Mode=OneWay, UpdateSourceTrigger=PropertyChanged}"
                            Header="{Binding LanResources.Scope_Value, Source={x:Static localization:LocalizationService.Current}}" />

                        <!--  是否可见，使用勾选框  -->
                        <DataGridTemplateColumn Width="Auto"
                                                Header="{Binding LanResources.Scope_Is_visible, Source={x:Static localization:LocalizationService.Current}}">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <CheckBox
                                        IsChecked="{Binding IsVisible, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                                        <i:Interaction.Triggers>
                                            <i:EventTrigger EventName="Click">
                                                <i:InvokeCommandAction
                                                    Command="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=local:ScopeView}, Path=HideDataLoggerCommand}" />
                                            </i:EventTrigger>
                                        </i:Interaction.Triggers>
                                    </CheckBox>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  偏移量列，使用数字文本框  -->
                        <mah:DataGridNumericUpDownColumn Binding="{Binding Offset}"
                                                         Header="{Binding LanResources.Scope_Offset, Source={x:Static localization:LocalizationService.Current}}" />

                        <!--  放大倍数列，使用数值文本框  -->
                        <mah:DataGridNumericUpDownColumn
                            Binding="{Binding Scale}"
                            Header="{Binding LanResources.Scope_Magni, Source={x:Static localization:LocalizationService.Current}}"
                            Minimum="0" />

                        <!--  颜色列，使用自定义模板显示颜色  -->
                        <DataGridTemplateColumn
                            Header="{Binding LanResources.Scope_Color, Source={x:Static localization:LocalizationService.Current}}">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Ellipse
                                        Width="20"
                                        Height="20"
                                        Fill="{Binding Color, Converter={StaticResource DrawingColorToBrushConverter}}"
                                        Stroke="White" />
                                    <!--<Border Background="{Binding Color, Converter={StaticResource DrawingColorToBrushConverter}}" />-->
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>
                  <TabItem
                Header="{Binding LanResources.Scope_Debug, Source={x:Static localization:LocalizationService.Current}}">
                <StackPanel Orientation="Vertical" Margin="8" HorizontalAlignment="Left">
                    <!-- 标题和按钮 - 紧凑排列 -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <TextBlock Text="{Binding LanResources.Scope_Plc_control_signal_count, Source={x:Static localization:LocalizationService.Current}}" FontWeight="Bold" VerticalAlignment="Center"/>
                        <Button Command="{Binding GetPlcCtrlSignalCountCommand}"
                                Content="{Binding LanResources.Scope_Acquire_count, Source={x:Static localization:LocalizationService.Current}}"
                                Margin="8,0,0,0"
                                Padding="6,2"
                                MinWidth="60"/>
                    </StackPanel>

                    <!-- 数据显示区域 - 紧凑靠左排列 -->
                    <StackPanel Orientation="Vertical">
                        <!-- 轴0 -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                            <TextBlock Text="{Binding LanResources.Scope_Axis_0, Source={x:Static localization:LocalizationService.Current}}" FontWeight="Bold" VerticalAlignment="Center" Width="25"/>
                            <TextBlock Text="{Binding LanResources.Scope_Brake, Source={x:Static localization:LocalizationService.Current}}" VerticalAlignment="Center" Margin="5,0,3,0"/>
                            <TextBox Text="{Binding Axis0StopSignalCount, Mode=OneWay}" Width="50" IsReadOnly="True" Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding LanResources.Scope_Acceleration, Source={x:Static localization:LocalizationService.Current}}" VerticalAlignment="Center" Margin="0,0,3,0"/>
                            <TextBox Text="{Binding Axis0AccSignalCount, Mode=OneWay}" Width="50" IsReadOnly="True"/>
                        </StackPanel>

                        <!-- 轴1 -->
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="{Binding LanResources.Scope_Axis_1, Source={x:Static localization:LocalizationService.Current}}" FontWeight="Bold" VerticalAlignment="Center" Width="25"/>
                            <TextBlock Text="{Binding LanResources.Scope_Brake, Source={x:Static localization:LocalizationService.Current}}" VerticalAlignment="Center" Margin="5,0,3,0"/>
                            <TextBox Text="{Binding Axis1StopSignalCount, Mode=OneWay}" Width="50" IsReadOnly="True" Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding LanResources.Scope_Acceleration, Source={x:Static localization:LocalizationService.Current}}" VerticalAlignment="Center" Margin="0,0,3,0"/>
                            <TextBox Text="{Binding Axis1AccSignalCount, Mode=OneWay}" Width="50" IsReadOnly="True"/>
                        </StackPanel>
                    </StackPanel>
                </StackPanel>
            </TabItem>
        </TabControl>

    </Grid>
</UserControl>