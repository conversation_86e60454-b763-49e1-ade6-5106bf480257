using ModularAT.Localization.Resources;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Text;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Input;
using System.Windows.Threading;
using AutoMapper;
using AutoMapper.Internal;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using CommunityToolkit.Mvvm.Messaging.Messages;
using Microsoft.Extensions.DependencyInjection;
using MiniExcelLibs;
using ModularAT.Common.Log;
using ModularAT.Driver.Servo;
using ModularAT.Driver.Servo.Enums;
using ModularAT.Entity;
using ModularAT.Entity.Dtos.Servo;
using ModularAT.Entity.Enum;
using ModularAT.Entitys;
using ModularAT.ViewModels;
using ScottPlot;
using ScottPlot.Plottables;
using TouchSocket.Core;
using Color = ScottPlot.Color;
using Cursors = System.Windows.Input.Cursors;
using UserControl = System.Windows.Controls.UserControl;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace ModularAT.Views;

/// <summary>
///     ScopeView.xaml 的交互逻辑
/// </summary>
[INotifyPropertyChanged]
public partial class ScopeView : UserControl
{
    #region Construct

    public ScopeView()
    {
        InitializeComponent();
        DataContext = this;
        InitializeChannelsPlot();
        InitKeyBind();
        // 注册串口连接状态变化消息监听
        WeakReferenceMessenger.Default.Register<ValueChangedMessage<bool>, string>(this, "ServoSerialPort",
            OnServoConnectionChanged);
        // 注册PLC控制信号计数消息监听
        WeakReferenceMessenger.Default.Register<ValueChangedMessage<PlcCtrlSignalCountDto>, string>(this,
            "PlcCtrlSignalCount", OnPlcCtrlSignalCountReceived);
    }

    #endregion

    private string SerializeDoubleArrayToString(double[] array)
    {
        // 使用StringBuilder来构建字符串
        var sb = new StringBuilder();
        for (var i = 0; i < array.Length; i++)
        {
            sb.Append(array[i].ToString("G", CultureInfo.InvariantCulture)); // 使用G格式，不使用千分位分隔符
            if (i < array.Length - 1) sb.Append("`"); // 数值之间用逗号分隔
        }

        return sb.ToString();
    }

    private double[] DeserializeStringtoDoubleArray(string serializedData)
    {
        // 根据反引号分割字符串
        string[] stringDoubles = serializedData.Split('`');

        // 初始化double数组
        var doubles = new double[stringDoubles.Length];

        // 将每个字符串解析为double并填充到数组中
        for (var i = 0; i < stringDoubles.Length; i++)
            doubles[i] = double.Parse(stringDoubles[i], CultureInfo.InvariantCulture);

        return doubles;
    }

    public class ChannelsCsvData
    {
        public string Label { get; set; }
        public string Color { get; set; }
        public double SampleRate { get; set; }
        public string Ys { get; set; }
    }

    #region Variables

    private LinkedList<ScopeChannelsFeedback> _scopeFeedbacks; //缓存数据

    private Stopwatch _stopwatch;

    private long _previousTime; //上一次追加的毫秒数
    private long _baseTime; //基准时间偏移量，用于确保时间戳连续递增

    private ScopeChannelsFeedback _feedback; //ServoContext.Instance.ScopeFeedback;//临时反馈数据对象

    private readonly DispatcherTimer _curveRefreshTimer = new(); //曲线刷新定时器

    /// <summary>
    ///     曲线刷新间隔ms
    /// </summary>
    private int _setTick = 2;

    private readonly ServoSerialPortClient client = App.Current.Services.GetService<ServoSerialPortClient>()!;

    //标注相关变量
    private Crosshair crosshair;
    private Marker highlightedPoint;

    private HorizontalSpan hSpan;
    private VerticalSpan vSpan;

    // 添加Text组件用于显示span数值
    private Text hSpanText;
    private Text vSpanText;

    // 吸附点相关变量
    private Marker snapPointMarker; // 吸附点高亮标记
    private const double SNAP_DISTANCE = 20; // 吸附距离（像素）
    private bool isSnapping = false; // 是否启用吸附

    private (double pointX, double pointY, int pointIndex) mousePoint;
    // private (double coordinateX, double coordinateY) coordinates; //当前鼠标的位置，十字星事件内更新

    #endregion

    #region Property

    private bool isRuning;

    public bool IsRuning
    {
        get => isRuning;
        set
        {
            isRuning = value;
            OnPropertyChanged();
            SettingPlot();
        }
    }

    public ObservableCollection<ScopeItemModel> ScopeList { get; set; } = [];

    public long Timewatch => _stopwatch != null ? _baseTime + _stopwatch.ElapsedMilliseconds : 0;

    public DataViewModel Data => ViewModelLocator.DataViewModel;

    /// <summary>
    ///     示波器设置参数列表
    ///     分组，参数名，索引值
    /// </summary>
    public ObservableCollection<Tuple<string, string, ushort>> ChannelParams { get; } = ScopeConst.ChannelParams;

    [ObservableProperty] private bool _isShowCrosshair;


    [ObservableProperty] private bool _isShowHorizontalSpan;

    [NotifyCanExecuteChangedFor(nameof(ShowSpansCommand))]
    [ObservableProperty]
    private bool _isShowVerticalSpan;

    /// <summary>
    /// 是否已导入数据
    /// </summary>
    [ObservableProperty] private bool _isDataImported;

    /// <summary>
    /// 轴0刹车信号计数
    /// </summary>
    [ObservableProperty] private uint _axis0StopSignalCount;

    /// <summary>
    /// 轴0加速信号计数
    /// </summary>
    [ObservableProperty] private uint _axis0AccSignalCount;

    /// <summary>
    /// 轴1刹车信号计数
    /// </summary>
    [ObservableProperty] private uint _axis1StopSignalCount;

    /// <summary>
    /// 轴1加速信号计数
    /// </summary>
    [ObservableProperty] private uint _axis1AccSignalCount;

    #endregion

    #region Function

    [RelayCommand]
    private async Task StartRun()
    {
        //OpenScope(false);
        //await Task.Delay(100);
        _scopeFeedbacks = [];
        OpenScope(true);
        // 如果不是第一次启动，需要保持时间连续性
        if (_stopwatch != null)
        {
            _baseTime = _previousTime;
        }
        else
        {
            _baseTime = 0;
            _previousTime = 0;
        }

        IsRuning = true;
        ReloadDataLogger();
        _stopwatch = new Stopwatch();
        _stopwatch.Start();
        _curveRefreshTimer.Start();
        await Task.Factory.StartNew(RealTimeRecordingData, TaskCreationOptions.LongRunning);
    }

    [RelayCommand]
    private async Task EndRun()
    {
        try
        {
            // 不重置_previousTime，保持时间连续性
            StopTimmers();
            OpenScope(false);
            await Task.Delay(1000); //等待串口数据解析完成
            
            // 创建一个副本，避免在处理过程中集合被修改
            _scopeFeedbacks = new LinkedList<ScopeChannelsFeedback>(ServoContext.Instance.ScopeFeedbacks);
            
            // 加载数据
            LoadCacheData();
            
            // 清空原始缓存
            ServoContext.Instance.ScopeFeedbacks.Clear();
            
            // 自动缩放
            AutoScale();
        }
        catch (Exception ex)
        {
            // 记录异常
            LogHelper.GetInstance().Error($"结束运行时发生错误: {ex.Message}");
            MsgToUiHelper.SendMsgError($"结束运行时发生错误: {ex.Message}");
        }
    }

    [RelayCommand]
    private void ClearData()
    {
        ScottPlot.Plot.Clear();
        IsShowCrosshair = false;
        IsShowHorizontalSpan = false;
        IsShowVerticalSpan = false;

        // 清理对象引用
        crosshair = null;
        hSpan = null;
        vSpan = null;
        hSpanText = null;
        vSpanText = null;
        snapPointMarker = null;
        isSnapping = false;

        // 重置时间相关变量，允许重新开始计时
        _previousTime = 0;
        _baseTime = 0;
        _stopwatch = null;

        // 重置导入状态
        IsDataImported = false;

        InitializeChannelsPlot();
        RefreshPlot();
    }

    [RelayCommand]
    private void AutoScale()
    {
        ScottPlot.Plot.Axes.AutoScale();
        RefreshPlot();
    }

    [RelayCommand]
    private void SetTick(string value)
    {
        int.TryParse(value, out _setTick);
        _setTick = _setTick > 0 && _setTick <= 300 ? _setTick : 2;
    }

    [RelayCommand]
    private void HideDataLogger()
    {
        foreach (var item in ScopeList) item.Plot.IsVisible = item.IsVisible;

        RefreshPlot();
    }

    [RelayCommand]
    private void SettingPlot()
    {
        //拖动时轴值是否跟着变化
        foreach (var item in ScopeList)
            if (item.Plot is DataLogger dataLogger)
            {
                dataLogger.ManageAxisLimits = IsRuning;
            }

        // disable mouse interaction if axis limits are managed automatically
        if (IsRuning)
        {
            ScottPlot.UserInputProcessor.IsEnabled = false;
        }
        else
        {
            // 当有Span显示时，禁用默认的用户输入处理，避免与拖拽冲突
            bool hasVisibleSpans = (IsShowHorizontalSpan && hSpan != null) ||
                                   (IsShowVerticalSpan && vSpan != null);
            ScottPlot.UserInputProcessor.IsEnabled = !hasVisibleSpans;
        }

        ScottPlot.Refresh();
    }

    [RelayCommand]
    private void RefreshPlot()
    {
        //this.Dispatcher.Invoke(scopePlot.Refresh); //刷新曲线
        ScottPlot?.Refresh();
    }

    [RelayCommand]
    private void Selected(ScopeItemModel? item)
    {
        if (item is null) return;
        foreach (var scope in ScopeList)
        {
            scope.IsSelected = false;
        }

        item.IsSelected = true;
    }

    /// <summary>
    /// 是否显示标注控件
    /// </summary>
    [RelayCommand]
    private void ShowSpans()
    {
        // 处理HorizontalSpan
        if (IsShowHorizontalSpan)
        {
            if (!ScottPlot.Plot.GetPlottables().Any(plottable => plottable is HorizontalSpan))
            {
                InitHorizontalSpan();
            }
            else
            {
                hSpan.IsVisible = true;
            }
        }
        else
        {
            // 取消勾选时，直接从Plot中移除对象
            if (hSpan != null)
            {
                ScottPlot.Plot.Remove(hSpan);
                hSpan = null;
            }

            if (hSpanText != null)
            {
                ScottPlot.Plot.Remove(hSpanText);
                hSpanText = null;
            }
        }

        // 处理VerticalSpan
        if (IsShowVerticalSpan)
        {
            if (!ScottPlot.Plot.GetPlottables().Any(plottable => plottable is VerticalSpan))
            {
                InitVerticalSpan();
            }
            else
            {
                vSpan.IsVisible = true;
            }
        }
        else
        {
            // 取消勾选时，直接从Plot中移除对象
            if (vSpan != null)
            {
                ScottPlot.Plot.Remove(vSpan);
                vSpan = null;
            }

            if (vSpanText != null)
            {
                ScottPlot.Plot.Remove(vSpanText);
                vSpanText = null;
            }
        }

        // 处理Crosshair
        if (IsShowCrosshair)
        {
            if (!ScottPlot.Plot.GetPlottables().Any(plottable => plottable is Crosshair))
            {
                InitCrosshair();
            }
            else
            {
                crosshair.IsVisible = true;
            }
        }
        else
        {
            // 取消勾选时，直接从Plot中移除对象
            if (crosshair != null)
            {
                ScottPlot.Plot.Remove(crosshair);
                crosshair = null;
            }

            // 同时移除吸附点标记
            if (snapPointMarker != null)
            {
                ScottPlot.Plot.Remove(snapPointMarker);
                snapPointMarker = null;
            }

            isSnapping = false;
        }

        // 更新UserInputProcessor状态
        SettingPlot();
        RefreshPlot();
    }

    [RelayCommand]
    private async Task ImportData()
    {
        // 创建一个文件对话框实例
        var openFileDialog = new OpenFileDialog();
        openFileDialog.Filter = Lang.ScopeView_xaml_Csv_file_filter;
        openFileDialog.Title = Lang.ScopeView_xaml_Select_csv_file;
        if (openFileDialog.ShowDialog() == DialogResult.OK)
        {
            var filePath = openFileDialog.FileName;
            try
            {
                ClearData();
                ClearAllChannels();
                var queryResult = await MiniExcel.QueryAsync<ChannelsCsvData>(filePath);

                // 使用ToList()确保查询结果被具体化，然后使用foreach避免并发修改异常
                var dataList = queryResult.ToList();
                foreach (var d in dataList)
                {
                    AddChannel(d.Label, DeserializeStringtoDoubleArray(d.Ys), d.SampleRate,
                        Color.FromHex(d.Color));
                }
                RefreshPlot();
                // 导入成功后设置状态
                IsDataImported = true;
            }
            catch (Exception ex)
            {
                MsgToUiHelper.SendMsgError($"数据导入失败：{ex.Message}");
            }
        }
    }

    [RelayCommand]
    private async Task ExportData()
    {
        var saveFileDialog = new SaveFileDialog();
        saveFileDialog.Filter = "CSV file (*.csv)|*.csv";
        saveFileDialog.Title = Lang.ScopeView_xaml_Select_save_path;
        saveFileDialog.FileName = $"scopeDatas_{DateTime.Now:yyyyMMdd_HHmmss}";
        if (saveFileDialog.ShowDialog() == DialogResult.OK)
            if (saveFileDialog.FileName != "")
            {
                var filePath = saveFileDialog.FileName;
                try
                {
                    var signals = ScottPlot.Plot.GetPlottables().Where(p => p is Signal).ToList();
                    var records = new List<ChannelsCsvData>();
                    foreach (Signal item in signals)
                    {
                        var record = new ChannelsCsvData
                        {
                            Label = item.LegendText,
                            Color = item.Color.ToHex(),
                            SampleRate = 1.0 / item.Data.Period,
                            Ys = SerializeDoubleArrayToString(item.Data.GetYs().ToArray())
                        };
                        records.Add(record);
                    }

                    await MiniExcel.SaveAsAsync(filePath, records, overwriteFile: true);
                    MsgToUiHelper.SendMsgInfo(Lang.ScopeView_xaml_Data_export_success);
                }
                catch (Exception ex)
                {
                    MsgToUiHelper.SendMsgError($"数据导出失败：{ex.Message}");
                }
            }
    }

    [RelayCommand]
    private void LoadErrRecord()
    {
        ClearAllChannels();
        ServoContext.Instance.ErrDataChannelDraw = (axis, errType, values) =>
            AddChannel($"{axis}-{(ThirdErrDataType)errType}", values);
        client.GetErrRecord();
        IsDataImported = true;
    }

    [RelayCommand]
    private void LoadProcessRecord()
    {
        ClearAllChannels();
        ServoContext.Instance.ProcessRecordChannelDraw = (axis, recordType, values) =>
            AddChannel($"{axis}-{(ThirdProcessRecordDataType)recordType}", values);
        client.GetProcessDbgRecord();
        IsDataImported = true;
    }

    [RelayCommand]
    private void LoadPlcCtrlSignalRecord()
    {
        ClearAllChannels();
        ServoContext.Instance.PlcCtrlSignalRecordChannelDraw = (axis, recordType, values) =>
        {
            double[] doubleValues = values.Select(b => (double)b).ToArray();
            AddChannel($"{axis}-{(ThirdPlcCtrlSignalRecordDataType)recordType}", doubleValues);
        };
        client.GetPlcCtrlSignalRecord();
        IsDataImported = true;
    }

    [RelayCommand]
    private void GetPlcCtrlSignalCount()
    {
        client.GetPlcCtrlSignalCount();
    }

    /// <summary>
    ///     采集示波器数据
    /// </summary>
    /// <param name="enable">是否开始采集</param>
    private void OpenScope(bool enable)
    {
        if (client.IsConnected)
        {
            var val = JsonConvert.DeserializeObject<SystemParamModel>(JsonConvert.SerializeObject(ServoContext.Instance.ParamsDic[ParamTableEnum.System]));
            val.ScopeCtl = (ushort)(enable ? 1 : 0);
            val.ScopeMapList0 = ScopeList.FirstOrDefault(x => x.Id == 0)?.ParamIndex ?? 0;
            val.ScopeMapList1 = ScopeList.FirstOrDefault(x => x.Id == 1)?.ParamIndex ?? 0;
            val.ScopeMapList2 = ScopeList.FirstOrDefault(x => x.Id == 2)?.ParamIndex ?? 0;
            val.ScopeMapList3 = ScopeList.FirstOrDefault(x => x.Id == 3)?.ParamIndex ?? 0;
            val.ScopeMapList4 = ScopeList.FirstOrDefault(x => x.Id == 4)?.ParamIndex ?? 0;
            val.ScopeMapList5 = ScopeList.FirstOrDefault(x => x.Id == 5)?.ParamIndex ?? 0;
            var config = new MapperConfiguration(cfg => cfg.CreateMap<SystemParamModel, SystemParamPackage>());
            LogHelper.GetInstance().Info("Scope Write: "+JsonConvert.SerializeObject(val));
            var mapper = config.CreateMapper();
            //发送采集指令
            var package = mapper.Map<SystemParamPackage>(val);
            package.AxisSelect = Data.SelectedAxis;
            client.Send(package);
            LogHelper.GetInstance().Info("Scope Package: "+JsonConvert.SerializeObject(package));
        }
    }

    private void UserControl_Unloaded(object sender, RoutedEventArgs e)
    {
        OpenScope(false);
        StopTimmers();

        // 取消注册消息监听
        WeakReferenceMessenger.Default.Unregister<ValueChangedMessage<bool>, string>(this, "ServoSerialPort");
        WeakReferenceMessenger.Default.Unregister<ValueChangedMessage<PlcCtrlSignalCountDto>, string>(this,
            "PlcCtrlSignalCount"); // 
    }

    /// <summary>
    /// 处理串口连接状态变化事件
    /// </summary>
    /// <param name="recipient">接收者</param>
    /// <param name="message">消息</param>
    private void OnServoConnectionChanged(object recipient, ValueChangedMessage<bool> message)
    {
        // 当串口断开时（IsConnected = false），自动停止示波器
        if (!message.Value && IsRuning)
        {
            // 在UI线程中执行停止操作
            Dispatcher.BeginInvoke(new Action(async () =>
            {
                await EndRun();
                MsgToUiHelper.SendMsgWarn(Lang.ScopeView_xaml_Serial_port_disconnected_oscilloscope_auto_stopped);
            }));
        }
    }

    /// <summary>
    ///     给定当前曲线点时间戳
    /// 并通过开一线程从ServoContext取变量，实时缓存数据
    /// </summary>
    private void RealTimeRecordingData()
    {
        while (IsRuning)
        {
            var time = Timewatch; //防止循环内的时间不一致
            if (time < _previousTime + _setTick)
                continue;
            _feedback = ServoContext.Instance.ScopeFeedback;
            _feedback.Time = time;
            // _scopeFeedbacks.AddLast(_feedback);
            _previousTime = time;
        }
    }

    /// <summary>
    /// 给定当前曲线点时间戳
    /// 客户端接收到示波器数据后通过调用委托，实现数据缓存
    /// </summary>
    /// <param name="feedback"></param>
    private void ScopeReceivedHandle(ScopeChannelsFeedback feedback)
    {
        var time = Timewatch; //防止循环内的时间不一致
        if (time < _previousTime + _setTick)
            return;
        _feedback = feedback;
        _feedback.Time = time;
        _scopeFeedbacks.AddLast(feedback);
        _previousTime = time;
    }

    /// <summary>
    ///     实时绘制数据
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void RealTimeDrawingChannels(object sender, EventArgs e)
    {
        if (!IsRuning) return;
        
        // 获取当前时间戳，确保它大于之前添加的所有点的时间戳
        var currentTime = Timewatch;
        if (currentTime <= _previousTime)
        {
            return;
        }
        
        foreach (var item in ScopeList)
        {
            item.Value = item.Id switch
            {
                0 => _feedback.Channel0,
                1 => _feedback.Channel1,
                2 => _feedback.Channel2,
                3 => _feedback.Channel3,
                4 => _feedback.Channel4,
                5 => _feedback.Channel5,
                _ => 0
            };
            
            try
            {
                (item.Plot as DataLogger)?.Add(currentTime, item.Value);
            }
            catch (ArgumentException ex)
            {
                // 记录错误但不中断程序
                LogHelper.GetInstance().Error($"添加数据点失败: {ex.Message}, 时间: {currentTime}, 上一时间: {_previousTime}");
            }
        }
        
        // 更新上一次添加点的时间戳
        _previousTime = currentTime;
        
        RefreshPlot();
    }

    /// <summary>
    ///     初始化示波器曲线、刷新定时器、数据缓存处理
    /// </summary>
    private void InitializeChannelsPlot()
    {
        if (ScopeList.Count > 0) ScopeList.Clear();

        //初始化曲线数据，一共6个通道
        for (var i = 0; i < 6; i++)
        {
            var dataLogger = ScottPlot.Plot.Add.DataLogger();
            dataLogger.LegendText = ChannelParams[i].Item2;
            dataLogger.ManageAxisLimits = false;
            dataLogger.ViewJump();
            var item = new ScopeItemModel("Channel" + i)
            {
                Plot = dataLogger,
                Id = i,
                Scale = 1,
                IsVisible = true,
                ParamIndex = ScopeConst.ChannelParams[i].Item3
            };
            ScopeList.Add(item);
            item.Color = dataLogger.Color;
        }

        #region 设置图表

        ScottPlot.Plot.ShowLegend(); //显示右侧图例
        ScottPlot.Plot.Font.Automatic(); // 设置支持中文的字体
        ScottPlot.Plot.Axes.Bottom.Min = 0; //设置X轴最小值

        #endregion

        //曲线刷新定时器
        _curveRefreshTimer.Tick += RealTimeDrawingChannels;
        _curveRefreshTimer.Interval = new TimeSpan(0, 0, 0, 0, 20);

        //数据缓存委托
        // ServoContext.Instance.ScopeReceived = ScopeReceivedHandle;

        RefreshPlot();
    }

    /// <summary>
    ///     加载缓存的数据
    /// </summary>
    private void LoadCacheData()
    {
        if (_scopeFeedbacks == null || _scopeFeedbacks.Count == 0) return;
        
        // 先将LinkedList转换为数组，避免在枚举过程中修改集合
        var feedbacksArray = _scopeFeedbacks.ToArray();
        
        // 清除所有DataLogger类型的图表
        ScottPlot.Plot.Clear<DataLogger>();
        
        foreach (var item in ScopeList)
        {
            try
            {
                // 根据通道ID获取对应的数据
                double[] values;
                switch (item.Id)
                {
                    case 0:
                        values = feedbacksArray.Select(fk => Convert.ToDouble(fk.Channel0)).ToArray();
                        break;
                    case 1:
                        values = feedbacksArray.Select(fk => Convert.ToDouble(fk.Channel1)).ToArray();
                        break;
                    case 2:
                        values = feedbacksArray.Select(fk => Convert.ToDouble(fk.Channel2)).ToArray();
                        break;
                    case 3:
                        values = feedbacksArray.Select(fk => Convert.ToDouble(fk.Channel3)).ToArray();
                        break;
                    case 4:
                        values = feedbacksArray.Select(fk => Convert.ToDouble(fk.Channel4)).ToArray();
                        break;
                    case 5:
                        values = feedbacksArray.Select(fk => Convert.ToDouble(fk.Channel5)).ToArray();
                        break;
                    default:
                        LogHelper.GetInstance().Error($"无效的通道ID: {item.Id}");
                        continue; // 跳过无效的通道
                }
                
                // 添加信号图表
                var signal = ScottPlot.Plot.Add.Signal(values, Math.Round((double)1 / _setTick, 2));
                signal.Color = item.Color;
                signal.IsVisible = item.IsVisible;
                signal.LegendText = ChannelParams.FirstOrDefault(p => p.Item3 == item.ParamIndex)?.Item2 ?? "";
                item.Plot = signal;
            }
            catch (Exception ex)
            {
                // 记录异常但不中断处理
                LogHelper.GetInstance().Error($"加载通道 {item.Id} 数据失败: {ex.Message}");
            }
        }
    }

    private void ReloadDataLogger()
    {
        ScottPlot.Plot.Clear<Signal>();
        for (int i = 0; i < ScopeList.Count; i++)
        {
            var item = ScopeList[i];
            if (!ScottPlot.Plot.GetPlottables().ToList().Contains(item.Plot))
            {
                var dataLogger = ScottPlot.Plot.Add.DataLogger();
                dataLogger.LegendText = ChannelParams[i].Item2;
                dataLogger.ManageAxisLimits = false;
                dataLogger.Color = item.Color;
                dataLogger.Data.YOffset = item.Offset;
                dataLogger.Data.YScale = item.Scale;
                //dataLogger.IsVisible = item.IsVisible;
                dataLogger.ViewJump();
                item.Plot = dataLogger;
            }
        }

        // 确保图例字体支持中文显示
        //ScottPlot.Plot.Font.Automatic();
        RefreshPlot();
    }

    /// <summary>
    ///     结束定时器和任务
    /// </summary>
    private void StopTimmers()
    {
        IsRuning = false;
        _stopwatch?.Stop();
        _curveRefreshTimer.Stop();
    }

    /// <summary>
    ///     初始化十字光标
    /// </summary>
    private void InitCrosshair()
    {
        if (ScottPlot.Plot.GetPlottables().Any(plottable => plottable is Crosshair))
        {
            return;
        }

        // 使用ScottPlot 5.0推荐的方式创建Crosshair
        crosshair = ScottPlot.Plot.Add.Crosshair(0, 0);
        crosshair.IsVisible = IsShowCrosshair; // 直接绑定到属性
        crosshair.LineColor = Colors.Red;
        crosshair.LineWidth = 1;
        crosshair.MarkerShape = MarkerShape.OpenCircle;
        crosshair.MarkerSize = 15;

        crosshair.TextColor = Colors.White;
        crosshair.TextBackgroundColor = crosshair.HorizontalLine.Color;

        // 设置线条样式以提高可见性
        crosshair.HorizontalLine.LinePattern = LinePattern.Solid;
        crosshair.VerticalLine.LinePattern = LinePattern.Solid;

        // 初始化吸附点高亮标记
        if (snapPointMarker == null)
        {
            snapPointMarker = ScottPlot.Plot.Add.Marker(0, 0);
            snapPointMarker.Color = Colors.LightGreen;
            snapPointMarker.Size = 6;
            snapPointMarker.Shape = MarkerShape.FilledCircle;
            snapPointMarker.IsVisible = false;
        }

        if (ScopeList?.Count > 0)
            ScopeList.First().IsSelected = true; //默认选中第一个通道
    }

    private void InitHorizontalSpan()
    {
        if (ScottPlot.Plot.GetPlottables().Any(plottable => plottable is HorizontalSpan))
        {
            return;
        }

        // 使用当前图表的X轴范围来设置初始位置
        var axisLimits = ScottPlot.Plot.Axes.GetLimits();
        var left = axisLimits.Left + (axisLimits.Right - axisLimits.Left) * 0.3;
        var right = axisLimits.Left + (axisLimits.Right - axisLimits.Left) * 0.7;

        hSpan = ScottPlot.Plot.Add.HorizontalSpan(left, right);
        hSpan.IsDraggable = true;
        hSpan.IsResizable = true;
        hSpan.IsVisible = IsShowHorizontalSpan;

        // 添加文本显示span数值
        if (hSpanText == null)
        {
            hSpanText = ScottPlot.Plot.Add.Text("", 0, 0);
            hSpanText.LabelFontColor = Colors.Green;
            hSpanText.LabelBackgroundColor = Color.FromHex("#80000000"); // 半透明黑色背景
            hSpanText.LabelFontSize = 12;
            hSpanText.LabelAlignment = Alignment.UpperRight;
        }

        UpdateHorizontalSpanText();

        ScottPlot.Refresh();
    }

    private void InitVerticalSpan()
    {
        if (ScottPlot.Plot.GetPlottables().Any(plottable => plottable is VerticalSpan))
        {
            return;
        }

        // 使用当前图表的Y轴范围来设置初始位置
        var axisLimits = ScottPlot.Plot.Axes.GetLimits();
        var bottom = axisLimits.Bottom + (axisLimits.Top - axisLimits.Bottom) * 0.3;
        var top = axisLimits.Bottom + (axisLimits.Top - axisLimits.Bottom) * 0.7;

        vSpan = ScottPlot.Plot.Add.VerticalSpan(bottom, top);
        vSpan.IsDraggable = true;
        vSpan.IsResizable = true;
        vSpan.IsVisible = IsShowVerticalSpan;

        // 添加文本显示span数值
        if (vSpanText == null)
        {
            vSpanText = ScottPlot.Plot.Add.Text("", 0, 0);
            vSpanText.LabelFontColor = Colors.Red;
            vSpanText.LabelBackgroundColor = Color.FromHex("#80000000"); // 半透明黑色背景
            vSpanText.LabelFontSize = 12;
            vSpanText.LabelAlignment = Alignment.UpperRight;
        }

        UpdateVerticalSpanText();

        ScottPlot.Refresh();
    }

    private void ClearAllChannels()
    {
        channnelIndex = 0;
        if (ScopeList.Count > 0)
        {
            ScopeList.Clear();
            ScottPlot.Plot.Clear();
        }

        RefreshPlot();
    }

    private int channnelIndex;

    private void AddChannel(string name, double[] values, double rate = 1, Color? color = null)
    {
        Dispatcher.BeginInvoke(() =>
        {
            var channelPlot = ScottPlot.Plot.Add.Signal(values, rate);
            if (color.HasValue)
                channelPlot.Color = color.Value;
            channelPlot.LegendText = name;
            var item = new ScopeItemModel(name)
            {
                Plot = channelPlot,
                Id = channnelIndex,
                Scale = 1,
                IsVisible = true,
                Color = channelPlot.Color
            };
            ScopeList.Add(item);
            channnelIndex++;
            ScottPlot.Plot.Axes.AutoScale();
            RefreshPlot();
        });
    }

    #region 标注功能

    AxisSpanUnderMouse? SpanBeingDragged = null;
    Pixel mousePixel = new(0, 0);
    Coordinates mouseLocation = new(0, 0);

    /// <summary>
    /// 初始化按键事件绑定
    /// </summary>
    private void InitKeyBind()
    {
        ScottPlot.MouseDown += (s, e) =>
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                // 实时获取鼠标位置
                var localMousePoint = Mouse.GetPosition(ScottPlot);
                var currentMousePixel = new Pixel(localMousePoint.X, localMousePoint.Y);
                mousePixel = currentMousePixel;
                mouseLocation = ScottPlot.Plot.GetCoordinates(mousePixel);

                // Crosshair的初始化现在统一在ShowSpans方法中处理

                if (IsShowHorizontalSpan || IsShowVerticalSpan)
                {
                    var thingUnderMouse = GetSpanUnderMouse(currentMousePixel);
                    if (thingUnderMouse is not null)
                    {
                        SpanBeingDragged = thingUnderMouse;
                        ScottPlot.UserInputProcessor.Disable();
                    }
                }
            }
        };

        ScottPlot.MouseUp += (s, e) =>
        {
            SpanBeingDragged = null;
            ScottPlot.UserInputProcessor.Enable(); // enable panning

            // 隐藏吸附点标记
            if (snapPointMarker != null)
            {
                snapPointMarker.IsVisible = false;
            }

            isSnapping = false;

            ScottPlot.Refresh();
        };

        ScottPlot.MouseMove += (s, e) =>
        {
            var localMousePoint = Mouse.GetPosition(ScottPlot);
            mousePixel = new(localMousePoint.X, localMousePoint.Y);
            mouseLocation = ScottPlot.Plot.GetCoordinates(mousePixel);
            // 更新Crosshair位置
            if (IsShowCrosshair && crosshair != null)
            {
                if (isRuning) return;

                Coordinates targetPosition = mouseLocation;
                bool hasSnapPoint = false;

                // 检查是否有数据点可以吸附
                if (FindNearestDataPoint(mouseLocation, out Coordinates snapPoint))
                {
                    targetPosition = snapPoint;
                    hasSnapPoint = true;
                    isSnapping = true;

                    // 确保吸附点标记已初始化
                    if (snapPointMarker == null)
                    {
                        snapPointMarker = ScottPlot.Plot.Add.Marker(0, 0);
                        snapPointMarker.Color = Colors.OrangeRed;
                        snapPointMarker.Size = 8;
                        snapPointMarker.Shape = MarkerShape.FilledCircle;
                    }

                    // 显示吸附点高亮
                    snapPointMarker.X = snapPoint.X;
                    snapPointMarker.Y = snapPoint.Y;
                    snapPointMarker.IsVisible = true;
                }
                else
                {
                    isSnapping = false;
                    // 隐藏吸附点高亮
                    if (snapPointMarker != null)
                    {
                        snapPointMarker.IsVisible = false;
                    }
                }

                crosshair.Position = targetPosition;
                crosshair.IsVisible = true;

                // 更新文本显示，如果有吸附点则显示吸附标识
                string xText = hasSnapPoint ? $"{targetPosition.X:N0} [SNAP]" : $"{targetPosition.X:N0}";
                string yText = hasSnapPoint ? $"{targetPosition.Y:N0} [SNAP]" : $"{targetPosition.Y:N0}";
                crosshair.VerticalLine.Text = xText;
                crosshair.HorizontalLine.Text = yText;

                ScottPlot.Refresh();
            }

            // 更新HorizontalSpan和VerticalSpan位置
            if ((IsShowHorizontalSpan && hSpan != null) || (IsShowVerticalSpan && vSpan != null))
            {
                if (SpanBeingDragged is not null)
                {
                    Coordinates targetLocation = mouseLocation;

                    // 为Span添加吸附功能
                    if (FindNearestDataPoint(mouseLocation, out Coordinates snapPoint))
                    {
                        // 根据Span类型决定吸附方向
                        if (SpanBeingDragged.Span is HorizontalSpan)
                        {
                            // HorizontalSpan只在X轴方向吸附
                            targetLocation = new Coordinates(snapPoint.X, mouseLocation.Y);
                        }
                        else if (SpanBeingDragged.Span is VerticalSpan)
                        {
                            // VerticalSpan只在Y轴方向吸附
                            targetLocation = new Coordinates(mouseLocation.X, snapPoint.Y);
                        }

                        // 确保吸附点标记已初始化
                        if (snapPointMarker == null)
                        {
                            snapPointMarker = ScottPlot.Plot.Add.Marker(0, 0);
                            snapPointMarker.Color = Colors.OrangeRed;
                            snapPointMarker.Size = 8;
                            snapPointMarker.Shape = MarkerShape.FilledCircle;
                        }

                        // 显示吸附点高亮
                        snapPointMarker.X = snapPoint.X;
                        snapPointMarker.Y = snapPoint.Y;
                        snapPointMarker.IsVisible = true;
                        isSnapping = true;
                    }
                    else
                    {
                        // 隐藏吸附点高亮
                        if (snapPointMarker != null)
                        {
                            snapPointMarker.IsVisible = false;
                        }

                        isSnapping = false;
                    }

                    SpanBeingDragged.DragTo(targetLocation);

                    // 更新文本显示
                    if (IsShowHorizontalSpan && hSpan != null)
                        UpdateHorizontalSpanText();
                    if (IsShowVerticalSpan && vSpan != null)
                        UpdateVerticalSpanText();

                    ScottPlot.Refresh();
                }
                else
                {
                    // 确保使用当前更新的mousePixel
                    var spanUnderMouse = GetSpanUnderMouse(mousePixel);
                    if (spanUnderMouse is null) Cursor = Cursors.Arrow;
                    else if (spanUnderMouse.IsResizingHorizontally) Cursor = Cursors.SizeWE;
                    else if (spanUnderMouse.IsResizingVertically) Cursor = Cursors.SizeNS;
                    else if (spanUnderMouse.IsMoving) Cursor = Cursors.SizeAll;
                }
            }
        };

        AxisSpanUnderMouse? GetSpanUnderMouse(Pixel mousePixel)
        {
            CoordinateRect rect = ScottPlot.Plot.GetCoordinateRect(mousePixel.X, mousePixel.Y, radius: 10);

            foreach (AxisSpan span in ScottPlot.Plot.GetPlottables<AxisSpan>().ToList().AsEnumerable().Reverse())
            {
                AxisSpanUnderMouse? spanUnderMouse = span.UnderMouse(rect);
                if (spanUnderMouse is not null)
                    return spanUnderMouse;
            }

            return null;
        }
    }

    /// <summary>
    /// 检查是否可以拖拽，数据刷新时不可
    /// </summary>
    public void CheckCanTagging()
    {
        var hasSigalPlot = ScottPlot.Plot.GetPlottables().Any(plottable => plottable is Signal);
        if (!hasSigalPlot)
        {
            IsShowCrosshair = false;
            IsShowHorizontalSpan = false;
            IsShowVerticalSpan = false;
        }

        RefreshPlot();
    }

    /// <summary>
    /// 查找最近的数据点用于吸附
    /// </summary>
    /// <param name="mouseCoordinates">鼠标坐标</param>
    /// <param name="snapPoint">输出吸附点坐标</param>
    /// <returns>是否找到吸附点</returns>
    private bool FindNearestDataPoint(Coordinates mouseCoordinates, out Coordinates snapPoint)
    {
        snapPoint = mouseCoordinates;
        double minDistance = double.MaxValue;
        bool foundPoint = false;

        // 遍历所有可见的Signal图形，使用ToList()避免集合修改异常
        var visibleSignalItems = ScopeList.Where(item => item.IsVisible && item.Plot is Signal).ToList();
        foreach (var scopeItem in visibleSignalItems)
        {
            if (scopeItem.Plot is not Signal signal) continue;

            try
            {
                // 获取最近的数据点
                var nearestPoint = signal.GetNearest(mouseCoordinates, ScottPlot.Plot.LastRender);
                if (nearestPoint.IsReal)
                {
                    // 计算鼠标位置与数据点的像素距离
                    var pointPixel = ScottPlot.Plot.GetPixel(nearestPoint.Coordinates);
                    var mousePixel = ScottPlot.Plot.GetPixel(mouseCoordinates);
                    var distance = Math.Sqrt(Math.Pow(pointPixel.X - mousePixel.X, 2) +
                                             Math.Pow(pointPixel.Y - mousePixel.Y, 2));

                    // 如果距离在吸附范围内且是最近的点
                    if (distance < SNAP_DISTANCE && distance < minDistance)
                    {
                        minDistance = distance;
                        snapPoint = nearestPoint.Coordinates;
                        foundPoint = true;
                    }
                }
            }
            catch
            {
                // 忽略获取数据点时的异常
                continue;
            }
        }

        return foundPoint;
    }

    partial void OnIsShowHorizontalSpanChanged(bool value)
    {
        // 统一通过ShowSpans方法处理对象的创建和销毁
        ShowSpans();
    }

    partial void OnIsShowVerticalSpanChanged(bool value)
    {
        // 统一通过ShowSpans方法处理对象的创建和销毁
        ShowSpans();
    }

    /// <summary>
    /// 更新HorizontalSpan文本显示
    /// </summary>
    private void UpdateHorizontalSpanText()
    {
        if (hSpan != null && hSpanText != null && IsShowHorizontalSpan)
        {
            var axisLimits = ScottPlot.Plot.Axes.GetLimits();
            var range = Math.Abs(hSpan.X2 - hSpan.X1);
            var snapIndicator = isSnapping ? " [SNAP]" : "";
            var text = $"X-axis range{snapIndicator}:\nLeft: {hSpan.X1:F0}\nRight: {hSpan.X2:F0}\nRange: {range:F0}";

            hSpanText.LabelText = text;
            // 将文本放置在左上角并右移
            // 定义一个偏移量，可根据实际情况调整
            double rightShiftOffset = 0.08;
            // var x = axisLimits.Left + (axisLimits.Right - axisLimits.Left) * (0.02 + rightShiftOffset);
            var y = axisLimits.Top - (axisLimits.Top - axisLimits.Bottom) * 0.02;
            hSpanText.Location = new(hSpan.X1 - 1, y);

            hSpanText.IsVisible = true;
        }
        else if (hSpanText != null)
        {
            hSpanText.IsVisible = false;
        }
    }

    /// <summary>
    /// 更新VerticalSpan文本显示
    /// </summary>
    private void UpdateVerticalSpanText()
    {
        if (vSpan != null && vSpanText != null && IsShowVerticalSpan)
        {
            var axisLimits = ScottPlot.Plot.Axes.GetLimits();
            var range = Math.Abs(vSpan.Y2 - vSpan.Y1);
            var snapIndicator = isSnapping ? " [SNAP]" : "";
            var text = $"Y-axis range{snapIndicator}:\nBottom: {vSpan.Y1:F0}\nTop: {vSpan.Y2:F0}\nRange: {range:F0}";

            vSpanText.LabelText = text;
            // 将文本放置在右上角，如果HorizontalSpan也显示，则向下偏移
            var yOffset = IsShowHorizontalSpan ? 0.15 : 0.02;
            var x = axisLimits.Right - (axisLimits.Right - axisLimits.Left) * 0.02;
            //var y = axisLimits.Top - (axisLimits.Top - axisLimits.Bottom) * yOffset;
            vSpanText.Location = new(x, vSpan.Y2);
            vSpanText.IsVisible = true;
        }
        else if (vSpanText != null)
        {
            vSpanText.IsVisible = false;
        }
    }

    #endregion


    /// <summary>
    /// 处理PLC控制信号计数数据接收事件
    /// </summary>
    /// <param name="recipient">接收者</param>
    /// <param name="message">消息</param>
    private void OnPlcCtrlSignalCountReceived(object recipient, ValueChangedMessage<PlcCtrlSignalCountDto> message)
    {
        var dto = message.Value;

        // 在UI线程中更新属性
        Dispatcher.BeginInvoke(new Action(() =>
        {
            // 根据轴号更新对应的计数
            if (dto.RecordAxis == 0)
            {
                Axis0StopSignalCount = dto.StopSignalCount;
                Axis0AccSignalCount = dto.AccSignalCount;
            }
            else if (dto.RecordAxis == 1)
            {
                Axis1StopSignalCount = dto.StopSignalCount;
                Axis1AccSignalCount = dto.AccSignalCount;
            }
        }));
    }

    #endregion
}