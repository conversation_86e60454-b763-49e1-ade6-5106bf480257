using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using System.Windows.Threading;

namespace ModularAT.Views;

/// <summary>
///     SimulationView.xaml 的交互逻辑
/// </summary>
public partial class SimulationView : UserControl
{
    // 定义显示和隐藏的命令值
    private const int SW_HIDE = 0;
    private const int SW_SHOW = 5;
    private const int WM_ACTIVATE = 0x0006;
    private readonly IntPtr WA_ACTIVE = new(1);
    private readonly IntPtr WA_INACTIVE = new(0);

    private float count = 0;

    private DispatcherTimer dispatcherTimer;

    private bool isU3DLoaded;

    private Process process;
    private Point u3dLeftUpPos;
    private IntPtr unityHWND = IntPtr.Zero;

    public SimulationView()
    {
        InitializeComponent();
        Loaded += (sender, e) => LoadUnity();
        SizeChanged += Window_SizeChanged;
        Unloaded += HideUnity;
        unityPanel.MouseEnter += Panel_MouseEnter;
    }

    [DllImport("User32.dll")]
    private static extern bool MoveWindow(IntPtr handle, int x, int y, int width, int height, bool redraw);
    //改变指定窗口的位置和尺寸，基于左上角（屏幕/父窗口）（指定窗口的句柄，窗口左位置，窗口顶位置，窗口新宽度，窗口新高度，指定是否重画窗口）

    [DllImport("user32.dll", SetLastError = true)]
    [return: MarshalAs(UnmanagedType.Bool)]
    private static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int X, int Y, int cx, int cy,
        uint uFlags);

    [DllImport("user32.dll")]
    internal static extern bool EnumChildWindows(IntPtr hwnd, WindowEnumProc func, IntPtr lParam);
    //枚举一个父窗口的所有子窗口（父窗口句柄，回调函数的地址，自定义的参数）

    [DllImport("user32.dll")]
    private static extern int SendMessage(IntPtr hWnd, int msg, IntPtr wParam, IntPtr lParam);
    //该函数将指定的消息发送到一个或多个窗口。此函数为指定的窗口调用窗口程序，直到窗口程序处理完消息再返回。（窗口句柄。窗口可以是任何类型的屏幕对象,用于区别其他消息的常量值,通常是一个与消息有关的常量值，也可能是窗口或控件的句柄,通常是一个指向内存中数据的指针）

    // 导入ShowWindow函数
    [DllImport("user32.dll")]
    private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);


    /// <summary>
    ///     窗体大小改变事件
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void Window_SizeChanged(object sender, SizeChangedEventArgs e)
    {
        ResizeU3D();
    }

    /// <summary>
    ///     隐藏Unity窗体
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void HideUnity(object sender, EventArgs e)
    {
        try
        {
            if (isU3DLoaded)
            {
                ShowWindow(unityHWND, SW_HIDE);
                dispatcherTimer.Stop();
            }
        }
        catch (Exception)
        {
        }
    }

    // 重点：需要wpf程序触发事件
    private void Panel_MouseEnter(object sender, MouseEventArgs e)
    {
        ActivateUnityWindow();
    }

    internal delegate int WindowEnumProc(IntPtr hwnd, IntPtr lparam);

    #region 窗体位置坐标变换

    public class DPIUtils
    {
        public static double DPIX { get; private set; } = 1.0;

        public static double DPIY { get; private set; } = 1.0;

        public static void Init(Visual visual)
        {
            var transformToDevice = PresentationSource.FromVisual(visual).CompositionTarget.TransformToDevice;
            DPIX = transformToDevice.M11;
            DPIY = transformToDevice.M22;
        }

        public static Point DivideByDPI(Point p)
        {
            return new Point(p.X / DPIX, p.Y / DPIY);
        }

        public static Rect DivideByDPI(Rect r)
        {
            return new Rect(r.Left / DPIX, r.Top / DPIY, r.Width, r.Height);
        }
    }

    #endregion

    #region Unity操作

    private void LoadUnity()
    {
        try
        {
            var panel = FindName("unityPanel") as Border;
            if (isU3DLoaded)
            {
                dispatcherTimer.Start();
                ShowWindow(unityHWND, SW_SHOW);
                return;
            }

            var hwnd = ((HwndSource)PresentationSource.FromVisual(panel)).Handle;

            foreach (var existingProcess in Process.GetProcessesByName("ModularATSimulation"))
            {
                existingProcess.Kill();
                existingProcess.WaitForExit(); // 确保进程已完全退出
            }

            process = new Process();

            var appStartupPath = Path.GetDirectoryName(Process.GetCurrentProcess().MainModule.FileName);
            process.StartInfo.FileName = Environment.CurrentDirectory + @"\simulation\ModularATSimulation.exe";
            process.StartInfo.Arguments = "-parentHWND " + hwnd.ToInt32() + " " + Environment.CommandLine;
            process.StartInfo.UseShellExecute = true;
            process.StartInfo.CreateNoWindow = true;

            process.Start();
            process.WaitForInputIdle();
            isU3DLoaded = true;
            EnumChildWindows(hwnd, WindowEnum, IntPtr.Zero);

            dispatcherTimer = new DispatcherTimer();
            dispatcherTimer.Tick += InitialResize;
            dispatcherTimer.Interval = new TimeSpan(0, 0, 0, 0, 200);
            dispatcherTimer.Start();
        }
        catch (Exception ex)
        {
            var error = ex.Message;
        }
    }

    private void InitialResize(object sender, EventArgs e)
    {
        ResizeU3D();
        dispatcherTimer.Stop();
    }

    private int WindowEnum(IntPtr hwnd, IntPtr lparam)
    {
        unityHWND = hwnd;
        ActivateUnityWindow();
        return 0;
    }

    private void ActivateUnityWindow()
    {
        SendMessage(unityHWND, WM_ACTIVATE, WA_ACTIVE, IntPtr.Zero);
    }

    private void ResizeU3D()
    {
        if (isU3DLoaded)
        {
            var panel = FindName("unityPanel") as Border;

            var window = Window.GetWindow(this);
            u3dLeftUpPos = panel.TransformToAncestor(window).Transform(new Point(0, 0));
            DPIUtils.Init(this);
            u3dLeftUpPos.X *= DPIUtils.DPIX;
            u3dLeftUpPos.Y *= DPIUtils.DPIY;
            MoveWindow(unityHWND, (int)u3dLeftUpPos.X, (int)u3dLeftUpPos.Y, (int)(panel.ActualWidth * DPIUtils.DPIX),
                (int)(panel.ActualHeight * DPIUtils.DPIY), true);
            ActivateUnityWindow();
        }
    }

    #endregion
}